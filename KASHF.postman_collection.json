{"info": {"_postman_id": "7ac0789a-ddc2-4bff-8e7e-fc7e69944219", "name": "KASHF", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "auth", "item": [{"name": "login", "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+201222222222\",\n    \"firebaseToken\": \"11111111\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "pre login", "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+201222222222\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/pre-login", "host": ["{{baseUrl}}"], "path": ["auth", "pre-login"]}}, "response": []}, {"name": "admin login", "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"string\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/admin/login", "host": ["{{baseUrl}}"], "path": ["auth", "admin", "login"]}}, "response": []}, {"name": "logout", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "response": []}, {"name": "forget password", "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+201111111111\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/forget-password", "host": ["{{baseUrl}}"], "path": ["auth", "forget-password"]}}, "response": []}, {"name": "send email verification", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"admin 2\",\r\n    \"phone\": \"+201273604088\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"A1234567890\",\r\n    \"role\": \"ADMIN\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/email-confirmation", "host": ["{{baseUrl}}"], "path": ["auth", "email-confirmation"]}}, "response": []}, {"name": "update password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"password\": \"1111\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/password", "host": ["{{baseUrl}}"], "path": ["auth", "password"]}}, "response": []}, {"name": "update fcm token", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"fcmToken\": \"test 2333214\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/fcm-token", "host": ["{{baseUrl}}"], "path": ["auth", "fcm-token"]}}, "response": []}, {"name": "verify", "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+201111111111\",\n    \"code\": \"6234\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/code-verification", "host": ["{{baseUrl}}"], "path": ["auth", "code-verification"]}}, "response": []}, {"name": "me", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}}, "response": []}, {"name": "refresh", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \n        \"email\": \"<EMAIL>\",\n        \"refreshToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.CFpRIbJ3nfWIJoNgFnFsXkyUhhICNYz3D2vVDCuzEp4\"\n\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}}, "response": []}, {"name": "guest login", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/guest-login", "host": ["{{baseUrl}}"], "path": ["auth", "guest-login"]}}, "response": []}, {"name": "login Copy", "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+201222222222\",\n    \"password\": \"11111111\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": []}]}, {"name": "user", "item": [{"name": "profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}}, "response": []}, {"name": "change passwrod", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"oldPassword\": \"1111\",\r\n    \"password\": \"11111\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/password", "host": ["{{baseUrl}}"], "path": ["users", "password"]}}, "response": []}, {"name": "list roles", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/users/roles", "host": ["{{baseUrl}}"], "path": ["users", "roles"]}}, "response": []}, {"name": "create admin", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"admin 2\",\r\n    \"phone\": \"+201273604087\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"A1234567890\",\r\n    \"role\": \"ADMIN\",\r\n    \"accessibility\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/admins", "host": ["{{baseUrl}}"], "path": ["users", "admins"]}}, "response": []}, {"name": "list admins", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"admin 2\",\r\n    \"phone\": \"+201273604088\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"A1234567890\",\r\n    \"role\": \"ADMIN\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/admins", "host": ["{{baseUrl}}"], "path": ["users", "admins"]}}, "response": []}, {"name": "view admins", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/admins/287", "host": ["{{baseUrl}}"], "path": ["users", "admins", "287"]}}, "response": []}, {"name": "update admin", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\r\n    \"isActive\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/admins/10", "host": ["{{baseUrl}}"], "path": ["users", "admins", "10"]}}, "response": []}, {"name": "delete admin", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/admins/10", "host": ["{{baseUrl}}"], "path": ["users", "admins", "10"]}}, "response": []}]}, {"name": "patients", "item": [{"name": "favourite", "item": [{"name": "add or remove", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"doctorId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/patients/favourite-doctors", "host": ["{{baseUrl}}"], "path": ["patients", "favourite-doctors"]}}, "response": []}, {"name": "get user favourites", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ratedUserId\": 1,\r\n    \"rating\": 2.2\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/patients/favourite-doctors", "host": ["{{baseUrl}}"], "path": ["patients", "favourite-doctors"]}}, "response": []}]}, {"name": "create patient", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"string\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+201092146492\",\n    \"gender\": \"Male\",\n    \"dateOfBirth\": \"1993-09-22\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/patients", "host": ["{{baseUrl}}"], "path": ["patients"]}}, "response": []}, {"name": "update patient", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"test\",\n    \"gender\": \"Male\",\n    \"dateOfBirth\": \"1997-07-19T12:00:00.000Z\",\n    \"firebaseUserId\": \"123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/patients/1", "host": ["{{baseUrl}}"], "path": ["patients", "1"]}}, "response": []}, {"name": "get patient", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/patients/7", "host": ["{{baseUrl}}"], "path": ["patients", "7"]}}, "response": []}, {"name": "get all patients", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/patients", "host": ["{{baseUrl}}"], "path": ["patients"]}}, "response": []}, {"name": "app settings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/patients/app-settings", "host": ["{{baseUrl}}"], "path": ["patients", "app-settings"]}}, "response": []}, {"name": "rate patient", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"requestId\": 23,\n    \"patientId\": 1,\n    \"rating\": 4,\n    \"comment\": \"prince\",\n    \"callRating\": 2,\n    \"callComment\": \"tmam\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/patients/ratings", "host": ["{{baseUrl}}"], "path": ["patients", "ratings"]}}, "response": []}, {"name": "download patients", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/patients/download?query={}", "host": ["{{baseUrl}}"], "path": ["patients", "download"], "query": [{"key": "query", "value": "{}"}]}}, "response": []}]}, {"name": "grades", "item": [{"name": "get grade", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "url": {"raw": "{{baseUrl}}/grades/1", "host": ["{{baseUrl}}"], "path": ["grades", "1"]}}, "response": []}, {"name": "get all grades", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "url": {"raw": "{{baseUrl}}/grades", "host": ["{{baseUrl}}"], "path": ["grades"]}}, "response": []}, {"name": "create grade", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"translations\": [\n        {\n            \"title\": \"master\",\n            \"languageCode\": \"en\"\n        },\n        {\n            \"title\": \"ماجستير\",\n            \"languageCode\": \"ar\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/grades", "host": ["{{baseUrl}}"], "path": ["grades"]}}, "response": []}, {"name": "update grade", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "value": "en", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"translations\": [\n        {\n            \"title\": \"master\",\n            \"languageCode\": \"en\"\n        },\n        {\n            \"title\": \"ماجستير\",\n            \"languageCode\": \"ar\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/grades/1", "host": ["{{baseUrl}}"], "path": ["grades", "1"]}}, "response": []}, {"name": "delete grade", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/grades/3", "host": ["{{baseUrl}}"], "path": ["grades", "3"]}}, "response": []}]}, {"name": "cities", "item": [{"name": "get all cities", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "url": {"raw": "{{baseUrl}}/cities", "host": ["{{baseUrl}}"], "path": ["cities"]}}, "response": []}]}, {"name": "governorates", "item": [{"name": "get all governorates", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/governorates", "host": ["{{baseUrl}}"], "path": ["governorates"]}}, "response": []}]}, {"name": "specialities", "item": [{"name": "get speciality", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/specialities/7", "host": ["{{baseUrl}}"], "path": ["specialities", "7"]}}, "response": []}, {"name": "get all specialities", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/specialities", "host": ["{{baseUrl}}"], "path": ["specialities"]}}, "response": []}, {"name": "create speciality", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"translations\": [\n        {\n            \"title\": \"bones\",\n            \"languageCode\": \"en\"\n        },\n        {\n            \"title\": \"عظام\",\n            \"languageCode\": \"ar\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/specialities", "host": ["{{baseUrl}}"], "path": ["specialities"]}}, "response": []}, {"name": "update specialities", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"translations\": [\n        {\n            \"title\": \"english-update-2\",\n            \"languageCode\": \"en\"\n        },\n        {\n            \"title\": \"arabic-update\",\n            \"languageCode\": \"ar\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/specialities/7", "host": ["{{baseUrl}}"], "path": ["specialities", "7"]}}, "response": []}, {"name": "delete specialities", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/specialities/1", "host": ["{{baseUrl}}"], "path": ["specialities", "1"]}}, "response": []}]}, {"name": "clinics", "item": [{"name": "get all clinics", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/clinics", "host": ["{{baseUrl}}"], "path": ["clinics"]}}, "response": []}, {"name": "create clinics", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"string\",\n    \"fees\": 400,\n    \"mobileNumber\": \"+201140188074\",\n    \"landlineNumber\": \"+20222345678\",\n    \"clinicLocation\": \"test\",\n    \"location\": {\n        \"latitude\": 30.1,\n        \"longitude\": 30.2\n    },\n    \"address\": \"string\",\n    \"cityId\": 1,\n    \"governorateId\": 1,\n    \"area\": \"string\",\n    \"doctorId\": 94\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/clinics", "host": ["{{baseUrl}}"], "path": ["clinics"]}}, "response": []}, {"name": "update clinics", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"isFreeClinic\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/clinics/1", "host": ["{{baseUrl}}"], "path": ["clinics", "1"]}}, "response": []}, {"name": "delete clinics", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/clinics/40", "host": ["{{baseUrl}}"], "path": ["clinics", "40"]}}, "response": []}, {"name": "get doctor clinics", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/clinics/doctor/1", "host": ["{{baseUrl}}"], "path": ["clinics", "doctor", "1"]}}, "response": []}]}, {"name": "doctors", "item": [{"name": "create doctor", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"string\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+201273604081\",\n    \"gender\": \"Male\",\n    \"nationalID\": \"12345678912345\",\n    \"syndicateNumber\": \"string\",\n    \"languages\": [\n        \"arabic\"\n    ],\n    \"gradeId\": 1,\n    \"specialityIds\": [\n        \"1\"\n    ],\n    \"homeVisitFees\": \"100\",\n    \"onlineConsultationFees\": \"100\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/doctors", "host": ["{{baseUrl}}"], "path": ["doctors"]}}, "response": []}, {"name": "get doctors", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/doctors?take=10&page=1", "host": ["{{baseUrl}}"], "path": ["doctors"], "query": [{"key": "take", "value": "10"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "view doctor", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"user\": {\n        \"name\": \"string\",\n        \"email\": \"<EMAIL>\",\n        \"password\": \"string\",\n        \"phone\": \"+201273604089\",\n        \"gender\": \"string\"\n    },\n    \"doctor\": {\n        \"nationalID\": \"12345678912345\",\n        \"syndicateNumber\": \"string\",\n        \"language\": \"string\",\n        \"gradeId\": 1,\n        \"specialityIds\": [\n            \"1\"\n        ],\n        \"homeVisitFees\": 10,\n        \"onlineConsultationFees\": 10\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/doctors/6", "host": ["{{baseUrl}}"], "path": ["doctors", "6"]}}, "response": []}, {"name": "get doctor service providers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"user\": {\n        \"name\": \"string\",\n        \"email\": \"<EMAIL>\",\n        \"password\": \"string\",\n        \"phone\": \"+201273604089\",\n        \"gender\": \"string\"\n    },\n    \"doctor\": {\n        \"nationalID\": \"12345678912345\",\n        \"syndicateNumber\": \"string\",\n        \"language\": \"string\",\n        \"gradeId\": 1,\n        \"specialityIds\": [\n            \"1\"\n        ],\n        \"homeVisitFees\": 10,\n        \"onlineConsultationFees\": 10\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/doctors/6/service-provider", "host": ["{{baseUrl}}"], "path": ["doctors", "6", "service-provider"]}}, "response": []}, {"name": "delete doctor", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"user\": {\n        \"name\": \"string\",\n        \"email\": \"<EMAIL>\",\n        \"password\": \"string\",\n        \"phone\": \"+201273604089\",\n        \"gender\": \"string\"\n    },\n    \"doctor\": {\n        \"nationalID\": \"12345678912345\",\n        \"syndicateNumber\": \"string\",\n        \"language\": \"string\",\n        \"gradeId\": 1,\n        \"specialityIds\": [\n            \"1\"\n        ],\n        \"homeVisitFees\": 10,\n        \"onlineConsultationFees\": 10\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/doctors/2", "host": ["{{baseUrl}}"], "path": ["doctors", "2"]}}, "response": []}, {"name": "update doctor", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"link\": \"tttttttttttttttttttt\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/doctors/6", "host": ["{{baseUrl}}"], "path": ["doctors", "6"]}}, "response": []}, {"name": "update doctor new syndicate id url", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"isApproved\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/doctors/6/syndicate-id-url", "host": ["{{baseUrl}}"], "path": ["doctors", "6", "syndicate-id-url"]}}, "response": []}, {"name": "get doctor reviews", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/doctors/1/ratings", "host": ["{{baseUrl}}"], "path": ["doctors", "1", "ratings"]}}, "response": []}, {"name": "get doctor day schedule", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "url": {"raw": "{{baseUrl}}/doctors/6/day-schedule?query={\"filters\": [{\"type\": \"FIXED\",\"by\": \"clinic\" ,\"value\": \"4\"},{\"type\": \"FIXED\", \"by\": \"date\", \"value\": \"2021-01-04\"}]}", "host": ["{{baseUrl}}"], "path": ["doctors", "6", "day-schedule"], "query": [{"key": "query", "value": "{\"filters\": [{\"type\": \"FIXED\",\"by\": \"clinic\" ,\"value\": \"4\"},{\"type\": \"FIXED\", \"by\": \"date\", \"value\": \"2021-01-04\"}]}"}]}}, "response": []}, {"name": "get doctor schedule", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/doctors/6/schedule?query={\"filters\": [{\"type\": \"FIXED\",\"by\": \"online\" ,\"value\": \"4\"}]}", "host": ["{{baseUrl}}"], "path": ["doctors", "6", "schedule"], "query": [{"key": "query", "value": "{\"filters\": [{\"type\": \"FIXED\",\"by\": \"online\" ,\"value\": \"4\"}]}"}]}}, "response": []}, {"name": "rate doctor", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"requestId\": 23,\n    \"doctorId\": 1,\n    \"rating\": 4\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/doctors/ratings", "host": ["{{baseUrl}}"], "path": ["doctors", "ratings"]}}, "response": []}, {"name": "get doctor online packages", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/doctors/1/online-packages", "host": ["{{baseUrl}}"], "path": ["doctors", "1", "online-packages"]}}, "response": []}, {"name": "download doctors", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/doctors/download?providerId=9", "host": ["{{baseUrl}}"], "path": ["doctors", "download"], "query": [{"key": "providerId", "value": "9"}]}}, "response": []}]}, {"name": "schedules", "item": [{"name": "update doctor schedule", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"isEnabled\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/schedules/15", "host": ["{{baseUrl}}"], "path": ["schedules", "15"]}}, "response": []}, {"name": "get doctor schedule", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/schedules/doctor/1", "host": ["{{baseUrl}}"], "path": ["schedules", "doctor", "1"]}}, "response": []}]}, {"name": "consultation-requests", "item": [{"name": "create consultation request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"forOtherPatient\": false,\n    \"patientAddress\": \"address\",\n    \"patientLocation\": {\n        \"latitude\": 30.1,\n        \"longitude\": 30.2\n    },\n    \"patientNotes\": \"string\",\n    \"date\": \"2021-01-02\",\n    \"from\": \"01:00 PM\",\n    \"duration\": 5,\n    \"patientId\": 1,\n    \"fees\": 200,\n    \"paymentMethod\": \"Cash\",\n    \"type\": \"Home Visit\",\n    \"doctorId\": 5\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/consultation-requests", "host": ["{{baseUrl}}"], "path": ["consultation-requests"]}}, "response": []}, {"name": "update consultation request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "body": {"mode": "raw", "raw": "{\n    \"status\":\"Cancelled\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/consultation-requests/23", "host": ["{{baseUrl}}"], "path": ["consultation-requests", "23"]}}, "response": []}, {"name": "get all consultation requests", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/consultation-requests", "host": ["{{baseUrl}}"], "path": ["consultation-requests"]}}, "response": []}, {"name": "get consultation request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "url": {"raw": "{{baseUrl}}/consultation-requests/9", "host": ["{{baseUrl}}"], "path": ["consultation-requests", "9"]}}, "response": []}, {"name": "download consultation requests", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/consultation-requests/download?providerId=9", "host": ["{{baseUrl}}"], "path": ["consultation-requests", "download"], "query": [{"key": "providerId", "value": "9"}]}}, "response": []}]}, {"name": "notifications", "item": [{"name": "get all notifications", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/notifications", "host": ["{{baseUrl}}"], "path": ["notifications"]}}, "response": []}, {"name": "get unseen notifications count", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/notifications/unseen/count", "host": ["{{baseUrl}}"], "path": ["notifications", "unseen", "count"]}}, "response": []}, {"name": "mark notification as seen", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/notifications/seen/10", "host": ["{{baseUrl}}"], "path": ["notifications", "seen", "10"]}}, "response": []}, {"name": "mark all notification as seen", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "ar"}], "url": {"raw": "{{baseUrl}}/notifications/seen/", "host": ["{{baseUrl}}"], "path": ["notifications", "seen", ""]}}, "response": []}]}, {"name": "paymob", "item": [{"name": "auth", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"api_key\": \"ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6VXhNaUo5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TkRZMk9ETXNJbTVoYldVaU9pSnBibWwwYVdGc0luMC5MaFFycnhhT1JfeUdZRFdYdWFCYm5GQlZmUlRncWVSSnBhc3phY2lQWFhLZXdjc2RJYmJ1d012SE9sVnA3VXJlcHBkZVZsLWQ1TzJIbzBkQnhDSm95QQ==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://accept.paymobsolutions.com/api/auth/tokens", "protocol": "https", "host": ["accept", "paymobsolutions", "com"], "path": ["api", "auth", "tokens"]}}, "response": []}, {"name": "order registration", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"auth_token\": \"ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6VXhNaUo5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TkRZMk9ETXNJbVY0Y0NJNk1UWXdOell5TURZd01Td2ljR2hoYzJnaU9pSXhPVFkyTVRkbU1HTTNaVFZtTWpBMk1HWXhNREkzTW1ObU1USTVNRE5oWVRRMU5EbGhZakEzT0RJd00yVmtPVFpoWlRVNFpURTJOREZoTUdNMU5USXpJbjAuY0lvNmU5ZVg5UE5vNldORmdZVEUyZXBDeWx5Y3JSVTV0Uy1OQmZwRUxYRWxUQTl0dGFyZ3BYckF4cUd2VlVyaVB1RFBEdzk3OWtRSS1GcU0yYmQzV0E=\",\r\n    \"delivery_needed\": \"false\",\r\n    \"amount_cents\": \"100\",\r\n    \"currency\": \"EGP\",\r\n    \"items\": []\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://accept.paymobsolutions.com/api/ecommerce/orders", "protocol": "https", "host": ["accept", "paymobsolutions", "com"], "path": ["api", "ecommerce", "orders"]}}, "response": []}, {"name": "Payment Key Request", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"auth_token\": \"ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6VXhNaUo5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TkRZMk9ETXNJbVY0Y0NJNk1UWXdOell5TURZd01Td2ljR2hoYzJnaU9pSXhPVFkyTVRkbU1HTTNaVFZtTWpBMk1HWXhNREkzTW1ObU1USTVNRE5oWVRRMU5EbGhZakEzT0RJd00yVmtPVFpoWlRVNFpURTJOREZoTUdNMU5USXpJbjAuY0lvNmU5ZVg5UE5vNldORmdZVEUyZXBDeWx5Y3JSVTV0Uy1OQmZwRUxYRWxUQTl0dGFyZ3BYckF4cUd2VlVyaVB1RFBEdzk3OWtRSS1GcU0yYmQzV0E=\",\r\n    \"amount_cents\": \"100\",\r\n    \"expiration\": 3600,\r\n    \"order_id\": \"7593235\",\r\n    \"billing_data\": {\r\n        \"apartment\": \"NA\",\r\n        \"email\": \"<EMAIL>\",\r\n        \"floor\": \"NA\",\r\n        \"first_name\": \"amr\",\r\n        \"street\": \"NA\",\r\n        \"building\": \"NA\",\r\n        \"phone_number\": \"+01273604089\",\r\n        \"shipping_method\": \"NA\",\r\n        \"postal_code\": \"NA\",\r\n        \"city\": \"NA\",\r\n        \"country\": \"NA\",\r\n        \"last_name\": \"ahmed\",\r\n        \"state\": \"NA\"\r\n    },\r\n    \"currency\": \"EGP\",\r\n    \"integration_id\": 120327,\r\n    \"lock_order_when_paid\": \"false\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://accept.paymobsolutions.com/api/acceptance/payment_keys", "protocol": "https", "host": ["accept", "paymobsolutions", "com"], "path": ["api", "acceptance", "payment_keys"]}}, "response": []}, {"name": "capture payment", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"auth_token\": \"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\r\n    \"amount_cents\": \"100\",\r\n    \"transaction_id\": \"4953485\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://accept.paymobsolutions.com/api/acceptance/capture", "protocol": "https", "host": ["accept", "paymobsolutions", "com"], "path": ["api", "acceptance", "capture"]}}, "response": []}, {"name": "refund payment", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"auth_token\": \"ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6VXhNaUo5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TkRZMk9ETXNJbVY0Y0NJNk1UWXdOell5TURZd01Td2ljR2hoYzJnaU9pSXhPVFkyTVRkbU1HTTNaVFZtTWpBMk1HWXhNREkzTW1ObU1USTVNRE5oWVRRMU5EbGhZakEzT0RJd00yVmtPVFpoWlRVNFpURTJOREZoTUdNMU5USXpJbjAuY0lvNmU5ZVg5UE5vNldORmdZVEUyZXBDeWx5Y3JSVTV0Uy1OQmZwRUxYRWxUQTl0dGFyZ3BYckF4cUd2VlVyaVB1RFBEdzk3OWtRSS1GcU0yYmQzV0E=\",\r\n    \"amount_cents\": \"100\",\r\n    \"transaction_id\": \"4953485\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://accept.paymobsolutions.com/api/acceptance/void_refund/refund", "protocol": "https", "host": ["accept", "paymobsolutions", "com"], "path": ["api", "acceptance", "void_refund", "refund"]}}, "response": []}, {"name": "get transaction", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://accept.paymobsolutions.com/api/acceptance/transactions/4953485", "protocol": "https", "host": ["accept", "paymobsolutions", "com"], "path": ["api", "acceptance", "transactions", "4953485"]}}, "response": []}]}, {"name": "payment", "item": [{"name": "create bill transaction", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"billId\": 4,\r\n    \"transactionId\": \"123456\",\r\n    \"date\": \"2021-03-11 14:52:21\",\r\n    \"receipt\": \"test\",\r\n    \"bankName\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/bill-transaction", "host": ["{{baseUrl}}"], "path": ["bill-transaction"]}}, "response": []}, {"name": "create payment url", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"requestId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payments/create-payment-url/", "host": ["{{baseUrl}}"], "path": ["payments", "create-payment-url", ""]}}, "response": []}, {"name": "get all cards", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"requestId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payments/cards/", "host": ["{{baseUrl}}"], "path": ["payments", "cards", ""]}}, "response": []}, {"name": "bills", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"requestId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/bills?query={\"filters\":[{\"type\":\"FIXED\", \"by\":\"doctor\",\"value\":6}]}", "host": ["{{baseUrl}}"], "path": ["bills"], "query": [{"key": "query", "value": "{\"filters\":[{\"type\":\"FIXED\", \"by\":\"doctor\",\"value\":6}]}"}]}}, "response": []}, {"name": "payments list", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"requestId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payments?query={\"filters\":[{\"type\":\"RANGE\", \"by\":\"date\",\"min\":\"2021-03-01\",\"max\":\"2021-03-22\"}]}", "host": ["{{baseUrl}}"], "path": ["payments"], "query": [{"key": "query", "value": "{\"filters\":[{\"type\":\"RANGE\", \"by\":\"date\",\"min\":\"2021-03-01\",\"max\":\"2021-03-22\"}]}"}]}}, "response": []}, {"name": "create payment with saved token", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"requestId\": 514,\r\n    \"cardId\": 2\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payments/create-payment-with-saved-token/", "host": ["{{baseUrl}}"], "path": ["payments", "create-payment-with-saved-token", ""]}}, "response": []}, {"name": "verify payment", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"transactionId\": \"5000842\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payments/verify/", "host": ["{{baseUrl}}"], "path": ["payments", "verify", ""]}}, "response": []}]}, {"name": "aws", "item": [{"name": "get session token", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/aws/session-token", "host": ["{{baseUrl}}"], "path": ["aws", "session-token"]}}, "response": []}, {"name": "get upload url", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/aws/upload-url?key=doctors/6/profile_picture.jpg", "host": ["{{baseUrl}}"], "path": ["aws", "upload-url"], "query": [{"key": "key", "value": "doctors/6/profile_picture.jpg"}]}}, "response": []}]}, {"name": "insurance-companies", "item": [{"name": "create insurance company", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"logoURL\": \"https://kashf-dev.s3.eu-central-1.amazonaws.com/23_1234_syndicateIdURL\",\n    \"translations\": [\n        {\n            \"title\": \"english\",\n            \"languageCode\": \"en\"\n        },\n        {\n            \"title\": \"arabic\",\n            \"languageCode\": \"ar\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/insurance-companies", "host": ["{{baseUrl}}"], "path": ["insurance-companies"]}}, "response": []}, {"name": "get insurance company", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/insurance-companies/2", "host": ["{{baseUrl}}"], "path": ["insurance-companies", "2"]}}, "response": []}, {"name": "get insurance companies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/insurance-companies", "host": ["{{baseUrl}}"], "path": ["insurance-companies"]}}, "response": []}, {"name": "delete insurance company", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/insurance-companies/1", "host": ["{{baseUrl}}"], "path": ["insurance-companies", "1"]}}, "response": []}, {"name": "update insurance company", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"logoURL\": \"tttttttt\",\n    \"translations\": [\n        {\n            \"title\": \"arabic-update2\",\n            \"languageCode\": \"ar\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/insurance-companies/2", "host": ["{{baseUrl}}"], "path": ["insurance-companies", "2"]}}, "response": []}]}, {"name": "promo-codes", "item": [{"name": "create promo code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"code\": \"Amr\",\n    \"discountType\": \"Fixed\",\n    \"amount\": 20,\n    \"maxNumberOfUsage\": 2,\n    \"country\": \"Egypt\",\n    \"consultationType\": \"Clinic\",\n    \"startDate\": \"2021-01-20\",\n    \"endDate\": \"2021-04-20\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/promo-codes", "host": ["{{baseUrl}}"], "path": ["promo-codes"]}}, "response": []}, {"name": "verify promo code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"code\": \"Amr\",\n    \"requestId\": 25\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/promo-codes/verify", "host": ["{{baseUrl}}"], "path": ["promo-codes", "verify"]}}, "response": []}, {"name": "get promo code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/promo-codes/3", "host": ["{{baseUrl}}"], "path": ["promo-codes", "3"]}}, "response": []}, {"name": "get promo codes", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/promo-codes", "host": ["{{baseUrl}}"], "path": ["promo-codes"]}}, "response": []}, {"name": "delete promo code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/promo-codes/1", "host": ["{{baseUrl}}"], "path": ["promo-codes", "1"]}}, "response": []}, {"name": "update promo code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"isActive\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/promo-codes/1", "host": ["{{baseUrl}}"], "path": ["promo-codes", "1"]}}, "response": []}]}, {"name": "service-provider-types", "item": [{"name": "create service provider type", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"translations\": [\n        {\n            \"title\": \"english\",\n            \"languageCode\": \"en\"\n        },\n        {\n            \"title\": \"arabic\",\n            \"languageCode\": \"ar\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/service-provider-types", "host": ["{{baseUrl}}"], "path": ["service-provider-types"]}}, "response": []}, {"name": "get service provider type", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/service-provider-types/1", "host": ["{{baseUrl}}"], "path": ["service-provider-types", "1"]}}, "response": []}, {"name": "get service provider types", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/service-provider-types", "host": ["{{baseUrl}}"], "path": ["service-provider-types"]}}, "response": []}, {"name": "delete service provider type", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/service-provider-types/1", "host": ["{{baseUrl}}"], "path": ["service-provider-types", "1"]}}, "response": []}, {"name": "update service provider type", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"translations\": [\n        {\n            \"title\": \"english-update-2\",\n            \"languageCode\": \"en\"\n        },\n        {\n            \"title\": \"arabic-update\",\n            \"languageCode\": \"ar\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/service-provider-types/1", "host": ["{{baseUrl}}"], "path": ["service-provider-types", "1"]}}, "response": []}]}, {"name": "service-providers", "item": [{"name": "create service provider", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"translations\": [\n        {\n            \"name\": \"english\",\n            \"languageCode\": \"en\"\n        },\n        {\n            \"name\": \"arabic\",\n            \"languageCode\": \"ar\"\n        }\n    ],\n    \"email\": \"<EMAIL>\",\n    \"password\": \"string\",\n    \"phone\": \"+201140188565\",\n    \"landlinePhone\": \"+2027495928947\",\n    \"serviceProviderTypeId\": 2,\n    \"location\": {\n        \"latitude\": 30.1,\n        \"longitude\": 30.2\n    },\n    \"cityId\": 1,\n    \"governorateId\": 1,\n    \"area\": \"string\",\n    \"website\": \"www.tt.com\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/service-providers", "host": ["{{baseUrl}}"], "path": ["service-providers"]}}, "response": []}, {"name": "add  service provider doctor", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept-Language", "value": "en", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"serviceProviderSchedule\": [\n        {\n            \"isEnabled\": true,\n            \"type\": \"Home Visit\",\n            \"fees\": 10,\n            \"workingDays\": [\n                {\n                    \"id\": 2787,\n                    \"day\": \"Saturday\",\n                    \"to\": \"22:00:00\",\n                    \"from\": \"10:00:00\",\n                    \"isActive\": false,\n                    \"containsBreak\": false,\n                    \"order\": 1\n                },\n                {\n                    \"id\": 2788,\n                    \"day\": \"Sunday\",\n                    \"to\": \"22:00:00\",\n                    \"from\": \"10:00:00\",\n                    \"isActive\": false,\n                    \"containsBreak\": false,\n                    \"order\": 2\n                },\n                {\n                    \"id\": 2793,\n                    \"day\": \"Monday\",\n                    \"to\": \"22:00:00\",\n                    \"from\": \"10:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 3\n                },\n                {\n                    \"id\": 2791,\n                    \"day\": \"Tuesday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 4\n                },\n                {\n                    \"id\": 2790,\n                    \"day\": \"Wednesday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 5\n                },\n                {\n                    \"id\": 2789,\n                    \"day\": \"Thursday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 6\n                },\n                {\n                    \"id\": 2792,\n                    \"day\": \"Friday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 7\n                }\n            ]\n        },\n        {\n            \"isEnabled\": true,\n            \"type\": \"Online Consultation\",\n            \"fees\": 10,\n            \"workingDays\": [\n                {\n                    \"id\": 2787,\n                    \"day\": \"Saturday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": false,\n                    \"containsBreak\": false,\n                    \"order\": 1\n                },\n                {\n                    \"id\": 2788,\n                    \"day\": \"Sunday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": false,\n                    \"containsBreak\": false,\n                    \"order\": 2\n                },\n                {\n                    \"id\": 2793,\n                    \"day\": \"Monday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 3\n                },\n                {\n                    \"id\": 2791,\n                    \"day\": \"Tuesday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 4\n                },\n                {\n                    \"id\": 2790,\n                    \"day\": \"Wednesday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 5\n                },\n                {\n                    \"id\": 2789,\n                    \"day\": \"Thursday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 6\n                },\n                {\n                    \"id\": 2792,\n                    \"day\": \"Friday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 7\n                }\n            ]\n        },\n        {\n            \"isEnabled\": true,\n            \"type\": \"Clinic\",\n            \"fees\": 10,\n            \"workingDays\": [\n                {\n                    \"id\": 2787,\n                    \"day\": \"Saturday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": false,\n                    \"containsBreak\": false,\n                    \"order\": 1\n                },\n                {\n                    \"id\": 2788,\n                    \"day\": \"Sunday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": false,\n                    \"containsBreak\": false,\n                    \"order\": 2\n                },\n                {\n                    \"id\": 2793,\n                    \"day\": \"Monday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 3\n                },\n                {\n                    \"id\": 2791,\n                    \"day\": \"Tuesday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 4\n                },\n                {\n                    \"id\": 2790,\n                    \"day\": \"Wednesday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 5\n                },\n                {\n                    \"id\": 2789,\n                    \"day\": \"Thursday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 6\n                },\n                {\n                    \"id\": 2792,\n                    \"day\": \"Friday\",\n                    \"to\": \"21:00:00\",\n                    \"from\": \"09:00:00\",\n                    \"isActive\": true,\n                    \"containsBreak\": false,\n                    \"order\": 7\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/service-providers/2/doctors/5/schedule", "host": ["{{baseUrl}}"], "path": ["service-providers", "2", "doctors", "5", "schedule"]}}, "response": []}, {"name": "delete  service provider doctor", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/service-providers/2/doctors/5", "host": ["{{baseUrl}}"], "path": ["service-providers", "2", "doctors", "5"]}}, "response": []}, {"name": "update  service provider doctor schedule", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept-Language", "type": "text", "value": "en"}], "body": {"mode": "raw", "raw": "{\n    \"serviceProviderSchedule\": [\n        {\n            \"id\": 39,\n            \"isEnabled\": true,\n            \"type\": \"Home Visit\",\n            \"fees\": 100,\n            \"workingDays\": [\n                {\n                    \"id\": 289,\n                    \"day\": \"Saturday\",\n                    \"to\": \"15:00:00\",\n                    \"from\": \"08:00:00\",\n                    \"isActive\": false,\n                    \"containsBreak\": false,\n                    \"order\": 1\n                },\n                {\n                    \"id\": 292,\n                    \"day\": \"Sunday\",\n                    \"to\": \"20:00:00\",\n                    \"from\": \"14:00:00\",\n                    \"isActive\": false,\n                    \"containsBreak\": false,\n                    \"order\": 2\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/service-providers/2/doctors/5/schedule", "host": ["{{baseUrl}}"], "path": ["service-providers", "2", "doctors", "5", "schedule"]}}, "response": []}, {"name": "get service provider", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/service-providers/4", "host": ["{{baseUrl}}"], "path": ["service-providers", "4"]}}, "response": []}, {"name": "get service providers", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/service-providers", "host": ["{{baseUrl}}"], "path": ["service-providers"]}}, "response": []}, {"name": "delete service provider", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/service-providers/6", "host": ["{{baseUrl}}"], "path": ["service-providers", "6"]}}, "response": []}, {"name": "update service provider", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/service-providers/4", "host": ["{{baseUrl}}"], "path": ["service-providers", "4"]}}, "response": []}]}, {"name": "help-center", "item": [{"name": "create call request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"soli\",\n    \"mobileNumber\": \"+201092166492\",\n    \"email\": \"<EMAIL>\",\n    \"message\": \"test message\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/help-center/call-requests", "host": ["{{baseUrl}}"], "path": ["help-center", "call-requests"]}}, "response": []}, {"name": "create feedback", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"soli bardo\",\n    \"mobileNumber\": \"+201092166492\",\n    \"email\": \"<EMAIL>\",\n    \"message\": \"test message\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/help-center/feedbacks", "host": ["{{baseUrl}}"], "path": ["help-center", "feedbacks"]}}, "response": []}]}]}