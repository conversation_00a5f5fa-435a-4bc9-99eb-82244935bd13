# Kashf REPL - Tinkerwell-like Environment for NestJS

A powerful, web-based REPL (Read-Eval-Print Loop) environment for your NestJS application, similar to <PERSON><PERSON>'s Tinkerwell. Execute JavaScript/TypeScript code with full access to your application context, services, and database.

## Features

- 🎨 **Beautiful Web Interface** - Modern, responsive design with syntax highlighting
- 🚀 **Real-time Code Execution** - Execute code instantly with Ctrl+Enter
- 🔧 **NestJS Integration** - Full access to your application services and modules
- 💾 **Database Access** - Direct access to TypeORM entities and queries
- 📝 **Syntax Highlighting** - CodeMirror editor with JavaScript/TypeScript support
- 🎯 **Auto-completion** - Intelligent code completion
- 📊 **Rich Output** - Formatted results with error handling
- 💡 **Built-in Examples** - Pre-loaded examples to get you started

## Getting Started

### 1. Start the REPL Server

```bash
# Development mode (recommended)
npm run repl:gui

# Or using ts-node directly
npx ts-node -r tsconfig-paths/register --files src/repl.ts
```

### 2. Open the Web Interface

Navigate to `http://localhost:3001` in your browser.

### 3. Start Coding!

The REPL provides a rich environment with access to:

- **Basic JavaScript/TypeScript** - All standard JS features
- **Async/Await** - Full support for asynchronous operations
- **NestJS Services** - Access to your application's dependency injection container
- **Database Operations** - Direct access to TypeORM entities and raw queries
- **Utility Functions** - Built-in helpers for common tasks

## Available Functions

### Core Functions

- `help()` - Display available commands and examples
- `inspect(obj)` - Detailed object inspection with formatting
- `console.log/error/warn/info()` - Output to console with capture

### NestJS Functions (when app context is available)

- `app` - NestJS application instance
- `getService(ServiceClass)` - Get a service from the DI container
- `getConnection()` - Get TypeORM DataSource
- `getEntityManager()` - Get TypeORM EntityManager
- `getRepository(EntityClass)` - Get repository for an entity

## Example Usage

### Basic JavaScript
```javascript
console.log('Hello from Kashf REPL!');
const message = 'This is working!';
return message;
```

### Async Operations
```javascript
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
console.log('Starting async operation...');
await delay(1000);
console.log('Completed!');
return 'Async example finished';
```

### NestJS Service Access
```javascript
// Get a service from your application
const configService = getService('ConfigService');
console.log('Config loaded:', !!configService);

// Access service methods
const isDevelopment = configService.isDevelopment;
console.log('Development mode:', isDevelopment);
```

### Database Operations
```javascript
// Get entity manager
const entityManager = getEntityManager();

// Raw SQL queries
const users = await entityManager.query('SELECT * FROM users LIMIT 5');
console.log('Users found:', users.length);

// Repository pattern (replace User with your actual entity)
// const userRepo = getRepository(User);
// const user = await userRepo.findOne({ where: { id: 1 } });
// inspect(user);
```

## Keyboard Shortcuts

- **Ctrl+Enter** - Execute code
- **Ctrl+L** - Clear results
- **Ctrl+K** - Clear editor
- **Tab** - Auto-complete

## Configuration

### Environment Variables

- `REPL_PORT` - Port for the REPL server (default: 3001)
- `NODE_ENV` - Environment mode (development/production)

### Customization

The REPL can be customized by modifying:

- `src/modules/repl/repl.service.ts` - Core execution logic and context
- `repl-gui/index.html` - Web interface layout and styling
- `repl-gui/app.js` - Frontend JavaScript functionality

## Security Considerations

⚠️ **Important**: This REPL provides full access to your application and database. Only use in development environments and never expose to production traffic.

## Troubleshooting

### REPL Won't Start
- Check if port 3001 is available
- Ensure all dependencies are installed: `npm install`
- Check for TypeScript compilation errors

### No App Context Available
- The REPL will show "NestJS Application Context Not Available"
- This means the main app couldn't be loaded (usually due to missing environment variables)
- The REPL will still work with basic JavaScript functionality

### Database Connection Issues
- Ensure your database is running and accessible
- Check your database configuration in the environment files
- Verify TypeORM entities are properly configured

## Architecture

The REPL consists of:

1. **Backend** (`src/modules/repl/`)
   - `ReplModule` - NestJS module configuration
   - `ReplService` - Core execution engine with VM context
   - `ReplController` - REST API endpoints

2. **Frontend** (`repl-gui/`)
   - Modern web interface with CodeMirror editor
   - Real-time communication with backend
   - Syntax highlighting and auto-completion

3. **Context Management**
   - Isolated VM context for safe code execution
   - Access to NestJS DI container
   - TypeORM integration for database operations

## Contributing

To extend the REPL:

1. Add new helper functions in `ReplService.initializeContext()`
2. Enhance the web interface in `repl-gui/`
3. Add new API endpoints in `ReplController`

## License

This REPL is part of the Kashf project and follows the same license terms.
