PORT=3000
NODE_ENV=development

DATABASE_TYPE=postgres
DATABASE_USER=postgres
DATABASE_NAME=kashf
DATABASE_PORT=5432
DATABASE_HOST=localhost
DATABASE_PASSWORD=pass123
TYPEORM_SYNCHRONIZE=true
TYPEORM_MIGRATIONS=true

JWT_ACCESS_TOKEN_SECRET_KEY=secretkey
JWT_ACCESS_TOKEN_EXPIRES_IN=15m
JWT_REFRESH_TOKEN_EXPIRES_IN=15m
JWT_REFRESH_TOKEN_SECRET_KEY=secretkeyrefresh

FALLBACK_LANGUAGE=ar

# aws configurations
AWS_REGION=hi
AWS_ACCESS_KEY_ID=hi
AWS_SECRET_ACCESS_KEY=hi
AWS_EXPIRATION_TIME_IN_MINUTES=10
AWS_S3_BUCKET_NAME=bucket


#agora
AGORA_APP_ID=
AGORA_CERTIFICATE=

DOCTORS_CONSULTATION_DURATION_IN_MINUTES=10

#firebase
FIREBASE_CLIENT_EMAIL=<EMAIL>
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_PROJECT_ID=kashf-e4602


#PAYMOB
PAYMOB_BASE_URL=https://accept.paymobsolutions.com/api/
PAYMOB_API_KEY=ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6VXhNaUo5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TkRZMk9ETXNJbTVoYldVaU9pSnBibWwwYVdGc0luMC5MaFFycnhhT1JfeUdZRFdYdWFCYm5GQlZmUlRncWVSSnBhc3phY2lQWFhLZXdjc2RJYmJ1d012SE9sVnA3VXJlcHBkZVZsLWQ1TzJIbzBkQnhDSm95QQ==
PAYMOB_IFRAME=123616
PAYMOB_INTEGRATIONID=120327


#email
EMAIL=<EMAIL>
EMAIL_PASSWORD=xxxxxxx
SERVER_URL=http://localhost:5000/
