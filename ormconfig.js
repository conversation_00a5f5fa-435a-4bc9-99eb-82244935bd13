const SnakeNamingStrategy = require('typeorm-naming-strategies')
  .SnakeNamingStrategy;
const ENTITY_PATH = `${__dirname}/dist/**/*.entity.js`;
const MIGRATION_PATH = `${__dirname}/dist/migration/*.js`;
let host  =  process.env.DATABASE_HOST;
let connection = {
  database: process.env.DATABASE_NAME,
  host,
  password: process.env.DATABASE_PASSWORD,
  port: Number(process.env.DATABASE_PORT),
  type: process.env.DATABASE_TYPE,
  username: process.env.DATABASE_USER,
  logging: true,
  migrationsRun: true,
  migrationsTableName: 'migrations',
  entities: [
    ENTITY_PATH,
  ],
  migrations: [
    MIGRATION_PATH,
  ],
  dataSource: {
    entitiesDir: 'src/entity',
    migrationsDir: 'src/migration',
    subscribersDir: 'src/subscriber',
  },
  namingStrategy: new SnakeNamingStrategy(),
};
console.log(JSON.stringify(connection, null, 2))
module.exports = connection;
