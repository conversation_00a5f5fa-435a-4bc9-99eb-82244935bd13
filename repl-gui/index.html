<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kashf REPL - Interactive Code Environment</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/show-hint.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/dialog/dialog.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            color: white;
            overflow-y: auto;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: 300;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ff4757;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background: #2ed573;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .editor-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 20px;
        }

        .code-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-header {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px 15px;
            color: white;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .CodeMirror {
            height: 200px;
            font-family: 'Fira Code', 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }

        .result-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-height: 300px;
            overflow-y: auto;
        }

        .result-content {
            padding: 15px;
            color: white;
            font-family: 'Fira Code', 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }

        .result-success {
            border-left: 4px solid #2ed573;
        }

        .result-error {
            border-left: 4px solid #ff4757;
        }

        .result-output {
            border-left: 4px solid #ffa502;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #ffa502;
        }

        .sidebar ul {
            list-style: none;
            margin-bottom: 20px;
        }

        .sidebar li {
            padding: 5px 0;
            font-size: 14px;
            opacity: 0.8;
        }

        .examples {
            margin-top: 20px;
        }

        .example-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 12px;
        }

        .example-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .loading {
            display: none;
            color: #ffa502;
        }

        .loading.show {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 165, 2, 0.3);
            border-top: 2px solid #ffa502;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h3><i class="fas fa-info-circle"></i> Context Info</h3>
            <div id="context-info">
                <p>Loading context...</p>
            </div>

            <h3><i class="fas fa-code"></i> Examples</h3>
            <div class="examples">
                <div class="example-item" onclick="loadExample('help')">
                    <strong>help()</strong><br>
                    Show available commands
                </div>
                <div class="example-item" onclick="loadExample('basic')">
                    <strong>Basic JavaScript</strong><br>
                    console.log('Hello World!')
                </div>
                <div class="example-item" onclick="loadExample('async')">
                    <strong>Async Operations</strong><br>
                    await Promise.resolve('Done!')
                </div>
                <div class="example-item" onclick="loadExample('nestjs')">
                    <strong>NestJS Service</strong><br>
                    const service = getService(UsersService)
                </div>
            </div>

            <h3><i class="fas fa-keyboard"></i> Shortcuts</h3>
            <ul>
                <li><strong>Ctrl+Enter:</strong> Execute code</li>
                <li><strong>Ctrl+/:</strong> Toggle comment</li>
                <li><strong>Ctrl+L:</strong> Clear results</li>
                <li><strong>Ctrl+K:</strong> Clear editor</li>
                <li><strong>Ctrl+D:</strong> Duplicate line</li>
                <li><strong>Ctrl+F:</strong> Find</li>
                <li><strong>Ctrl+H:</strong> Find & replace</li>
                <li><strong>Tab:</strong> Auto-complete</li>
                <li><strong>Ctrl+Space:</strong> Force autocomplete</li>
                <li><strong>Alt+Up/Down:</strong> Move line up/down</li>
            </ul>
        </div>

        <div class="main-content">
            <div class="header">
                <h1><i class="fas fa-terminal"></i> Kashf REPL</h1>
                <div class="status">
                    <div class="status-indicator" id="connection-status"></div>
                    <span id="status-text">Connecting...</span>
                </div>
            </div>

            <div class="editor-container">
                <div class="controls">
                    <button class="btn btn-primary" onclick="executeCode()" id="execute-btn">
                        <i class="fas fa-play"></i> Execute
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">
                        <i class="fas fa-trash"></i> Clear Results
                    </button>
                    <button class="btn btn-secondary" onclick="clearEditor()">
                        <i class="fas fa-eraser"></i> Clear Editor
                    </button>
                    <button class="btn btn-secondary" onclick="loadSavedCode()" title="Load previously saved code">
                        <i class="fas fa-folder-open"></i> Load Saved
                    </button>
                    <button class="btn btn-secondary" onclick="formatCode()" title="Format/beautify code">
                        <i class="fas fa-magic"></i> Format
                    </button>
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        Executing...
                    </div>
                </div>

                <div class="code-section">
                    <div class="section-header">
                        <span><i class="fas fa-edit"></i> Code Editor</span>
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <span id="cursor-position" style="font-size: 12px; opacity: 0.7;">Line 1, Column 1</span>
                            <span style="font-size: 12px;">Press Ctrl+Enter to execute</span>
                        </div>
                    </div>
                    <textarea id="code-editor">// Welcome to Kashf REPL!
// This is a Tinkerwell-like environment for your NestJS application
// Type JavaScript/TypeScript code and press Ctrl+Enter to execute

// Start with the help command to see available functions
help()</textarea>
                </div>

                <div class="result-container">
                    <div class="section-header">
                        <span><i class="fas fa-terminal"></i> Results</span>
                        <span id="execution-time"></span>
                    </div>
                    <div class="result-content" id="results">
                        <div style="opacity: 0.7;">Results will appear here...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/show-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/javascript-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/comment/comment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/selection/active-line.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/trailingspace.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/brace-fold.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/comment-fold.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/search/search.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/search/searchcursor.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/dialog/dialog.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="app.js"></script>
</body>
</html>
