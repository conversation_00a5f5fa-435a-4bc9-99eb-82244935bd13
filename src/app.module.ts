import './boilerplate.polyfill';

import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ScheduleModule as NestSchedule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
    AcceptLanguageResolver,
    CookieResolver,
    HeaderResolver,
    I18nJsonParser,
    I18nModule,
    QueryResolver,
} from 'nestjs-i18n';
import * as path from 'path';

import { ConfigModule } from './config/config.module';
import { ConfigService } from './config/config.service';
import { contextMiddleware } from './middlewares';
import { AccountantsModule } from './modules/accountants/accountants.module';
import { AuthModule } from './modules/auth/auth.module';
import { BookingsModule } from './modules/booking/bookings.module';
import { CitiesModule } from './modules/cities/cities.module';
import { ClinicsModule } from './modules/clinics/clinics.module';
import { AwsModule } from './modules/cloud-providers/aws/aws.module';
import { ComplaintModule } from './modules/complaints/complaint.module';
import { ConsultationRequestsModule } from './modules/consultation-requests/consultation-requests.module';
import { CustomNotificationsModule } from './modules/custom-notifications/custom-notifications.module';
import { DoctorsModule } from './modules/doctors/doctors.module';
import { FavouriteModule } from './modules/favourites/favourites.module';
import { FileModule } from './modules/file/file.module';
import { GovernoratesModule } from './modules/governorates/governorates.module';
import { GradesModule } from './modules/grades/grades.module';
import { HelpCenterModule } from './modules/help-center/help-center.module';
import { InsuranceCompaniesModule } from './modules/insurance-companies/insurance-companies.module';
import { MedicationsModule } from './modules/medications/medications.module';
import { MembershipsModule } from './modules/memberships/memberships.module';
import { MultiLangModule } from './modules/multilang-ai-messages/multilang.module';
import { NationalitiesModule } from './modules/nationalities/nationalities.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { PackagesModule } from './modules/packages/packages.module';
import { PatientsModule } from './modules/patients/patients.module';
import { PaymentsModule } from './modules/payments/payments.module';
import { PromoCodesModule } from './modules/promo-codes/promo-codes.module';
import { RatingsModule } from './modules/ratings/ratings.module';
import { RemindersModule } from './modules/reminders/reminders.module';
import { SchedulesModule } from './modules/schedules/schedules.module';
import { ServiceProviderTypesModule } from './modules/service-provider-types/service-provider-types.module';
import { ServiceProvidersModule } from './modules/service-providers/service-providers.module';
import { SettingsModule } from './modules/settings/settings.module';
import { SpecialitiesModule } from './modules/specialities/specialities.module';
import { UsersModule } from './modules/users/users.module';
import { VerificationModule } from './modules/verification/verification.module';
import { AiModelModule } from './modules/artificial-intelligence/ai.module';

@Module({
    imports: [
        TypeOrmModule.forRootAsync({
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) =>
                configService.typeOrmConfig,
            inject: [ConfigService],
        }),
        I18nModule.forRootAsync({
            useFactory: (configService: ConfigService) => ({
                fallbackLanguage: 'ar',
                filePattern: '*.json',
                parserOptions: {
                    path: path.join(__dirname, '/i18n/'),
                    watch: configService.isDevelopment,
                },
            }),
            imports: [ConfigModule],
            parser: I18nJsonParser,
            inject: [ConfigService],
            resolvers: [
                { use: QueryResolver, options: ['lang', 'locale', 'l'] },
                new HeaderResolver(['Accept-Language']),
                AcceptLanguageResolver,
                new CookieResolver(['lang', 'locale', 'l']),
            ],
        }),
        FileModule,
        AuthModule,
        UsersModule,
        CustomNotificationsModule,
        GradesModule,
        SpecialitiesModule,
        DoctorsModule,
        ClinicsModule,
        SchedulesModule,
        PatientsModule,
        MedicationsModule,
        MembershipsModule,
        ConsultationRequestsModule,
        RatingsModule,
        NotificationsModule,
        FavouriteModule,
        PromoCodesModule,
        PackagesModule,
        BookingsModule,
        PaymentsModule,
        AwsModule,
        ServiceProvidersModule,
        ServiceProviderTypesModule,
        InsuranceCompaniesModule,
        PromoCodesModule,
        CitiesModule,
        GovernoratesModule,
        HelpCenterModule,
        SettingsModule,
        AccountantsModule,
        RemindersModule,
        VerificationModule,
        NationalitiesModule,
        ComplaintModule,
        MultiLangModule,
        AiModelModule,
        NestSchedule.forRoot(),
    ],
    // providers: [
    //     {
    //         provide: APP_FILTER,
    //         useClass: AllExceptionsFilter,
    //     },
    // ],
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer): MiddlewareConsumer | void {
        consumer.apply(contextMiddleware).forRoutes('*');
    }
}
