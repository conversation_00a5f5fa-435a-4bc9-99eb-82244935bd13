/* eslint-disable @typescript-eslint/naming-convention,@typescript-eslint/tslint/config */
import 'source-map-support/register';

import { Brackets, QueryBuilder, SelectQueryBuilder } from 'typeorm';

import { PageMetaDto } from './common/dto/pageMetaDto';
import { PageOptionsDto } from './common/dto/pageOptionsDto';

// declare global {
//     interface Array<T> {
//         toDtos<B extends AbstractDto>(this: AbstractEntity<B>[]): B[];
//     }
// }

declare module 'typeorm' {
    interface QueryBuilder<Entity> {
        searchByString(q: string, columnNames: string[]): this;
    }

    interface SelectQueryBuilder<Entity> {
        paginate(
            this: SelectQueryBuilder<Entity>,
            pageOptionsDto: PageOptionsDto,
        ): Promise<[Entity[], PageMetaDto]>;
    }
}

QueryBuilder.prototype.searchByString = function (q, columnNames) {
    this.andWhere(
        new Brackets((qb) => {
            for (const item of columnNames) {
                qb.orWhere(`${item} ILIKE :q`);
            }
        }),
    );

    this.setParameter('q', `%${q}%`);

    return this;
};

SelectQueryBuilder.prototype.paginate = async function <Entity>(
    this: SelectQueryBuilder<Entity>,
    pageOptionsDto: PageOptionsDto,
): Promise<[Entity[], PageMetaDto]> {
    const [items, itemCount] = await this.skip(pageOptionsDto.skip)
        .take(pageOptionsDto.take)
        .getManyAndCount();

    const pageMetaDto = new PageMetaDto({
        itemCount,
        pageOptionsDto,
    });

    return [items, pageMetaDto];
};
