/* eslint-disable @typescript-eslint/restrict-template-expressions */
import * as fs from 'fs';
export class PsuedoLogger {
    error(msg: string | unknown): void {
        this.writeErrorLogToFile(`error:       ${msg}`);
    }
    log(msg: string | unknown): void {
        this.writeErrorLogToFile(`log:       ${msg}`);
    }
    private writeErrorLogToFile = (errorLog: string): void => {
        fs.appendFile('error.log', errorLog + '\n', 'utf8', (err) => {
            if (err != null) {
                console.error('Failed to write to error log:', err);
            }
        });
    };
}
