'use strict';

import { Expose } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';
import {
    CreateDateColumn,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

export abstract class AbstractEntity {
    @PrimaryGeneratedColumn()
    @Expose()
    @IsOptional()
    @IsNumber()
    id?: number;

    @CreateDateColumn({
        type: 'timestamp without time zone',
        name: 'created_at',
    })
    @Expose()
    @IsOptional()
    createdAt?: Date;

    @UpdateDateColumn({
        type: 'timestamp without time zone',
        name: 'updated_at',
    })
    @Expose()
    @IsOptional()
    updatedAt?: Date;
}
