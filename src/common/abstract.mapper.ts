import { ClassConstructor as ClassType, plainToClass } from 'class-transformer';

import { AbstractEntity } from './abstract.entity';
import { AbstractDto } from './dto/abstractDto';

export class AbstractMapper<T extends AbstractDto, K extends AbstractEntity> {
    protected fromEntityToDTO(
        destination: ClassType<T>,
        sourceObject: K,
        lang?: string,
        excludeExtraneousValues = true,
    ): T {
        return plainToClass(destination, sourceObject, {
            excludeExtraneousValues,
        });
    }

    protected fromDTOToEntity(
        destination: ClassType<K>,
        sourceObject: T,
        lang?: string,
        excludeExtraneousValues = true,
    ): K {
        return plainToClass(destination, sourceObject, {
            excludeExtraneousValues,
        });
    }
}
