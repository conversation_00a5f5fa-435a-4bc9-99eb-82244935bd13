'use strict';

export enum ConsultationRequestStatus {
    STARTED = 'Started',
    REQUESTED = 'Requested',
    ACCEPTED = 'Accepted',
    PENDINGPAYMENT = 'Pending Payment',
    PAID = 'Paid',
    CANCELLED = 'Cancelled',
    FINISHED = 'Finished',
    EXPIRED = 'Expired',
}

export enum UserStatus {
    ONLINE = 'Online',
    OFFLINE = 'Offline',
    BUSY = 'Busy',
}

export enum PaymentStatus {
    PENDING = 'Pending',
    PAID = 'Paid',
    REFUNDED = 'Refunded',
    FAILED = 'Failed',
}

export enum BillStatus {
    NOT_SETTLED = 'NotSettled',
    SETTLED = 'Settled',
}

export enum ProviderTransactionStatus {
    PENDING = 'Pending',
    SETTLED = 'Settled',
}

export enum ReminderType {
    ONE_DAY_BEFORE = 'one_day_before',
    TWO_HOURS_BEFORE = 'two_hours_before',
    FIFTEEN_MINUTES_BEFORE = 'fifteen_minutes_before',
}
