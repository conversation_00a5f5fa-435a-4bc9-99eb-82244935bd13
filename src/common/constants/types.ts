'use strict';

export enum SourceType {
    DOCTOR = 'doctor',
    ADMIN = 'admin',
    PATIENT = 'patient',
    SERVICE_PROVIDER = 'service-provider',
}

export enum DEACTIVATION_REASON {
    UNPAID_KASHF_FEES = 'UNPAID_KASHF_FEES',
    OTHER = 'OTHER',
}

export enum RoleType {
    GUEST = 'GUEST',
    ADMIN = 'ADMIN',
    SUPER_ADMIN = 'SUPER_ADMIN',
    DOCTOR = 'DOCTOR',
    PATIENT = 'PATIENT',
    SERVICE_PROVIDER = 'SERVICE_PROVIDER',
    ACCOUNTANT = 'ACCOUNTANT',
}

export enum SortingType {
    ASC = 'ASC',
    DESC = 'DESC',
}

export enum FilterType {
    FIXED = 'FIXED',
    RANGE = 'RANGE',
    MULTIPLE = 'MULTIPLE',
}

export enum SortByType {
    POPULARITY = 'popularity',
    FEES = 'fees',
    ROLE = 'role',
    RATING = 'rating',
    CREATED_AT = 'created_at',
    UPDATED_AT = 'updated_at',
    DATE = 'date',
    NAME = 'name',
    DOCTOR_NAME = 'doctorName',
    SPECIALTY = 'specialty',
    CONSULTATION_TYPE = 'consultation_type',
    SERVICE_PROVIDER_NAME = 'service_provider_name',
    SERVICE_PROVIDER_TYPE = 'service_provider_type',
}

export enum FilterByType {
    ID = 'id',
    SPECIALITY = 'speciality',
    SERVICE_PROVIDER = 'service_provider',
    INSURANCE_COMPANY = 'insurance_company',
    PAYMENT_METHOD = 'payment_method',
    DOCTOR = 'doctor',
    PATIENT = 'patient',
    GRADE = 'grade',
    GOVERNORATE = 'governorate',
    CITY = 'city',
    GENDER = 'gender',
    RATING = 'rating',
    FEES = 'fees',
    HOME = 'home',
    ONLINE = 'online',
    TYPE = 'type',
    CLINIC = 'clinic',
    DATE = 'date',
    LANGUAGES = 'languages',
    EMAIL = 'email',
    CONSULTATION_TYPE = 'consultation_type',
    SERVICE_PROVIDER_TYPE = 'service_provider_type',
}

export enum MaritalStatusType {
    MARRIED = 'Married',
    SINGLE = 'Single',
    DIVORCED = 'Divorced',
    SEPARATED = 'Separated',
    WIDOWED = 'Widowed',
}

export enum GenderType {
    FEMALE = 'Female',
    MALE = 'Male',
}

export enum CancelledByType {
    DOCTOR = 'patient',
    PATIENT = 'doctor',
}

export enum ConsultationType {
    HOME_VISIT = 'Home Visit',
    ONLINE_CONSULTATION = 'Online Consultation',
    CLINIC = 'Clinic',
    ALL = 'All',
}

export enum DiscountType {
    PERCENTAGE = 'Percentage',
    FIXED = 'Fixed',
}

export enum RequestUpdateType {
    UPDATE_STATUS = 'Update Status',
    UPDATE_PAYMENT = 'Update Payment',
    UPDATE_FIELDS = 'Update Fields',
}

export enum PaymentMethod {
    CASH = 'Cash',
    CREDIT_CARD = 'Credit Card',
}

export enum WorkingDaysType {
    SATURDAY = 'Saturday',
    SUNDAY = 'Sunday',
    MONDAY = 'Monday',
    TUESDAY = 'Tuesday',
    WEDNESDAY = 'Wednesday',
    THURSDAY = 'Thursday',
    FRIDAY = 'Friday',
}

export enum Languages {
    AR = 'ar',
    EN = 'en',
    FR = 'fr',
    ES = 'es',
    IT = 'it',
    DE = 'de',
}

export enum FirebaseTopics {
    ALL = 'ALL',
    PATIENTS = 'PATIENT',
    DOCTORS = 'DOCTOR',
    SERVICE_PROVIDERS = 'SERVICE_PROVIDER',
    LANG_EN = 'LANG_EN',
    LANG_AR = 'LANG_AR',
}

export enum CallBackType {
    TOKEN = 'TOKEN',
    TRANSACTION = 'TRANSACTION',
}

export enum SyndicateIdStatus {
    NOT_APPLIED = 'none',
    NEW = 'new',
    APPROVED = 'approved',
    REJECTED = 'rejected',
}

export enum TransactionKind {
    REQUEST_FEE = 'REQUEST_FEE',
    KASHF_FEE = 'KASHF_FEE',
    DONATION_FEE = 'DONATION_FEE',
    BOOKING_FEE = 'BOOKING_FEE',
}

export enum AttachementType {
    PRESCRIPTION = 'PRESCRIPTION',
    LAP_TEST = 'LAP_TEST',
    X_RAY = 'X_RAY',
}

export enum VerificationMessages {
    SEND_SUCCESS = 'Verification code sent successfully',
    VERIFY_SUCCESS = 'Verification successful',
    INVALID_PHONE = 'Invalid phone number format',
    SEND_FAILED = 'Failed to send verification code',
    VERIFY_FAILED = 'Failed to verify code',
}

export enum BookingStatus {
    AWAITING_DATE_CONFIRMATION = 'awaiting_date_confirmation',
    PENDING = 'pending',
    CONFIRMED = 'confirmed',
    COMPLETED = 'completed',
    CANCELED = 'canceled',
    PENDINGPAYMENT = 'pending payment',
    PAID = 'paid',
}
