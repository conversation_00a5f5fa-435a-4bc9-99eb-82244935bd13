import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsNumber, IsString } from 'class-validator';

export class CreateOperationsResponse {
    @ApiProperty()
    @IsBoolean()
    @Expose()
    isSuccessful: boolean;

    @ApiProperty()
    @IsString()
    @Expose()
    message: string;

    @ApiProperty()
    @IsNumber()
    @Expose()
    createdId: number;
}
