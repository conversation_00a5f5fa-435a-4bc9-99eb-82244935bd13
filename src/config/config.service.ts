import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as Jo<PERSON> from 'joi';
import * as Path from 'path';

import { UserSubscriber } from '../shared/entity-subscribers/user-subscriber';
import { SnakeNamingStrategy } from '../snake-naming.strategy';

export interface IEnvConfig {
    [key: string]: string;
}

export class ConfigService {
    private readonly ENV_CONFIG: IEnvConfig;
    DATABASE_USER: string;
    DATABASE_PASSWORD: string;
    DATABASE_NAME: string;
    DATABASE_PORT: string;
    DATABASE_HOST: string;
    DATABASE_TYPE: string;
    JWT_ACCESS_TOKEN_SECRET_KEY: string;
    JWT_ACCESS_TOKEN_EXPIRES_IN: string;
    JWT_REFRESH_TOKEN_EXPIRES_IN: string;
    JWT_REFRESH_TOKEN_SECRET_KEY: string;
    NODE_ENV: string;
    FALLBACK_LANGUAGE: string;
    PORT: string;
    AWS_REGION: string;
    AWS_ACCESS_KEY_ID: string;
    AWS_SECRET_ACCESS_KEY: string;
    AWS_EXPIRATION_TIME_IN_MINUTES: number;
    AWS_S3_BUCKET_NAME: string;
    AGORA_APP_ID: string;
    AGORA_CERTIFICATE: string;
    TYPEORM_SYNCHRONIZE: string;
    TYPEORM_MIGRATIONS: string;
    DOCTORS_CONSULTATION_DURATION_IN_MINUTES: number;
    FIREBASE_CLIENT_EMAIL: string;
    FIREBASE_PRIVATE_KEY: string;
    FIREBASE_PROJECT_ID: string;
    PAYMOB_API_KEY: string;
    PAYMOB_IFRAME: string;
    PAYMOB_BASE_URL: string;
    PAYMOB_INTEGRATIONID: string;
    EMAIL: string;
    EMAIL_PASSWORD: string;
    SERVER_URL: string;
    SERVICE_PROVIDER_URL: string;
    SERVICE_PROVIDER_RESET_PASSWORD_API: string;
    TWILIO_SID: string;
    TWILIO_AUTH_TOKEN: string;
    TWILIO_VERIFY_SID: string;
    PAYMOB_HMAC: string;

    constructor(environment: string) {
        const filePath = `./env/${environment}.env`;
        if (fs.existsSync(filePath)) {
            const config = dotenv.parse(fs.readFileSync(filePath));
            this.ENV_CONFIG = this.validateInput(config);
        }
        this.initializeVariables();
    }

    private validateInput(envConfig: IEnvConfig): IEnvConfig {
        const envVarsSchema = Joi.object({
            NODE_ENV: Joi.string().default('development'),
            DATABASE_PORT: Joi.number().default(3000),
            DATABASE_USER: Joi.string().required(),
            DATABASE_PASSWORD: Joi.string().required(),
            DATABASE_NAME: Joi.string().required(),
            DATABASE_HOST: Joi.string().required(),
            DATABASE_TYPE: Joi.string().required(),
            JWT_ACCESS_TOKEN_SECRET_KEY: Joi.string().required(),
            JWT_ACCESS_TOKEN_EXPIRES_IN: Joi.string().required(),
            JWT_REFRESH_TOKEN_EXPIRES_IN: Joi.string().required(),
            JWT_REFRESH_TOKEN_SECRET_KEY: Joi.string().required(),
            FALLBACK_LANGUAGE: Joi.string().required(),
            PORT: Joi.string().required(),
            AWS_REGION: Joi.string().required(),
            AWS_ACCESS_KEY_ID: Joi.string().required(),
            AWS_SECRET_ACCESS_KEY: Joi.string().required(),
            AWS_EXPIRATION_TIME_IN_MINUTES: Joi.string().default('10'),
            AWS_S3_BUCKET_NAME: Joi.string().required(),
            AGORA_APP_ID: Joi.string().required(),
            AGORA_CERTIFICATE: Joi.string().required(),
            TYPEORM_SYNCHRONIZE: Joi.string().required(),
            TYPEORM_MIGRATIONS: Joi.string().required(),
            DOCTORS_CONSULTATION_DURATION_IN_MINUTES: Joi.string().required(),
            FIREBASE_CLIENT_EMAIL: Joi.string().required(),
            FIREBASE_PRIVATE_KEY: Joi.string().required(),
            FIREBASE_PROJECT_ID: Joi.string().required(),
            PAYMOB_API_KEY: Joi.string().required(),
            PAYMOB_IFRAME: Joi.string().required(),
            PAYMOB_BASE_URL: Joi.string().required(),
            PAYMOB_INTEGRATIONID: Joi.string().required(),
            PAYMOB_HMAC: Joi.string().required(),
            EMAIL: Joi.string().required(),
            EMAIL_PASSWORD: Joi.string().required(),
            SERVER_URL: Joi.string().required(),
            SERVICE_PROVIDER_URL: Joi.string().required(),
            SERVICE_PROVIDER_RESET_PASSWORD_API: Joi.string().required(),
            TWILIO_VERIFY_SID: Joi.string().required(),
            TWILIO_AUTH_TOKEN: Joi.string().required(),
            TWILIO_SID: Joi.string().required(),
        });

        const { error, value: validatedEnvConfig } =
            envVarsSchema.validate(envConfig);
        if (error) {
            throw new Error(`Config validation error: ${error.message}`);
        }

        return validatedEnvConfig;
    }

    // eslint-disable-next-line complexity
    private initializeVariables(): void {
        this.NODE_ENV = this.ENV_CONFIG
            ? this.ENV_CONFIG.NODE_ENV
            : process.env.NODE_ENV;
        this.DATABASE_PASSWORD = this.ENV_CONFIG
            ? this.ENV_CONFIG.DATABASE_PASSWORD
            : process.env.DATABASE_PASSWORD;
        this.DATABASE_PORT = this.ENV_CONFIG
            ? this.ENV_CONFIG.DATABASE_PORT
            : process.env.DATABASE_PORT;
        this.DATABASE_USER = this.ENV_CONFIG
            ? this.ENV_CONFIG.DATABASE_USER
            : process.env.DATABASE_USER;
        this.DATABASE_NAME = this.ENV_CONFIG
            ? this.ENV_CONFIG.DATABASE_NAME
            : process.env.DATABASE_NAME;
        this.DATABASE_HOST = this.ENV_CONFIG
            ? this.ENV_CONFIG.DATABASE_HOST
            : process.env.DATABASE_HOST;
        this.DATABASE_TYPE = this.ENV_CONFIG
            ? this.ENV_CONFIG.DATABASE_TYPE
            : process.env.DATABASE_TYPE;
        this.JWT_ACCESS_TOKEN_SECRET_KEY = this.ENV_CONFIG
            ? this.ENV_CONFIG.JWT_ACCESS_TOKEN_SECRET_KEY
            : process.env.JWT_ACCESS_TOKEN_SECRET_KEY;
        this.JWT_ACCESS_TOKEN_EXPIRES_IN = this.ENV_CONFIG
            ? this.ENV_CONFIG.JWT_ACCESS_TOKEN_EXPIRES_IN
            : process.env.JWT_ACCESS_TOKEN_EXPIRES_IN;
        this.JWT_REFRESH_TOKEN_EXPIRES_IN = this.ENV_CONFIG
            ? this.ENV_CONFIG.JWT_REFRESH_TOKEN_EXPIRES_IN
            : process.env.JWT_REFRESH_TOKEN_EXPIRES_IN;
        this.JWT_REFRESH_TOKEN_SECRET_KEY = this.ENV_CONFIG
            ? this.ENV_CONFIG.JWT_REFRESH_TOKEN_SECRET_KEY
            : process.env.JWT_REFRESH_TOKEN_SECRET_KEY;
        this.FALLBACK_LANGUAGE = this.ENV_CONFIG
            ? this.ENV_CONFIG.FALLBACK_LANGUAGE
            : process.env.FALLBACK_LANGUAGE;
        this.PORT = this.ENV_CONFIG ? this.ENV_CONFIG.PORT : process.env.PORT;
        this.AWS_S3_BUCKET_NAME = this.ENV_CONFIG
            ? this.ENV_CONFIG.AWS_S3_BUCKET_NAME
            : process.env.AWS_S3_BUCKET_NAME;
        this.AWS_REGION = this.ENV_CONFIG
            ? this.ENV_CONFIG.AWS_REGION
            : process.env.AWS_REGION;
        this.AWS_ACCESS_KEY_ID = this.ENV_CONFIG
            ? this.ENV_CONFIG.AWS_ACCESS_KEY_ID
            : process.env.AWS_ACCESS_KEY_ID;
        this.AWS_SECRET_ACCESS_KEY = this.ENV_CONFIG
            ? this.ENV_CONFIG.AWS_SECRET_ACCESS_KEY
            : process.env.AWS_SECRET_ACCESS_KEY;
        this.AWS_EXPIRATION_TIME_IN_MINUTES = parseInt(
            this.ENV_CONFIG
                ? this.ENV_CONFIG.AWS_EXPIRATION_TIME_IN_MINUTES
                : process.env.AWS_EXPIRATION_TIME_IN_MINUTES,
            10,
        );
        this.AGORA_CERTIFICATE = this.ENV_CONFIG
            ? this.ENV_CONFIG.AGORA_CERTIFICATE
            : process.env.AGORA_CERTIFICATE;
        this.AGORA_APP_ID = this.ENV_CONFIG
            ? this.ENV_CONFIG.AGORA_APP_ID
            : process.env.AGORA_APP_ID;
        this.TYPEORM_MIGRATIONS = this.ENV_CONFIG
            ? this.ENV_CONFIG.TYPEORM_MIGRATIONS
            : process.env.TYPEORM_MIGRATIONS;
        this.TYPEORM_SYNCHRONIZE = this.ENV_CONFIG
            ? this.ENV_CONFIG.TYPEORM_SYNCHRONIZE
            : process.env.TYPEORM_SYNCHRONIZE;
        this.DOCTORS_CONSULTATION_DURATION_IN_MINUTES = parseInt(
            this.ENV_CONFIG
                ? this.ENV_CONFIG.DOCTORS_CONSULTATION_DURATION_IN_MINUTES
                : process.env.DOCTORS_CONSULTATION_DURATION_IN_MINUTES,
            10,
        );
        this.FIREBASE_CLIENT_EMAIL = this.ENV_CONFIG
            ? this.ENV_CONFIG.FIREBASE_CLIENT_EMAIL
            : process.env.FIREBASE_CLIENT_EMAIL;
        this.FIREBASE_PRIVATE_KEY = this.ENV_CONFIG
            ? this.ENV_CONFIG.FIREBASE_PRIVATE_KEY
            : process.env.FIREBASE_PRIVATE_KEY;
        this.FIREBASE_PROJECT_ID = this.ENV_CONFIG
            ? this.ENV_CONFIG.FIREBASE_PROJECT_ID
            : process.env.FIREBASE_PROJECT_ID;
        this.PAYMOB_API_KEY = this.ENV_CONFIG
            ? this.ENV_CONFIG.PAYMOB_API_KEY
            : process.env.PAYMOB_API_KEY;
        this.PAYMOB_IFRAME = this.ENV_CONFIG
            ? this.ENV_CONFIG.PAYMOB_IFRAME
            : process.env.PAYMOB_IFRAME;
        this.PAYMOB_BASE_URL = this.ENV_CONFIG
            ? this.ENV_CONFIG.PAYMOB_BASE_URL
            : process.env.PAYMOB_BASE_URL;
        this.PAYMOB_INTEGRATIONID = this.ENV_CONFIG
            ? this.ENV_CONFIG.PAYMOB_INTEGRATIONID
            : process.env.PAYMOB_INTEGRATIONID;
        this.PAYMOB_HMAC = this.ENV_CONFIG
            ? this.ENV_CONFIG.PAYMOB_HMAC
            : process.env.PAYMOB_HMAC;
        this.EMAIL = this.ENV_CONFIG
            ? this.ENV_CONFIG.EMAIL
            : process.env.EMAIL;
        this.EMAIL_PASSWORD = this.ENV_CONFIG
            ? this.ENV_CONFIG.EMAIL_PASSWORD
            : process.env.EMAIL_PASSWORD;
        this.SERVER_URL = this.ENV_CONFIG
            ? this.ENV_CONFIG.SERVER_URL
            : process.env.SERVER_URL;
        this.SERVICE_PROVIDER_URL = this.ENV_CONFIG
            ? this.ENV_CONFIG.SERVICE_PROVIDER_URL
            : process.env.SERVICE_PROVIDER_URL;
        this.SERVICE_PROVIDER_RESET_PASSWORD_API = this.ENV_CONFIG
            ? this.ENV_CONFIG.SERVICE_PROVIDER_RESET_PASSWORD_API
            : process.env.SERVICE_PROVIDER_RESET_PASSWORD_API;

        this.TWILIO_SID = this.ENV_CONFIG
            ? this.ENV_CONFIG.TWILIO_SID
            : process.env.TWILIO_SID;

        this.TWILIO_VERIFY_SID = this.ENV_CONFIG
            ? this.ENV_CONFIG.TWILIO_VERIFY_SID
            : process.env.TWILIO_VERIFY_SID;

        this.TWILIO_AUTH_TOKEN = this.ENV_CONFIG
            ? this.ENV_CONFIG.TWILIO_AUTH_TOKEN
            : process.env.TWILIO_AUTH_TOKEN;
    }

    get isDevelopment(): boolean {
        return this.NODE_ENV === 'development';
    }

    private isTrue(value): boolean {
        return value === 'true';
    }

    get typeOrmConfig(): TypeOrmModuleOptions {
        return {
            type: 'postgres',
            host: this.DATABASE_HOST,
            port: +this.DATABASE_PORT,
            username: this.DATABASE_USER,
            password: this.DATABASE_PASSWORD,
            database: this.DATABASE_NAME,
            subscribers: [UserSubscriber],
            migrationsRun: this.isTrue(this.TYPEORM_MIGRATIONS),
            logging: true,
            namingStrategy: new SnakeNamingStrategy(),
            autoLoadEntities: true,
            synchronize: this.isTrue(this.TYPEORM_SYNCHRONIZE),
            entities: [
                Path.join(__dirname, '/../modules/**/*.entity{.ts,.js}'),
            ],
            migrations: [Path.join(__dirname, '/../migration/*.js')],
            migrationsTableName: 'migrations',
            // cli: {
            //     migrationsDir: 'src/migrations',
            // },
        };
    }
}
