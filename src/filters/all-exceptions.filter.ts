/* eslint-disable @typescript-eslint/tslint/config */
import {
    ArgumentsHost,
    Catch,
    ExceptionFilter,
    HttpException,
    HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request, Response } from 'express';
import * as fs from 'fs';

import { ICustomHttpExceptionResponse } from '../interfaces/ICustomHttpExceptionResponse';
import { IHttpExceptionResponse } from '../interfaces/IHttpExceptionResponse';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
    constructor(public reflector: Reflector) {}
    catch(exception: unknown, host: ArgumentsHost): void {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();

        let status: HttpStatus;
        let errorMessage: string;

        if (exception instanceof HttpException) {
            status = exception.getStatus();
            errorMessage =
                (exception.getResponse() as IHttpExceptionResponse).error ||
                exception.message;
        } else {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            errorMessage = 'Critical internal server error occurred!';
        }

        const errorResponse = this.getErrorResponse(
            status,
            errorMessage,
            request,
        );
        const errorLog = this.getErrorLog(errorResponse, request, exception);
        this.writeErrorLogToFile(errorLog);
        response.status(status).json(errorResponse);
    }

    private getErrorResponse = (
        status: HttpStatus,
        errorMessage: string,
        request: Request,
    ): ICustomHttpExceptionResponse => ({
        statusCode: status,
        error: errorMessage,
        path: request.url,
        method: request.method,
        timeStamp: new Date(),
    });

    private getErrorLog = (
        errorResponse: ICustomHttpExceptionResponse,
        request: Request,
        exception: unknown,
    ): string => {
        const { statusCode, error } = errorResponse;
        const { method, url } = request;
        return `Response Code: ${statusCode} - Method: ${method} - URL: ${url}\n\n
            ${JSON.stringify(errorResponse)}\n\n
            User: ${JSON.stringify(request.user ?? 'Not signed in')}\n\n
            ${
                exception instanceof HttpException ? exception.stack : error
            }\n\n`;
    };

    private writeErrorLogToFile = (errorLog: string): void => {
        fs.appendFile('error.log', errorLog, 'utf8', (err) => {
            if (err != null) {
                throw err;
            }
        });
    };
}
