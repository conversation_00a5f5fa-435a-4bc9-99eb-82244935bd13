import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { PaymobHmacGuardGuard } from './paymob-hmac-guard.guard';
import { ConfigService } from '../../config/config.service';
import * as crypto from 'crypto';

describe('PaymobHmacGuardGuard', () => {
    let guard: PaymobHmacGuardGuard;
    let configService: ConfigService;

    // Mock data
    const mockHmacKey = 'test-hmac-key';
    const mockPayload = JSON.stringify({ test: 'data' });
    const mockPayloadBuffer = Buffer.from(mockPayload);

    // Create a valid signature for testing
    const createValidSignature = (payload: Buffer): string =>
        crypto.createHmac('sha512', mockHmacKey).update(payload).digest('hex');

    // Mock ConfigService
    const mockConfigService = {
        PAYMOB_HMAC: mockHmacKey,
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                {
                    provide: PaymobHmacGuardGuard,
                    useFactory: () =>
                        new PaymobHmacGuardGuard(
                            mockConfigService as ConfigService,
                        ),
                },
                {
                    provide: ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();

        guard = module.get<PaymobHmacGuardGuard>(PaymobHmacGuardGuard);
        configService = module.get<ConfigService>(ConfigService);
    });

    it('should be defined', () => {
        expect(guard).toBeDefined();
    });

    describe('canActivate', () => {
        let mockContext: ExecutionContext;
        let mockRequest: any;
        let validSignature: string;

        beforeEach(() => {
            // Create a valid signature for our test payload
            validSignature = createValidSignature(mockPayloadBuffer);

            // Setup mock request with raw body handling
            mockRequest = {
                headers: {
                    hmac: validSignature,
                },
                rawBody: mockPayloadBuffer,
                body: {},
            };

            // Setup mock context
            mockContext = {
                switchToHttp: jest.fn().mockReturnValue({
                    getRequest: jest.fn().mockReturnValue(mockRequest),
                }),
            } as unknown as ExecutionContext;
        });

        it('should return true for valid HMAC signature', async () => {
            const isResult = await guard.canActivate(mockContext);
            expect(isResult).toBe(true);
            // Verify body was parsed
            expect(mockRequest.body).toEqual(JSON.parse(mockPayload));
        });

        it('should throw HttpException when HMAC signature is missing', async () => {
            // Remove the signature
            mockRequest.headers.hmac = undefined;

            await expect(guard.canActivate(mockContext)).rejects.toThrow(
                new HttpException(
                    'Missing Paymob HMAC signature',
                    HttpStatus.UNAUTHORIZED,
                ),
            );
        });

        it('should throw HttpException when HMAC signature is invalid', async () => {
            // Set an invalid signature
            mockRequest.headers.hmac = 'invalid-signature';

            await expect(guard.canActivate(mockContext)).rejects.toThrow(
                new HttpException(
                    'Invalid Paymob signature',
                    HttpStatus.UNAUTHORIZED,
                ),
            );
        });

        it('should handle raw body when not already available', async () => {
            // Mock a request without rawBody but with a stream
            const mockStream = {
                on: jest.fn((event, callback) => {
                    if (event === 'data') {
                        callback(mockPayloadBuffer);
                    }
                    if (event === 'end') {
                        callback();
                    }
                    return mockStream;
                }),
                pipe: jest.fn().mockReturnThis(),
            };

            const requestWithoutRawBody = {
                headers: {
                    hmac: validSignature,
                },
                on: mockStream.on,
                pipe: mockStream.pipe,
            };

            const contextWithoutRawBody = {
                switchToHttp: jest.fn().mockReturnValue({
                    getRequest: jest
                        .fn()
                        .mockReturnValue(requestWithoutRawBody),
                }),
            } as unknown as ExecutionContext;

            // Mock the getRawBody method to return our payload
            jest.spyOn(guard as any, 'getRawBody').mockResolvedValue(
                mockPayloadBuffer,
            );

            const isResult = await guard.canActivate(contextWithoutRawBody);
            expect(isResult).toBe(true);
            expect(guard.getRawBody).toHaveBeenCalledWith(
                requestWithoutRawBody,
            );
        });

        it('should throw HttpException for general errors', async () => {
            // Mock getRawBody to throw a generic error
            jest.spyOn(guard as any, 'getRawBody').mockImplementation(() => {
                throw new Error('Some unexpected error');
            });

            await expect(guard.canActivate(mockContext)).rejects.toThrow(
                new HttpException('Invalid request', HttpStatus.BAD_REQUEST),
            );
        });
    });

    describe('verifyPaymobSignature', () => {
        it('should return true for valid signature', () => {
            const validSignature = createValidSignature(mockPayloadBuffer);
            const result = guard.verifyPaymobSignature(
                mockPayloadBuffer,
                validSignature,
            );
            expect(result).toBe(true);
        });

        it('should return false for invalid signature', () => {
            const result = guard.verifyPaymobSignature(
                mockPayloadBuffer,
                'invalid-signature',
            );
            expect(result).toBe(false);
        });

        it('should return false when an error occurs during verification', () => {
            // Mock crypto.timingSafeEqual to throw an error
            jest.spyOn(crypto, 'timingSafeEqual').mockImplementation(() => {
                throw new Error('Crypto error');
            });

            const validSignature = createValidSignature(mockPayloadBuffer);
            const result = guard.verifyPaymobSignature(
                mockPayloadBuffer,
                validSignature,
            );
            expect(result).toBe(false);
        });
    });

    describe('getRawBody', () => {
        it('should return existing rawBody if available', async () => {
            const req = { rawBody: mockPayloadBuffer };
            const result = await guard.getRawBody(req);
            expect(result).toBe(mockPayloadBuffer);
        });

        it('should read and store rawBody if not available', async () => {
            // Create a request without rawBody
            const req = {
                on: jest.fn(),
                pipe: jest.fn(),
            };

            // Mock the raw-body module
            jest.mock('raw-body');
            const rawBody = require('raw-body');
            rawBody.mockResolvedValue(mockPayloadBuffer);

            // Create a spy on the original method but call through
            const getRawBodySpy = jest.spyOn(guard as any, 'getRawBody');

            // Create a custom implementation that sets the rawBody property
            getRawBodySpy.mockImplementation(async (request: any) => {
                request.rawBody = mockPayloadBuffer;
                return mockPayloadBuffer;
            });

            try {
                const result = await guard.getRawBody(req);
                expect(result).toEqual(mockPayloadBuffer);
                expect(req).toHaveProperty('rawBody', mockPayloadBuffer);
            } finally {
                // Restore the original spy
                getRawBodySpy.mockRestore();
            }
        });
    });

    describe('Custom header name', () => {
        it('should use custom header name when provided', async () => {
            // Create a guard with custom header name
            const customHeaderGuard = new PaymobHmacGuardGuard(
                mockConfigService as ConfigService,
                'X-Custom-Hmac',
            );

            // Setup mock request with custom header
            const validSignature = createValidSignature(mockPayloadBuffer);
            const mockReq = {
                headers: {
                    'x-custom-hmac': validSignature,
                },
                rawBody: mockPayloadBuffer,
                body: {},
            };

            const mockCtx = {
                switchToHttp: jest.fn().mockReturnValue({
                    getRequest: jest.fn().mockReturnValue(mockReq),
                }),
            } as unknown as ExecutionContext;

            // Mock the getRawBody method to avoid actual HTTP request processing
            jest.spyOn(
                customHeaderGuard as any,
                'getRawBody',
            ).mockResolvedValue(mockPayloadBuffer);

            // Mock the verifyPaymobSignature method to return true
            jest.spyOn(
                customHeaderGuard as any,
                'verifyPaymobSignature',
            ).mockReturnValue(true);

            const isResult = await customHeaderGuard.canActivate(mockCtx);
            expect(isResult).toBe(true);
            expect(mockReq.body).toEqual(JSON.parse(mockPayload));

            // Verify that the correct header was used
            expect(
                customHeaderGuard.verifyPaymobSignature,
            ).toHaveBeenCalledWith(mockPayloadBuffer, validSignature);
        });
    });
});
