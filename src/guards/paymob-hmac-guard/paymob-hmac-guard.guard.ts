import {
    Injectable,
    CanActivate,
    ExecutionContext,
    HttpException,
    HttpStatus,
} from '@nestjs/common';
import * as crypto from 'crypto';
import * as rawBody from 'raw-body';
import { ConfigService } from '../../config/config.service';

@Injectable()
export class PaymobHmacGuardGuard implements CanActivate {
    constructor(
        private readonly configService: ConfigService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();

        try {
            // Get the raw request body
            const rawRequestBody = await this.getRawBody(request);

            // Extract the Paymob HMAC signature from headers
            const signature = request.headers[
                'Hmac'.toLowerCase()
            ] as string;

            if (!signature) {
                throw new HttpException(
                    'Missing Paymob HMAC signature',
                    HttpStatus.UNAUTHORIZED,
                );
            }

            // Verify the HMAC signature
            const isValid = this.verifyPaymobSignature(
                rawRequestBody,
                signature,
            );

            if (!isValid) {
                throw new HttpException(
                    'Invalid Paymob signature',
                    HttpStatus.UNAUTHORIZED,
                );
            }

            // Parse the body for controllers to use
            request.body = JSON.parse(rawRequestBody.toString());

            return true;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException('Invalid request', HttpStatus.BAD_REQUEST);
        }
    }

    private async getRawBody(req: any): Promise<Buffer> {
        // If the raw body is already available, return it
        if (req.rawBody) {
            return req.rawBody;
        }

        // Otherwise, read the raw body
        const body = await rawBody(req);

        // Store the raw body for potential reuse
        req.rawBody = body;

        return body;
    }

    private verifyPaymobSignature(payload: Buffer, signature: string): boolean {
        try {
            // Calculate the expected HMAC signature for Paymob
            // Paymob uses SHA512 with hex encoding
            const expectedSignature = crypto
                .createHmac('sha512', this.configService.PAYMOB_HMAC)
                .update(payload)
                .digest('hex');

            // Perform a constant-time comparison to prevent timing attacks
            return crypto.timingSafeEqual(
                Buffer.from(expectedSignature),
                Buffer.from(signature),
            );
        } catch (error) {
            console.error('Error verifying Paymob signature:', error);
            return false;
        }
    }
}
