import {
    HttpException,
    HttpStatus,
    NestInterceptor,
    Type,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';

interface ILocalFileProps {
    destination: string;
    fieldName: string;
    filter: string | RegExp;
    limitFileSize?: number;
}

export function localFileInterceptor({
    filter,
    destination,
    fieldName,
    limitFileSize,
}: ILocalFileProps): Type<NestInterceptor<any, any>> {
    return FileInterceptor(fieldName, {
        storage: diskStorage({
            destination,
            filename: (
                req: any,
                file: { originalname: string },
                cb: (arg0: any, arg1: string) => any,
            ) => {
                const randomName = Array(32)
                    .fill(null)
                    .map(() => Math.round(Math.random() * 16).toString(16))
                    .join('');
                return cb(null, `${randomName}${extname(file.originalname)}`);
            },
        }),
        fileFilter: (req, file, cb) => {
            // Check file type (you can add more file type checks)
            // eslint-disable-next-line @typescript-eslint/prefer-regexp-exec
            if (!file.originalname.match(filter)) {
                return cb(
                    new HttpException(
                        'Unsupported file type',
                        HttpStatus.BAD_REQUEST,
                    ),
                    false,
                );
            }
            cb(null, true);
        },
        limits: {
            fileSize: limitFileSize ?? 1024 * 1024, // 1 MB limit
        },
    });
}
