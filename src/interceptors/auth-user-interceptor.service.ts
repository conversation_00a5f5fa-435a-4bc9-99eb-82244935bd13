import {
    <PERSON><PERSON><PERSON><PERSON>,
    ExecutionContext,
    Injectable,
    NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';

import { AuthService } from '../modules/auth/auth.service';
import { UserEntity } from '../modules/users/entities/user.entity';

@Injectable()
export class AuthUserInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();

        const user = <UserEntity>request.user;
        AuthService.setAuthUser(user);

        return next.handle();
    }
}
