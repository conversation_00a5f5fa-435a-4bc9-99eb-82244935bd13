/* eslint-disable complexity */
import {
    <PERSON><PERSON><PERSON><PERSON>,
    ExecutionContext,
    Injectable,
    NestInterceptor,
} from '@nestjs/common';
import { Request } from 'express';
import { Observable } from 'rxjs';

import { SourceType } from '../common/constants/types';
import { AvailableLanguageCodes } from '../i18n/languageCodes';
@Injectable()
export class HeaderInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request: Request = context.switchToHttp().getRequest();

        if (
            !request.headers['accept-language'] ||
            !AvailableLanguageCodes[request.headers['accept-language']]
        ) {
            request.headers['accept-language'] = AvailableLanguageCodes.ar;
        }
        if (!request.headers['source-type']) {
            request.headers['source-type'] = SourceType.PATIENT;
        }

        if (request && request.user && request.user.appLanguage) {
            request.headers['accept-language'] = request.user.appLanguage;
        }
        return next.handle();
    }
}
