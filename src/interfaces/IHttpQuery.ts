/* eslint-disable max-len */
'use strict';

import {
    FilterByType,
    FilterType,
    SortByType,
    SortingType,
} from '../common/constants/types';

export interface IFilter {
    type: FilterType; // "FIXED", "RANGE" or "MULTIPLE"
    by: FilterByType; // "speciality", "grade", "gender", "rating", "home", "online", "clinic", "date"
    value?: string; // "1", "5", "MALE", "Thu Nov 19 2020 04:42:19 GMT+0000", "Online or Offline"
    values?: string[];
    min?: number; // 10
    max?: number; // 100
}

export interface IHttpQuery {
    search?: {
        by: string; // name
        value: string; // "nader"
    };
    sort?: {
        by: SortByType; // "popularity", "rating" or "fees"
        type: SortingType; // "ASC" or "DESC"
    };
    pagination?: {
        page: number; // not implemented yet
        perPage: number; // not implemented yet
    };
    filters?: IFilter[];
}

export interface IPaginatedResponse<T> {
    data: T[];
    meta: {
        total: number;
        page: number;
        lastPage: number;
        perPage: number;
    };
}

/*
Examples:

-> search: ?query={"search":{"by":"name","value":"sol"}}

-> sort:
    ?query={"sort": {"by":"popularity","type":"ASC"}}
    ?query={"sort": {"by":"fees","type":"DESC"}}
    ?query={"sort": {"by":"rating","type":"ASC"}}

-> filters:
    ?query={"filters":[{"type":"FIXED","by":"speciality","value":"2"}]}
    ?query={"filters":[{"type":"FIXED","by":"speciality","value":"2"},{"type":"FIXED","by":"gender","value":"Female"}]}
    ?query={"filters": [{"type": "FIXED","by": "speciality","value": "2"},{"type":"FIXED","by":"gender","value":"Male"},{"type":"RANGE","by":"gender","min":0, "max": 1}]}
    ?query={"filters":[{"type": "MULTIPLE", "by": "languages", "values": ["arabic"]}]}
*/
