import { ClassSerializerInterceptor, ValidationPipe } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import {
    ExpressAdapter,
    NestExpressApplication,
} from '@nestjs/platform-express';
import * as compression from 'compression';
import * as express from 'express';
import RateLimit from 'express-rate-limit';
import * as admin from 'firebase-admin';
import { createWriteStream, readFileSync } from 'fs';
import helmet from 'helmet';
import * as morgan from 'morgan';
import { join } from 'path';
import {
    initializeTransactionalContext,
    patchTypeORMRepositoryWithBaseRepository,
} from 'typeorm-transactional-cls-hooked';

import { AppModule } from './app.module';
import { ConfigModule } from './config/config.module';
import { ConfigService } from './config/config.service';
import { AllExceptionsFilter } from './filters/all-exceptions.filter';
import { HttpExceptionFilter } from './filters/bad-request.filter';
import { QueryFailedFilter } from './filters/query-failed.filter';
import { SettingsModule } from './modules/settings/settings.module';
import { SettingsService } from './modules/settings/settings.service';
import { DebugLogger } from './shared/services/logger.service';
import { setupSwagger } from './viveo-swagger';
import * as bodyParser from 'body-parser';

const rawData = readFileSync('firebase.json').toString();
const serviceAccountKey = JSON.parse(rawData);

const logStream = createWriteStream('api.log', {
    flags: 'a',
});

const logger = new DebugLogger('Bootstrap');

async function bootstrap() {
    try {
        initializeTransactionalContext();
        patchTypeORMRepositoryWithBaseRepository();
        const app = await NestFactory.create<NestExpressApplication>(
            AppModule,
            new ExpressAdapter(),
            { cors: true, logger: ['error', 'warn', 'log'] },
        );

        app.enable('trust proxy'); // only if you're behind a reverse proxy (Heroku, Bluemix, AWS ELB, Nginx, etc)
        // disable contentSecurityPolicy to fix docs error
        app.use(
            helmet({
                contentSecurityPolicy: false,
            }),
        );

        app.use(
            RateLimit({
                windowMs: 60 * 60 * 1000, // 60 minutes
                max: 100000, // limit each IP to 1000 requests per windowMs
            }),
        );

        app.use(
            bodyParser.json({
                verify: (req: any, res, buf) => {
                    req.rawBody = buf;
                },
            }),
        );

        app.use(
            bodyParser.urlencoded({
                extended: true,
                verify: (req: any, res, buf) => {
                    req.rawBody = buf;
                },
            }),
        );

        app.use(compression());

        app.use(morgan('combined', { stream: logStream }));

        const reflector = app.get(Reflector);

        app.useGlobalFilters(
            new HttpExceptionFilter(reflector),
            new QueryFailedFilter(reflector),
            // eslint-disable-next-line new-parens
            new AllExceptionsFilter(reflector),
        );

        app.useGlobalInterceptors(new ClassSerializerInterceptor(reflector));

        app.useGlobalPipes(
            new ValidationPipe({
                whitelist: true,
                transform: true,
            }),
        );

        const configService = app.select(ConfigModule).get(ConfigService);

        setupSwagger(app);

        admin.initializeApp({
            credential: admin.credential.cert({
                projectId: serviceAccountKey.projectId,
                privateKey: serviceAccountKey.privateKey.replace(/\\n/g, '\n'),
                clientEmail: serviceAccountKey.clientEmail,
            }),
        });

        app.useStaticAssets(join(__dirname, '..', 'public'));
        app.setBaseViewsDir(join(__dirname, '..', 'views'));
        app.setViewEngine('hbs');
        app.use(express.static('views/images'));

        const settingsService = app.select(SettingsModule).get(SettingsService);
        await settingsService.get();

        const port = configService.PORT;
        await app.listen(port);

        logger.log(`🚀 Kashf Backend server running on port ${port} in development mode!`);
    } catch (err) {
        logger.error('Application startup failed' + err.message);
        logger.error('Application startup failed' + err.stack);
    }
}

void bootstrap();

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at: ' + JSON.stringify(promise));
    logger.error('Unhandled Rejection reason:' + JSON.stringify(reason));
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:' + error.message);
    logger.error('Uncaught Exception:' + error.stack);
});
