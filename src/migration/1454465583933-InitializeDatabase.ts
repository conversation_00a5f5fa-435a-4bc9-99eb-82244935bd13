import {MigrationInterface, QueryRunner} from "typeorm";

export class InitializeDatabase1454465583933 implements MigrationInterface {
    name = 'InitializeDatabase1454465583933'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "notifications_translations" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "title" character varying NOT NULL, "body" character varying NOT NULL, "language_code" character varying NOT NULL, "notification_id" integer, CONSTRAINT "PK_b8fa45f41a721874013cc1070af" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "notifications" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "data" json NOT NULL, "seen" boolean NOT NULL DEFAULT false, "user_id" integer, CONSTRAINT "PK_6a72c3c0f683f6462415e653c3a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "custom-notifications" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "arabic_text" character varying NOT NULL, "english_text" character varying NOT NULL, "audience" character varying NOT NULL, "service_provider_id" integer, CONSTRAINT "PK_9f7069e653289073a63a09641bf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "doctors_service_providers_check_in_logs" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "is_checked_in" boolean NOT NULL DEFAULT false, "doctor_id" integer, "service_provider_id" integer, CONSTRAINT "PK_a09d25529b80d56950a5f1cc1a0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "governorates" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying, "name_en" character varying, CONSTRAINT "PK_3b62bb4e44b53e3119d0a8648fa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "insurance_companies_translations" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "title" character varying NOT NULL, "language_code" character varying NOT NULL, "insurance_company_id" integer, CONSTRAINT "PK_0ccf86ce0e910686a49c708cbbd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "insurance_companies" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "logo_url" character varying NOT NULL, CONSTRAINT "PK_0a708efe35c240b2ea80c1148a2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "schedule_days_day_enum" AS ENUM('Saturday', 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday')`);
        await queryRunner.query(`CREATE TABLE "schedule_days" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "order" integer NOT NULL, "day" "schedule_days_day_enum" NOT NULL, "from" TIME NOT NULL DEFAULT '9:00 AM', "to" TIME NOT NULL DEFAULT '9:00 PM', "break_from" TIME, "break_to" TIME, "is_active" boolean NOT NULL DEFAULT false, "contains_break" boolean NOT NULL DEFAULT false, "schedule_id" integer, CONSTRAINT "PK_92e16541f328fb9658feccc082a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "schedules_type_enum" AS ENUM('Home Visit', 'Online Consultation', 'Clinic', 'All')`);
        await queryRunner.query(`CREATE TABLE "schedules" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "type" "schedules_type_enum", "is_enabled" boolean NOT NULL DEFAULT false, "fees" integer NOT NULL DEFAULT 0, "clinic_id" integer, "doctor_id" integer, "service_provider_id" integer, CONSTRAINT "REL_b723a982d8579f74107e715fdf" UNIQUE ("clinic_id"), CONSTRAINT "PK_7e33fc2ea755a5765e3564e66dd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_provider_types_translations" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "title" character varying NOT NULL, "language_code" character varying NOT NULL, "service_provider_type_id" integer, CONSTRAINT "PK_6bdc4278f0838b2b400a070a939" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_provider_types" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_dc344e5814966f0ffd8e39264ff" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_providers" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "coordinator_phone" character varying, "coordinator_name" character varying, "agree_to_terms_and_policy" boolean, "landline_phone" character varying NOT NULL, "area" character varying, "location" geometry(Point,4326), "website" character varying, "commercial_record" character varying, "tax_id" character varying, "kashf_percentage" numeric(16,2) DEFAULT 0, "is_active" boolean NOT NULL DEFAULT false, "docs" text, "governorate_id" integer, "city_id" integer, "user_id" integer, "service_provider_type_id" integer, CONSTRAINT "REL_2cc7c52b39288cadfad8a0ad63" UNIQUE ("user_id"), CONSTRAINT "PK_73c86f1298c5285d76e66da2da9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bill_transactions" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "transaction_id" character varying NOT NULL, "bank_name" character varying, "date" date NOT NULL, "receipt" character varying NOT NULL, "bill_id" integer, CONSTRAINT "PK_8d3ff91c04f0a0143b41ef05273" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "bills_status_enum" AS ENUM('NotSettled', 'Settled')`);
        await queryRunner.query(`CREATE TABLE "bills" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "status" "bills_status_enum" NOT NULL DEFAULT 'NotSettled', "start" date NOT NULL, "end" date NOT NULL, "kashf_percentage" numeric(16,2) DEFAULT 0, "online_consulation_revenue" numeric(16,2) DEFAULT 0, "home_visit_revenue" numeric(16,2) DEFAULT 0, "clinic_visit_revenue" numeric(16,2) DEFAULT 0, "gross_revenue" numeric(16,2) DEFAULT 0, "total_cash" numeric(16,2) DEFAULT 0, "total_credit" numeric(16,2) DEFAULT 0, "total" numeric(16,2) DEFAULT 0, "net_revenue" numeric(16,2) DEFAULT 0, "doctor_id" integer, "service_provider_id" integer, CONSTRAINT "PK_a56215dfcb525755ec832cc80b7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "paymob_transactions" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "status" character varying NOT NULL, "paymob_order_id" character varying NOT NULL, "paymob_transaction_id" integer, "callback_data" json, "transaction_id" integer, CONSTRAINT "PK_a938e7cc68f284bca59a1928351" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "transactions" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "status" character varying NOT NULL, "type" character varying, "amount" numeric(16,3), "kashf_percentage" numeric(16,2) DEFAULT 0, "refunded_amount" numeric(16,3), "doctor_id" integer, "patient_id" integer, "service_provider_id" integer, "bill_id" integer, "user_id" integer, "request_id" integer, CONSTRAINT "REL_d8bd6b0bf8b8c2684d35b6bc8c" UNIQUE ("request_id"), CONSTRAINT "PK_a219afd8dd77ed80f5a862f1db9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "medications_translations" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "title" character varying NOT NULL, "language_code" character varying NOT NULL, "medication_id" integer, CONSTRAINT "PK_0f1cdfb188efb33aba0691be035" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "medications" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_cdee49fe7cd79db13340150d356" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "patients_marital_status_enum" AS ENUM('Married', 'Single', 'Divorced', 'Separated', 'Widowed')`);
        await queryRunner.query(`CREATE TABLE "patients" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "date_of_birth" date, "blood_group" character varying, "marital_status" "patients_marital_status_enum", "is_smoking" boolean, "allergies" text array NOT NULL DEFAULT '{}'::text[], "chronic_diseases" text array NOT NULL DEFAULT '{}'::text[], "disabilities" text array NOT NULL DEFAULT '{}'::text[], "injuries" text array NOT NULL DEFAULT '{}'::text[], "surgeries" text array NOT NULL DEFAULT '{}'::text[], "is_active" boolean NOT NULL DEFAULT true, "user_id" integer, CONSTRAINT "REL_7fe1518dc780fd777669b5cb7a" UNIQUE ("user_id"), CONSTRAINT "PK_a7f0b9fcbb3469d5ec0b0aceaa7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "users_translations" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying NOT NULL, "about" character varying, "description" character varying, "language_code" character varying NOT NULL, "user_id" integer, CONSTRAINT "PK_a72a34f997f2df60f94440c41f0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_3a501173aedfbeb8d751beb167" ON "users_translations" ("name") `);
        await queryRunner.query(`CREATE TABLE "creditcards" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "card_number" character varying NOT NULL, "token" character varying NOT NULL, "card_type" character varying NOT NULL, "user_id" integer, CONSTRAINT "PK_731b9a6ceb9d0dc4f07feeae402" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "cities" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying, "name_en" character varying, "governorate_id" integer, CONSTRAINT "PK_4762ffb6e5d198cfec5606bc11e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "grades" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_4740fb6f5df2505a48649f1687b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "grades_translations" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "title" character varying NOT NULL, "language_code" character varying NOT NULL, "grade_id" integer, CONSTRAINT "PK_77a3349e70bbacf4d7f91d5f05b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "specialities" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_bff0e3b630c901aec7557343230" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "specialities_translations" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "title" character varying NOT NULL, "language_code" character varying NOT NULL, "speciality_id" integer, CONSTRAINT "PK_c0be82015351f1be668707da0b8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ratings" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "rating" double precision NOT NULL DEFAULT 0, "call_rating" double precision, "call_comment" character varying, "comment" character varying, "rated_user_id" integer, "rater_user_id" integer, "consultation_request_id" integer, CONSTRAINT "PK_0f31425b073219379545ad68ed9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "fcm_tokens" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "device_id" character varying NOT NULL, "fcm_token" character varying NOT NULL, "user_id" integer, CONSTRAINT "PK_0802a779d616597e9330bb9a7cc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "users_role_enum" AS ENUM('GUEST', 'ADMIN', 'SUPER_ADMIN', 'DOCTOR', 'PATIENT', 'SERVICE_PROVIDER')`);
        await queryRunner.query(`CREATE TYPE "users_gender_enum" AS ENUM('Female', 'Male')`);
        await queryRunner.query(`CREATE TABLE "users" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying, "firebase_user_id" character varying, "role" "users_role_enum" NOT NULL DEFAULT 'PATIENT', "accessibility" character varying, "email" character varying NOT NULL, "password" character varying, "phone" character varying NOT NULL, "migrated_user_id" integer, "avatar" character varying DEFAULT 'https://bit.ly/1nRvZRB', "app_language" character varying DEFAULT 'en', "refresh_token" character varying, "gender" "users_gender_enum", "average_rating" numeric(16,2) NOT NULL DEFAULT 0, "is_email_verified" boolean NOT NULL DEFAULT false, "is_deleted" boolean NOT NULL DEFAULT false, "is_active" boolean NOT NULL DEFAULT false, "verification_code" character varying, "total_consultations" integer NOT NULL DEFAULT 0, CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "favourites" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "doctor_id" integer, "user_id" integer, CONSTRAINT "PK_173e5d5cc35490bf1de2d2d3739" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "doctors_syndicate_id_status_enum" AS ENUM('none', 'new', 'approved', 'rejected')`);
        await queryRunner.query(`CREATE TYPE "doctors_status_enum" AS ENUM('Online', 'Offline', 'Busy')`);
        await queryRunner.query(`CREATE TABLE "doctors" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "national_id" character varying, "national_id_front_url" character varying, "national_id_back_url" character varying, "syndicate_number" character varying, "syndicate_id_url" character varying, "new_syndicate_id_url" character varying, "syndicate_id_status" "doctors_syndicate_id_status_enum" NOT NULL DEFAULT 'none', "tax_id_url" character varying, "link" character varying, "certificates" text, "languages" text, "home_visit_fees" integer NOT NULL DEFAULT 0, "online_consultation_fees" integer NOT NULL DEFAULT 0, "is_asap_home_visit" boolean NOT NULL DEFAULT false, "is_asap_calls" boolean NOT NULL DEFAULT false, "is_free_call" boolean NOT NULL DEFAULT false, "is_free_visit" boolean NOT NULL DEFAULT false, "is_accept_cash_onhome_visit" boolean NOT NULL DEFAULT false, "is_active" boolean NOT NULL DEFAULT false, "is_in_call" boolean NOT NULL DEFAULT false, "status" "doctors_status_enum" NOT NULL DEFAULT 'Offline', "bank_account_number" character varying, "bank_name" character varying, "beneficiary_name" character varying, "bank_swift_code" character varying, "bank_iban" character varying, "wallet_number" character varying, "wallet_provider" character varying, "website" character varying, "wallet_balance" numeric(16,2) DEFAULT 0, "kashf_percentage" numeric(16,2) DEFAULT 0, "grade_id" integer, "user_id" integer, CONSTRAINT "REL_653c27d1b10652eb0c7bbbc442" UNIQUE ("user_id"), CONSTRAINT "PK_8207e7889b50ee3695c2b8154ff" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "consultation_request_history_status_enum" AS ENUM('Started', 'Requested', 'Accepted', 'Pending Payment', 'Paid', 'Cancelled', 'Finished', 'Expired')`);
        await queryRunner.query(`CREATE TYPE "consultation_request_history_update_type_enum" AS ENUM('Update Status', 'Update Payment', 'Update Fields')`);
        await queryRunner.query(`CREATE TABLE "consultation_request_history" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "status" "consultation_request_history_status_enum" NOT NULL DEFAULT 'Requested', "update_type" "consultation_request_history_update_type_enum" NOT NULL, "consultation_request_id" integer, "created_by_id" integer, CONSTRAINT "PK_e212888cb8f749f2a49f3c26e9f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "consultation_requests_status_enum" AS ENUM('Started', 'Requested', 'Accepted', 'Pending Payment', 'Paid', 'Cancelled', 'Finished', 'Expired')`);
        await queryRunner.query(`CREATE TYPE "consultation_requests_type_enum" AS ENUM('Home Visit', 'Online Consultation', 'Clinic', 'All')`);
        await queryRunner.query(`CREATE TYPE "consultation_requests_other_patient_gender_enum" AS ENUM('Female', 'Male')`);
        await queryRunner.query(`CREATE TYPE "consultation_requests_payment_method_enum" AS ENUM('Cash', 'Credit Card')`);
        await queryRunner.query(`CREATE TYPE "consultation_requests_payment_status_enum" AS ENUM('Pending', 'Paid', 'Refunded')`);
        await queryRunner.query(`CREATE TABLE "consultation_requests" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "status" "consultation_requests_status_enum" NOT NULL DEFAULT 'Requested', "type" "consultation_requests_type_enum" NOT NULL, "other_patient_name" character varying, "other_patient_number" character varying, "other_patient_date_of_birth" date, "other_patient_gender" "consultation_requests_other_patient_gender_enum", "patient_notes" character varying, "date" date NOT NULL, "from" TIME, "duration" integer, "diagnosis" character varying, "patient_address" character varying, "location" geometry(Point,4326), "patient_location_address" character varying, "prescription" character varying, "doctor_notes" character varying, "doctor_notes_images" text, "agora_token" character varying, "code" character varying, "promo_code" character varying, "expired_at" TIMESTAMP, "deleted_at" TIMESTAMP, "canceled_at" TIMESTAMP, "accepted_at" TIMESTAMP, "started_at" TIMESTAMP, "finished_at" TIMESTAMP, "is_fee_collected" boolean NOT NULL DEFAULT false, "is_verified" boolean NOT NULL DEFAULT false, "is_free_request" boolean NOT NULL DEFAULT false, "for_other_patient" boolean NOT NULL DEFAULT false, "fees" numeric(16,2) NOT NULL DEFAULT 0, "kashf_percentage" numeric(16,2) DEFAULT 0, "discount_amount" numeric(16,2) DEFAULT 0, "payment_method" "consultation_requests_payment_method_enum", "payment_status" "consultation_requests_payment_status_enum" NOT NULL DEFAULT 'Pending', "doctor_id" integer, "patient_id" integer, "clinic_id" integer, "transaction_id" integer, "created_by_id" integer, "service_provider_id" integer, CONSTRAINT "REL_9730167e96da64fbd633599536" UNIQUE ("transaction_id"), CONSTRAINT "PK_b241fc32bd9f9f7cb3dd41b1f5b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_3ba72373b3e38e2be2507f57a9" ON "consultation_requests" ("date") `);
        await queryRunner.query(`CREATE TABLE "clinics_translations" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "address" character varying, "language_code" character varying NOT NULL, "name" character varying, "area" character varying, "clinic_id" integer, CONSTRAINT "PK_ad31627c939580b303efe080dd9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "clinics" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "mobile_number" character varying, "landline_number" character varying, "clinic_location" character varying, "location" geometry(Point,4326), "fees" integer NOT NULL DEFAULT 0, "is_free_clinic" boolean NOT NULL DEFAULT false, "schedule_id" integer, "doctor_id" integer, "governorate_id" integer, "city_id" integer, CONSTRAINT "REL_88648bb1654b6cd00d259c84e4" UNIQUE ("schedule_id"), CONSTRAINT "PK_5513b659e4d12b01a8ab3956abc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "call_requests" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying NOT NULL, "email" character varying, "mobile_number" character varying NOT NULL, "message" text, CONSTRAINT "PK_f9e7ee5721f64dd02189290d83c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "feedbacks" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying NOT NULL, "email" character varying, "mobile_number" character varying NOT NULL, "message" text NOT NULL, CONSTRAINT "PK_79affc530fdd838a9f1e0cc30be" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "promo_codes_discount_type_enum" AS ENUM('Percentage', 'Fixed')`);
        await queryRunner.query(`CREATE TYPE "promo_codes_consultation_type_enum" AS ENUM('Home Visit', 'Online Consultation', 'Clinic', 'All')`);
        await queryRunner.query(`CREATE TABLE "promo_codes" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "code" character varying NOT NULL, "discount_type" "promo_codes_discount_type_enum" NOT NULL DEFAULT 'Fixed', "is_active" boolean NOT NULL DEFAULT false, "amount" integer NOT NULL, "max_number_of_usage" integer NOT NULL, "country" character varying, "consultation_type" "promo_codes_consultation_type_enum" NOT NULL DEFAULT 'All', "start_date" date, "end_date" date, CONSTRAINT "UQ_2f096c406a9d9d5b8ce204190c3" UNIQUE ("code"), CONSTRAINT "PK_c7b4f01710fda5afa056a2b4a35" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "promo_codes-user" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "promo_code_id" integer, "user_id" integer, CONSTRAINT "PK_43981631d33a1201774ac536c42" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "provider_companies_company" ("service_providers_id" integer NOT NULL, "insurance_companies_id" integer NOT NULL, CONSTRAINT "PK_2855a843a7d2bbff4863843cc00" PRIMARY KEY ("service_providers_id", "insurance_companies_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_024a096748b76087f752ec6c4b" ON "provider_companies_company" ("service_providers_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_0d6b238ee50ba27ef8e90d8f21" ON "provider_companies_company" ("insurance_companies_id") `);
        await queryRunner.query(`CREATE TABLE "service_provider_doctors" ("service_providers_id" integer NOT NULL, "doctors_id" integer NOT NULL, CONSTRAINT "PK_a1a5fdf7d0e04fa5d002fafe8ef" PRIMARY KEY ("service_providers_id", "doctors_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_e4b307ec58ea949e6107cc5d6f" ON "service_provider_doctors" ("service_providers_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_9d2f17341847e889403bd4aa5a" ON "service_provider_doctors" ("doctors_id") `);
        await queryRunner.query(`CREATE TABLE "patient_medications" ("patients_id" integer NOT NULL, "medications_id" integer NOT NULL, CONSTRAINT "PK_3f71ee78f5b960956b1fd51aa06" PRIMARY KEY ("patients_id", "medications_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_5b981a15320bdb53f30ef5547c" ON "patient_medications" ("patients_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_11a0d68462e374cb09734274b9" ON "patient_medications" ("medications_id") `);
        await queryRunner.query(`CREATE TABLE "doctor_specialities_speciality" ("doctors_id" integer NOT NULL, "specialities_id" integer NOT NULL, CONSTRAINT "PK_07c52be7a18f3e3894f3d3c2426" PRIMARY KEY ("doctors_id", "specialities_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_9f62766333360a477d595e7775" ON "doctor_specialities_speciality" ("doctors_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_336aa98dff48bd40d1811f017e" ON "doctor_specialities_speciality" ("specialities_id") `);
        await queryRunner.query(`ALTER TABLE "notifications_translations" ADD CONSTRAINT "FK_499117d68fc0ed6e27af12f4528" FOREIGN KEY ("notification_id") REFERENCES "notifications"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "notifications" ADD CONSTRAINT "FK_9a8a82462cab47c73d25f49261f" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "custom-notifications" ADD CONSTRAINT "FK_daf47e0c1fb6b2661bbaa1311e3" FOREIGN KEY ("service_provider_id") REFERENCES "service_providers"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "doctors_service_providers_check_in_logs" ADD CONSTRAINT "FK_9a8b522682a16c4e6dda9abb557" FOREIGN KEY ("doctor_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "doctors_service_providers_check_in_logs" ADD CONSTRAINT "FK_aa70e0ce0c1ff885d73605f9836" FOREIGN KEY ("service_provider_id") REFERENCES "service_providers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "insurance_companies_translations" ADD CONSTRAINT "FK_15023d792557db5b6b56a9b9749" FOREIGN KEY ("insurance_company_id") REFERENCES "insurance_companies"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "schedule_days" ADD CONSTRAINT "FK_516216a81173c41661ba456b178" FOREIGN KEY ("schedule_id") REFERENCES "schedules"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schedules" ADD CONSTRAINT "FK_b723a982d8579f74107e715fdff" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schedules" ADD CONSTRAINT "FK_b7f3d0151b76508ba896d15e574" FOREIGN KEY ("doctor_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schedules" ADD CONSTRAINT "FK_30c4b47951abd6ff16457579ac7" FOREIGN KEY ("service_provider_id") REFERENCES "service_providers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_provider_types_translations" ADD CONSTRAINT "FK_5b2bf66f7c634a4947e8f898b78" FOREIGN KEY ("service_provider_type_id") REFERENCES "service_provider_types"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "service_providers" ADD CONSTRAINT "FK_a321a2bf4301c68dd9018b3e57f" FOREIGN KEY ("governorate_id") REFERENCES "governorates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_providers" ADD CONSTRAINT "FK_1398eaa18ba106c6704ae4c8b4f" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_providers" ADD CONSTRAINT "FK_2cc7c52b39288cadfad8a0ad63c" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_providers" ADD CONSTRAINT "FK_2eb201c984e1dcd6ec34fca396b" FOREIGN KEY ("service_provider_type_id") REFERENCES "service_provider_types"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "bill_transactions" ADD CONSTRAINT "FK_c9c7b8a897656034cbce3989fc1" FOREIGN KEY ("bill_id") REFERENCES "bills"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "bills" ADD CONSTRAINT "FK_436ecbb205f90ed4579287b8566" FOREIGN KEY ("doctor_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "bills" ADD CONSTRAINT "FK_8481289eecc8e52c39553c9e6bd" FOREIGN KEY ("service_provider_id") REFERENCES "service_providers"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "paymob_transactions" ADD CONSTRAINT "FK_e6a6f9f415ad8a602f62c5cedd8" FOREIGN KEY ("transaction_id") REFERENCES "transactions"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_2189be53b3816de49ea7b52ff5d" FOREIGN KEY ("doctor_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_ed0f03c517b2f5173041fbdac69" FOREIGN KEY ("patient_id") REFERENCES "patients"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_300bbe0b54e70255a815f2f0b62" FOREIGN KEY ("service_provider_id") REFERENCES "service_providers"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_186ddd4679c49cd78a937d25b1d" FOREIGN KEY ("bill_id") REFERENCES "bills"("id") ON DELETE NO ACTION ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_e9acc6efa76de013e8c1553ed2b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_d8bd6b0bf8b8c2684d35b6bc8c7" FOREIGN KEY ("request_id") REFERENCES "consultation_requests"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "medications_translations" ADD CONSTRAINT "FK_87fbc7e32e84a2e5100990d21e9" FOREIGN KEY ("medication_id") REFERENCES "medications"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "patients" ADD CONSTRAINT "FK_7fe1518dc780fd777669b5cb7a0" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users_translations" ADD CONSTRAINT "FK_36c74dda82ccc33424c0a7761b7" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "creditcards" ADD CONSTRAINT "FK_e59f6e28b207acc46ef36d44a22" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "cities" ADD CONSTRAINT "FK_f80852bdb3173c95268ccf087de" FOREIGN KEY ("governorate_id") REFERENCES "governorates"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "grades_translations" ADD CONSTRAINT "FK_ac80463b90b1605655f2e2712f0" FOREIGN KEY ("grade_id") REFERENCES "grades"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "specialities_translations" ADD CONSTRAINT "FK_3bdd57f60b650c5dcd6de1f88c3" FOREIGN KEY ("speciality_id") REFERENCES "specialities"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "ratings" ADD CONSTRAINT "FK_58e67c1ee51b4a91f58be40ed18" FOREIGN KEY ("rated_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ratings" ADD CONSTRAINT "FK_52e03df010ddc061f458676879f" FOREIGN KEY ("rater_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ratings" ADD CONSTRAINT "FK_fab651366c6681783136ec42781" FOREIGN KEY ("consultation_request_id") REFERENCES "consultation_requests"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "fcm_tokens" ADD CONSTRAINT "FK_9fd867cabc75028a5625ce7b24c" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "favourites" ADD CONSTRAINT "FK_210dadbaf4c8455574096faf746" FOREIGN KEY ("doctor_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "favourites" ADD CONSTRAINT "FK_ffb0866c42b7ff4d6e5131f3dcc" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "doctors" ADD CONSTRAINT "FK_c918bb8832b5e0b322913b969f8" FOREIGN KEY ("grade_id") REFERENCES "grades"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "doctors" ADD CONSTRAINT "FK_653c27d1b10652eb0c7bbbc4427" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "consultation_request_history" ADD CONSTRAINT "FK_e66e93f6b035a212c9ff4d243ae" FOREIGN KEY ("consultation_request_id") REFERENCES "consultation_requests"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "consultation_request_history" ADD CONSTRAINT "FK_e808ef6a8c2b70f569169759042" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" ADD CONSTRAINT "FK_a07eea8b56d25e25290a29f0b9c" FOREIGN KEY ("doctor_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" ADD CONSTRAINT "FK_08806f64a5a7605d500235d27c4" FOREIGN KEY ("patient_id") REFERENCES "patients"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" ADD CONSTRAINT "FK_d2fd09ef990372dc0091c8639c4" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" ADD CONSTRAINT "FK_9730167e96da64fbd6335995368" FOREIGN KEY ("transaction_id") REFERENCES "transactions"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" ADD CONSTRAINT "FK_3826ebf712a2a741850d3cb1e78" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" ADD CONSTRAINT "FK_fefe47a9f0748505716d4706610" FOREIGN KEY ("service_provider_id") REFERENCES "service_providers"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "clinics_translations" ADD CONSTRAINT "FK_0489a50e6ad6748c26599d8bbf2" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "clinics" ADD CONSTRAINT "FK_88648bb1654b6cd00d259c84e41" FOREIGN KEY ("schedule_id") REFERENCES "schedules"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "clinics" ADD CONSTRAINT "FK_f21fc3b0d46d769836fa1d22603" FOREIGN KEY ("doctor_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "clinics" ADD CONSTRAINT "FK_4fba5df35105d47f4a29af3c4a0" FOREIGN KEY ("governorate_id") REFERENCES "governorates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "clinics" ADD CONSTRAINT "FK_5b41283aa0a406d9d5497a5fae7" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "promo_codes-user" ADD CONSTRAINT "FK_9ae34e9482c963ab5fe70bc3611" FOREIGN KEY ("promo_code_id") REFERENCES "promo_codes"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "promo_codes-user" ADD CONSTRAINT "FK_798d1d3ea8d2166ed04d5916ff4" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "provider_companies_company" ADD CONSTRAINT "FK_024a096748b76087f752ec6c4bc" FOREIGN KEY ("service_providers_id") REFERENCES "service_providers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "provider_companies_company" ADD CONSTRAINT "FK_0d6b238ee50ba27ef8e90d8f21b" FOREIGN KEY ("insurance_companies_id") REFERENCES "insurance_companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_provider_doctors" ADD CONSTRAINT "FK_e4b307ec58ea949e6107cc5d6fb" FOREIGN KEY ("service_providers_id") REFERENCES "service_providers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_provider_doctors" ADD CONSTRAINT "FK_9d2f17341847e889403bd4aa5aa" FOREIGN KEY ("doctors_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "patient_medications" ADD CONSTRAINT "FK_5b981a15320bdb53f30ef5547cd" FOREIGN KEY ("patients_id") REFERENCES "patients"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "patient_medications" ADD CONSTRAINT "FK_11a0d68462e374cb09734274b95" FOREIGN KEY ("medications_id") REFERENCES "medications"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "doctor_specialities_speciality" ADD CONSTRAINT "FK_9f62766333360a477d595e7775b" FOREIGN KEY ("doctors_id") REFERENCES "doctors"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "doctor_specialities_speciality" ADD CONSTRAINT "FK_336aa98dff48bd40d1811f017e9" FOREIGN KEY ("specialities_id") REFERENCES "specialities"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "doctor_specialities_speciality" DROP CONSTRAINT "FK_336aa98dff48bd40d1811f017e9"`);
        await queryRunner.query(`ALTER TABLE "doctor_specialities_speciality" DROP CONSTRAINT "FK_9f62766333360a477d595e7775b"`);
        await queryRunner.query(`ALTER TABLE "patient_medications" DROP CONSTRAINT "FK_11a0d68462e374cb09734274b95"`);
        await queryRunner.query(`ALTER TABLE "patient_medications" DROP CONSTRAINT "FK_5b981a15320bdb53f30ef5547cd"`);
        await queryRunner.query(`ALTER TABLE "service_provider_doctors" DROP CONSTRAINT "FK_9d2f17341847e889403bd4aa5aa"`);
        await queryRunner.query(`ALTER TABLE "service_provider_doctors" DROP CONSTRAINT "FK_e4b307ec58ea949e6107cc5d6fb"`);
        await queryRunner.query(`ALTER TABLE "provider_companies_company" DROP CONSTRAINT "FK_0d6b238ee50ba27ef8e90d8f21b"`);
        await queryRunner.query(`ALTER TABLE "provider_companies_company" DROP CONSTRAINT "FK_024a096748b76087f752ec6c4bc"`);
        await queryRunner.query(`ALTER TABLE "promo_codes-user" DROP CONSTRAINT "FK_798d1d3ea8d2166ed04d5916ff4"`);
        await queryRunner.query(`ALTER TABLE "promo_codes-user" DROP CONSTRAINT "FK_9ae34e9482c963ab5fe70bc3611"`);
        await queryRunner.query(`ALTER TABLE "clinics" DROP CONSTRAINT "FK_5b41283aa0a406d9d5497a5fae7"`);
        await queryRunner.query(`ALTER TABLE "clinics" DROP CONSTRAINT "FK_4fba5df35105d47f4a29af3c4a0"`);
        await queryRunner.query(`ALTER TABLE "clinics" DROP CONSTRAINT "FK_f21fc3b0d46d769836fa1d22603"`);
        await queryRunner.query(`ALTER TABLE "clinics" DROP CONSTRAINT "FK_88648bb1654b6cd00d259c84e41"`);
        await queryRunner.query(`ALTER TABLE "clinics_translations" DROP CONSTRAINT "FK_0489a50e6ad6748c26599d8bbf2"`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" DROP CONSTRAINT "FK_fefe47a9f0748505716d4706610"`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" DROP CONSTRAINT "FK_3826ebf712a2a741850d3cb1e78"`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" DROP CONSTRAINT "FK_9730167e96da64fbd6335995368"`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" DROP CONSTRAINT "FK_d2fd09ef990372dc0091c8639c4"`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" DROP CONSTRAINT "FK_08806f64a5a7605d500235d27c4"`);
        await queryRunner.query(`ALTER TABLE "consultation_requests" DROP CONSTRAINT "FK_a07eea8b56d25e25290a29f0b9c"`);
        await queryRunner.query(`ALTER TABLE "consultation_request_history" DROP CONSTRAINT "FK_e808ef6a8c2b70f569169759042"`);
        await queryRunner.query(`ALTER TABLE "consultation_request_history" DROP CONSTRAINT "FK_e66e93f6b035a212c9ff4d243ae"`);
        await queryRunner.query(`ALTER TABLE "doctors" DROP CONSTRAINT "FK_653c27d1b10652eb0c7bbbc4427"`);
        await queryRunner.query(`ALTER TABLE "doctors" DROP CONSTRAINT "FK_c918bb8832b5e0b322913b969f8"`);
        await queryRunner.query(`ALTER TABLE "favourites" DROP CONSTRAINT "FK_ffb0866c42b7ff4d6e5131f3dcc"`);
        await queryRunner.query(`ALTER TABLE "favourites" DROP CONSTRAINT "FK_210dadbaf4c8455574096faf746"`);
        await queryRunner.query(`ALTER TABLE "fcm_tokens" DROP CONSTRAINT "FK_9fd867cabc75028a5625ce7b24c"`);
        await queryRunner.query(`ALTER TABLE "ratings" DROP CONSTRAINT "FK_fab651366c6681783136ec42781"`);
        await queryRunner.query(`ALTER TABLE "ratings" DROP CONSTRAINT "FK_52e03df010ddc061f458676879f"`);
        await queryRunner.query(`ALTER TABLE "ratings" DROP CONSTRAINT "FK_58e67c1ee51b4a91f58be40ed18"`);
        await queryRunner.query(`ALTER TABLE "specialities_translations" DROP CONSTRAINT "FK_3bdd57f60b650c5dcd6de1f88c3"`);
        await queryRunner.query(`ALTER TABLE "grades_translations" DROP CONSTRAINT "FK_ac80463b90b1605655f2e2712f0"`);
        await queryRunner.query(`ALTER TABLE "cities" DROP CONSTRAINT "FK_f80852bdb3173c95268ccf087de"`);
        await queryRunner.query(`ALTER TABLE "creditcards" DROP CONSTRAINT "FK_e59f6e28b207acc46ef36d44a22"`);
        await queryRunner.query(`ALTER TABLE "users_translations" DROP CONSTRAINT "FK_36c74dda82ccc33424c0a7761b7"`);
        await queryRunner.query(`ALTER TABLE "patients" DROP CONSTRAINT "FK_7fe1518dc780fd777669b5cb7a0"`);
        await queryRunner.query(`ALTER TABLE "medications_translations" DROP CONSTRAINT "FK_87fbc7e32e84a2e5100990d21e9"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_d8bd6b0bf8b8c2684d35b6bc8c7"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_e9acc6efa76de013e8c1553ed2b"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_186ddd4679c49cd78a937d25b1d"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_300bbe0b54e70255a815f2f0b62"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_ed0f03c517b2f5173041fbdac69"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_2189be53b3816de49ea7b52ff5d"`);
        await queryRunner.query(`ALTER TABLE "paymob_transactions" DROP CONSTRAINT "FK_e6a6f9f415ad8a602f62c5cedd8"`);
        await queryRunner.query(`ALTER TABLE "bills" DROP CONSTRAINT "FK_8481289eecc8e52c39553c9e6bd"`);
        await queryRunner.query(`ALTER TABLE "bills" DROP CONSTRAINT "FK_436ecbb205f90ed4579287b8566"`);
        await queryRunner.query(`ALTER TABLE "bill_transactions" DROP CONSTRAINT "FK_c9c7b8a897656034cbce3989fc1"`);
        await queryRunner.query(`ALTER TABLE "service_providers" DROP CONSTRAINT "FK_2eb201c984e1dcd6ec34fca396b"`);
        await queryRunner.query(`ALTER TABLE "service_providers" DROP CONSTRAINT "FK_2cc7c52b39288cadfad8a0ad63c"`);
        await queryRunner.query(`ALTER TABLE "service_providers" DROP CONSTRAINT "FK_1398eaa18ba106c6704ae4c8b4f"`);
        await queryRunner.query(`ALTER TABLE "service_providers" DROP CONSTRAINT "FK_a321a2bf4301c68dd9018b3e57f"`);
        await queryRunner.query(`ALTER TABLE "service_provider_types_translations" DROP CONSTRAINT "FK_5b2bf66f7c634a4947e8f898b78"`);
        await queryRunner.query(`ALTER TABLE "schedules" DROP CONSTRAINT "FK_30c4b47951abd6ff16457579ac7"`);
        await queryRunner.query(`ALTER TABLE "schedules" DROP CONSTRAINT "FK_b7f3d0151b76508ba896d15e574"`);
        await queryRunner.query(`ALTER TABLE "schedules" DROP CONSTRAINT "FK_b723a982d8579f74107e715fdff"`);
        await queryRunner.query(`ALTER TABLE "schedule_days" DROP CONSTRAINT "FK_516216a81173c41661ba456b178"`);
        await queryRunner.query(`ALTER TABLE "insurance_companies_translations" DROP CONSTRAINT "FK_15023d792557db5b6b56a9b9749"`);
        await queryRunner.query(`ALTER TABLE "doctors_service_providers_check_in_logs" DROP CONSTRAINT "FK_aa70e0ce0c1ff885d73605f9836"`);
        await queryRunner.query(`ALTER TABLE "doctors_service_providers_check_in_logs" DROP CONSTRAINT "FK_9a8b522682a16c4e6dda9abb557"`);
        await queryRunner.query(`ALTER TABLE "custom-notifications" DROP CONSTRAINT "FK_daf47e0c1fb6b2661bbaa1311e3"`);
        await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_9a8a82462cab47c73d25f49261f"`);
        await queryRunner.query(`ALTER TABLE "notifications_translations" DROP CONSTRAINT "FK_499117d68fc0ed6e27af12f4528"`);
        await queryRunner.query(`DROP INDEX "IDX_336aa98dff48bd40d1811f017e"`);
        await queryRunner.query(`DROP INDEX "IDX_9f62766333360a477d595e7775"`);
        await queryRunner.query(`DROP TABLE "doctor_specialities_speciality"`);
        await queryRunner.query(`DROP INDEX "IDX_11a0d68462e374cb09734274b9"`);
        await queryRunner.query(`DROP INDEX "IDX_5b981a15320bdb53f30ef5547c"`);
        await queryRunner.query(`DROP TABLE "patient_medications"`);
        await queryRunner.query(`DROP INDEX "IDX_9d2f17341847e889403bd4aa5a"`);
        await queryRunner.query(`DROP INDEX "IDX_e4b307ec58ea949e6107cc5d6f"`);
        await queryRunner.query(`DROP TABLE "service_provider_doctors"`);
        await queryRunner.query(`DROP INDEX "IDX_0d6b238ee50ba27ef8e90d8f21"`);
        await queryRunner.query(`DROP INDEX "IDX_024a096748b76087f752ec6c4b"`);
        await queryRunner.query(`DROP TABLE "provider_companies_company"`);
        await queryRunner.query(`DROP TABLE "promo_codes-user"`);
        await queryRunner.query(`DROP TABLE "promo_codes"`);
        await queryRunner.query(`DROP TYPE "promo_codes_consultation_type_enum"`);
        await queryRunner.query(`DROP TYPE "promo_codes_discount_type_enum"`);
        await queryRunner.query(`DROP TABLE "feedbacks"`);
        await queryRunner.query(`DROP TABLE "call_requests"`);
        await queryRunner.query(`DROP TABLE "clinics"`);
        await queryRunner.query(`DROP TABLE "clinics_translations"`);
        await queryRunner.query(`DROP INDEX "IDX_3ba72373b3e38e2be2507f57a9"`);
        await queryRunner.query(`DROP TABLE "consultation_requests"`);
        await queryRunner.query(`DROP TYPE "consultation_requests_payment_status_enum"`);
        await queryRunner.query(`DROP TYPE "consultation_requests_payment_method_enum"`);
        await queryRunner.query(`DROP TYPE "consultation_requests_other_patient_gender_enum"`);
        await queryRunner.query(`DROP TYPE "consultation_requests_type_enum"`);
        await queryRunner.query(`DROP TYPE "consultation_requests_status_enum"`);
        await queryRunner.query(`DROP TABLE "consultation_request_history"`);
        await queryRunner.query(`DROP TYPE "consultation_request_history_update_type_enum"`);
        await queryRunner.query(`DROP TYPE "consultation_request_history_status_enum"`);
        await queryRunner.query(`DROP TABLE "doctors"`);
        await queryRunner.query(`DROP TYPE "doctors_status_enum"`);
        await queryRunner.query(`DROP TYPE "doctors_syndicate_id_status_enum"`);
        await queryRunner.query(`DROP TABLE "favourites"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TYPE "users_gender_enum"`);
        await queryRunner.query(`DROP TYPE "users_role_enum"`);
        await queryRunner.query(`DROP TABLE "fcm_tokens"`);
        await queryRunner.query(`DROP TABLE "ratings"`);
        await queryRunner.query(`DROP TABLE "specialities_translations"`);
        await queryRunner.query(`DROP TABLE "specialities"`);
        await queryRunner.query(`DROP TABLE "grades_translations"`);
        await queryRunner.query(`DROP TABLE "grades"`);
        await queryRunner.query(`DROP TABLE "cities"`);
        await queryRunner.query(`DROP TABLE "creditcards"`);
        await queryRunner.query(`DROP INDEX "IDX_3a501173aedfbeb8d751beb167"`);
        await queryRunner.query(`DROP TABLE "users_translations"`);
        await queryRunner.query(`DROP TABLE "patients"`);
        await queryRunner.query(`DROP TYPE "patients_marital_status_enum"`);
        await queryRunner.query(`DROP TABLE "medications"`);
        await queryRunner.query(`DROP TABLE "medications_translations"`);
        await queryRunner.query(`DROP TABLE "transactions"`);
        await queryRunner.query(`DROP TABLE "paymob_transactions"`);
        await queryRunner.query(`DROP TABLE "bills"`);
        await queryRunner.query(`DROP TYPE "bills_status_enum"`);
        await queryRunner.query(`DROP TABLE "bill_transactions"`);
        await queryRunner.query(`DROP TABLE "service_providers"`);
        await queryRunner.query(`DROP TABLE "service_provider_types"`);
        await queryRunner.query(`DROP TABLE "service_provider_types_translations"`);
        await queryRunner.query(`DROP TABLE "schedules"`);
        await queryRunner.query(`DROP TYPE "schedules_type_enum"`);
        await queryRunner.query(`DROP TABLE "schedule_days"`);
        await queryRunner.query(`DROP TYPE "schedule_days_day_enum"`);
        await queryRunner.query(`DROP TABLE "insurance_companies"`);
        await queryRunner.query(`DROP TABLE "insurance_companies_translations"`);
        await queryRunner.query(`DROP TABLE "governorates"`);
        await queryRunner.query(`DROP TABLE "doctors_service_providers_check_in_logs"`);
        await queryRunner.query(`DROP TABLE "custom-notifications"`);
        await queryRunner.query(`DROP TABLE "notifications"`);
        await queryRunner.query(`DROP TABLE "notifications_translations"`);
    }

}
