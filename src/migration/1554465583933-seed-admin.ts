import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedAdmin1554465583933 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // password = A1234567890
        await queryRunner.query(`
        INSERT INTO "users"
        ("created_at", "updated_at", "name", "role", "email", "password", "phone", "avatar", "refresh_token", "gender")
        values (
            default,
            default,
            'kashf admin',
            'SUPER_ADMIN',
            '<EMAIL>',
            '$2b$10$u3USV0aG9YByz3nL74Gn5.bQ9T14V4Y.g3k8Ng78SXa4nWnsbM7CO',
            '+201273604089',
            DEFAULT,
            DEFAULT,
            'Male')
            returning id
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const [user] = await queryRunner.query(`
        SELECT "id"
        FROM "users"
        WHERE "email" = '<EMAIL>'
      `);
        const userId: number = user.id;
        await queryRunner.query(`
        DELETE FROM "users" WHERE "id" = ${userId}
      `);
    }
}
