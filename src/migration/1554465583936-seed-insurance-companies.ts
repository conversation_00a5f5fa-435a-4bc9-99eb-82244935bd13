/* eslint-disable @typescript-eslint/restrict-template-expressions */
/* eslint-disable max-len */
import { MigrationInterface, QueryRunner } from 'typeorm';

import { AvailableLanguageCodes } from '../i18n/languageCodes';
import { InsuranceCompany } from '../modules/insurance-companies/entities/insurance-company.entity';

export class SeedInsuranceCompanies1554465583936 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        const insuranceCompanies = [
            {
                translations: [
                    {
                        title: 'Mashreq',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'Mashreq',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
            {
                translations: [
                    {
                        title: 'Axa',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'Axa',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
            {
                translations: [
                    {
                        title: 'Met Life',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'Met Life',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
            {
                translations: [
                    {
                        title: 'Prime Health',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'Prime Health',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
            {
                translations: [
                    {
                        title: 'Glob Med',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'Glob Med',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
            {
                translations: [
                    {
                        title: 'رعاية +',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'رعاية +',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
            {
                translations: [
                    {
                        title: 'Syndicate of Doctors',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'Syndicate of Doctors',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
            {
                translations: [
                    {
                        title: 'Syndicate of Engineers',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'Syndicate of Engineers',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
            {
                translations: [
                    {
                        title: 'Syndicate of Lawyers',
                        languageCode: AvailableLanguageCodes.en,
                    },
                    {
                        title: 'Syndicate of Lawyers',
                        languageCode: AvailableLanguageCodes.ar,
                    },
                ],
            },
        ];

        await Promise.all(
            insuranceCompanies.map(async (speciality) => {
                const createdInsuranceCompany = await queryRunner.query(`
                INSERT INTO "insurance_companies"("created_at", "updated_at", "logo_url") VALUES (DEFAULT, DEFAULT, '') RETURNING "id"
                `);
                await Promise.all(
                    speciality.translations.map(async (translation) => {
                        await queryRunner.query(
                            `INSERT INTO "insurance_companies_translations"("created_at", "updated_at", "title", "language_code", "insurance_company_id") VALUES (DEFAULT, DEFAULT, '${translation.title}', '${translation.languageCode}', ${createdInsuranceCompany[0].id}) `,
                        );
                    }),
                );
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.manager
            .createQueryBuilder()
            .delete()
            .from(InsuranceCompany)
            .execute();
    }
}
