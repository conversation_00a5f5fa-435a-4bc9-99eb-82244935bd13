/* eslint-disable @typescript-eslint/restrict-template-expressions */
/* eslint-disable max-len */

import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateMigratedDoctorsScheduleStatus1622625138562
    implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            "update schedules set is_enabled = false where created_at between '2021-04-21' and '2021-04-22'",
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            "update schedules set is_enabled = true where created_at between '2021-04-21' and '2021-04-22'",
        );
    }
}
