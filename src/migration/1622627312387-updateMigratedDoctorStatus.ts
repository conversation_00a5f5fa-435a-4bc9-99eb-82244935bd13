/* eslint-disable @typescript-eslint/restrict-template-expressions */

import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateMigratedDoctorStatus1622627312387
    implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        queryRunner.query(
            "update doctors set is_active = false where (syndicate_id_url  is NULL or syndicate_number = '' or syndicate_number is NULL or syndicate_id_url = '') and created_at between '2021-04-21' and '2021-04-22'",
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {}
}
