import { MigrationInterface, QueryRunner } from 'typeorm';

export class GradesSeed1618827377838 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        const grades = [
            {
                id: '1',
            },
            {
                id: '2',
            },
            {
                id: '3',
            },
            {
                id: '4',
            },
        ];
        await Promise.all(
            grades.map(async (grade) => {
                await queryRunner.query(`
            INSERT INTO "grades"("id") VALUES ('${grade.id}')`);
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DELETE from grades where id >= 1 and <= 4');
    }
}
