import { MigrationInterface, QueryRunner } from 'typeorm';

export class GradesTranslationSeed1624188999156 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        const gradesTranslation = [
            {
                id: '1',
                title: 'Perfect',
                language_code: 'en',
                grade_id: '1',
            },
            {
                id: '2',
                title: 'ممتاز',
                language_code: 'ar',
                grade_id: '1',
            },
            {
                id: '3',
                title: 'Very Good',
                language_code: 'en',
                grade_id: '2',
            },
            {
                id: '4',
                title: 'جيد جدا',
                language_code: 'ar',
                grade_id: '2',
            },
            {
                id: '5',
                title: 'Good',
                language_code: 'en',
                grade_id: '3',
            },
            {
                id: '6',
                title: 'جيد',
                language_code: 'ar',
                grade_id: '3',
            },
            {
                id: '7',
                title: 'Bad',
                language_code: 'en',
                grade_id: '4',
            },
            {
                id: '8',
                title: 'سيئ',
                language_code: 'ar',
                grade_id: '4',
            },
        ];
        await Promise.all(
            gradesTranslation.map(async (item) => {
                await queryRunner.query(`
                INSERT INTO "grades_translations"("id","title","language_code","grade_id") VALUES 
                ('${item.id}','${item.title}','${item.language_code}','${item.grade_id}')`);
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            'DELETE from grades_translations where id >= 1 and <= 8',
        );
    }
}
