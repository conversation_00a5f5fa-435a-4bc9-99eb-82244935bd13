'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { UsersPageDto } from '../users/dto/usersPage.dto';
import { UsersPageOptionsDto } from '../users/dto/usersPageOptions.dto';
import { AccountantsService } from './accountants.service';
import { CreateAccountantDto } from './dto/create-accountant.dto';
import { DownloadAccountantsDto } from './dto/download-accountants.dto';
import { UpdateAccountantDto } from './dto/update-accountant.dto';

@Controller('accountants')
@ApiTags('accountants')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(HeaderInterceptor)
@ApiBearerAuth()
export class AccountantsController {
    constructor(private accountantsService: AccountantsService) {}

    @Post('')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Create new admin',
        type: CreateOperationsResponse,
    })
    createAccountant(
        @Body() createAccountantDto: CreateAccountantDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.accountantsService.createAccountant(
            createAccountantDto,
            lang,
        );
    }

    @Get('')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'list admins',
        type: UsersPageDto,
    })
    listAccountants(
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: UsersPageOptionsDto,
    ): Promise<UsersPageDto> {
        return this.accountantsService.listAccountants(query, pageOptionsDto);
    }

    @Get(':id')
    @Roles(RoleType.SUPER_ADMIN)
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'view admin',
        type: UserDetailsDto,
    })
    viewAccountant(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<UserDetailsDto> {
        return this.accountantsService.viewAccountant(id, lang);
    }

    @Get('/download')
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        description: 'Download Accountants',
        type: [DownloadAccountantsDto],
    })
    async downloadAccountant(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
    ): Promise<void> {
        const data = await this.accountantsService.downloadAccountants(query);
        res.setHeader('Content-disposition', 'attachment; filename=admins.csv');
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Put(':id')
    @Roles(RoleType.SUPER_ADMIN)
    @UseGuards(AuthGuard, RolesGuard)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'update admin',
        type: BasicOperationsResponse,
    })
    updateAccountant(
        @Param('id') id: number,
        @Body() updateAccountant: UpdateAccountantDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.accountantsService.updateAccountant(
            id,
            updateAccountant,
            lang,
        );
    }

    @Delete(':id')
    @Roles(RoleType.SUPER_ADMIN)
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'delete accountant',
        type: BasicOperationsResponse,
    })
    deleteAdmin(@Param('id') id: number): Promise<BasicOperationsResponse> {
        return this.accountantsService.deleteAccountant(id);
    }
}
