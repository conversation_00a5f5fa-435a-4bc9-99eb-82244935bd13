import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { AccountantDto } from './dto/accountant.dto';
import { CreateAccountantDto } from './dto/create-accountant.dto';
import { DownloadAccountantsDto } from './dto/download-accountants.dto';
import { UpdateAccountantDto } from './dto/update-accountant.dto';
import { AccountantEntity } from './entities/accountant.entity';

type AccountantDTOs = CreateAccountantDto | UpdateAccountantDto | AccountantDto;

@Injectable()
export class AccountantsMapper extends AbstractMapper<
    AccountantDTOs,
    AccountantEntity
> {
    constructor(private readonly helperService: HelperService) {
        super();
    }

    fromEntityToDTO(
        destination: ClassType<AccountantDto>,
        sourceObject: AccountantEntity,
    ): AccountantDto {
        const doctorDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(doctorDto);
        return doctorDto;
    }

    fromDTOToEntity(
        destination: ClassType<AccountantEntity>,
        sourceObject: AccountantDTOs,
    ): AccountantEntity {
        const accountantEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(accountantEntity);
        return accountantEntity;
    }

    fromEntityToDownloadData(
        destination: ClassType<DownloadAccountantsDto>,
        sourceObject: AccountantEntity,
    ): DownloadAccountantsDto {
        return {
            id: sourceObject.id,
            email: sourceObject.user.email,
            isActive: sourceObject.user.isActive,
            name: sourceObject.user.name,
            phone: sourceObject.user.phone,
            role: sourceObject.user.role,
        };
    }
}
