import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DataDownloadService } from '../../shared/services/data-download.service';
import { SharedModule } from '../../shared/shared.module';
import { AuthModule } from '../auth/auth.module';
import { UserEntity } from '../users/entities/user.entity';
import { UsersModule } from '../users/users.module';
import { AccountantsController } from './accountants.controller';
import { AccountantsMapper } from './accountants.mapper';
import { AccountantsService } from './accountants.service';
import { AccountantRepository } from './repositories/accountant.repository';

@Module({
    imports: [
        forwardRef(() => AuthModule),
        TypeOrmModule.forFeature([AccountantRepository, UserEntity]),
        SharedModule,
        UsersModule,
        JwtModule.register({}),
    ],
    controllers: [AccountantsController],
    exports: [AccountantsService, AccountantsMapper],
    providers: [AccountantsService, AccountantsMapper, DataDownloadService],
})
export class AccountantsModule {}
