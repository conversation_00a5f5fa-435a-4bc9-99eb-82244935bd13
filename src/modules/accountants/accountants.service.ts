import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
} from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { Repository, SelectQueryBuilder } from 'typeorm';

import { RoleType, SortByType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { DataDownloadService } from '../../shared/services/data-download.service';
import { DebugLogger } from '../../shared/services/logger.service';
import { AuthMessagesKeys } from '../auth/translate.enum';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { UsersPageDto } from '../users/dto/usersPage.dto';
import { UsersPageOptionsDto } from '../users/dto/usersPageOptions.dto';
import { UserEntity } from '../users/entities/user.entity';
// import { UsersRepository } from '../users/repositories/users.repository';
import { UsersMapper } from '../users/users.mapper';
import { UsersService } from '../users/users.service';
import { AccountantsMapper } from './accountants.mapper';
import { CreateAccountantDto } from './dto/create-accountant.dto';
import { DownloadAccountantsDto } from './dto/download-accountants.dto';
import { UpdateAccountantDto } from './dto/update-accountant.dto';
import { AccountantEntity } from './entities/accountant.entity';
import { AccountantRepository } from './repositories/accountant.repository';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class AccountantsService {
    constructor(
        private readonly userService: UsersService,
        public readonly accountantRepository: AccountantRepository,
        @InjectRepository(UserEntity)
        public readonly usersRepository: Repository<UserEntity>,
        public readonly accountantsMapper: AccountantsMapper,
        public readonly usersMapper: UsersMapper,
        private readonly i18n: I18nService,
        private readonly dataDownloadService: DataDownloadService,
        private readonly logger: DebugLogger,
    ) {}

    async createAccountant(
        createAccountantDto: CreateAccountantDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        await this.userService.checkUserExistence(
            createAccountantDto,
            lang,
            RoleType.ACCOUNTANT,
        );

        const userEntity = this.usersMapper.fromDTOToEntity(
            UserEntity,
            createAccountantDto,
        );
        userEntity.isActive = true;
        userEntity.role = RoleType.ACCOUNTANT;

        const user = this.usersRepository.create(userEntity);
        const createdUser = await this.usersRepository.save(user);

        const accountantEntity = this.accountantsMapper.fromDTOToEntity(
            AccountantEntity,
            createAccountantDto,
        );

        accountantEntity.user = createdUser;

        const accountant = this.accountantRepository.create(accountantEntity);

        await this.accountantRepository.save(accountant);

        return {
            createdId: createdUser.id,
            isSuccessful: true,
            message: 'Accountant is added successfully',
        };
    }

    async listAccountants(
        httpQueryString: string,
        pageOptionsDto: UsersPageOptionsDto,
    ): Promise<UsersPageDto> {
        let queryBuilder = this.usersRepository
            .createQueryBuilder('user')
            .select([
                'user.id',
                'user.role',
                'user.name',
                'user.email',
                'user.phone',
                'user.accessibility',
                'user.isActive',
            ])
            .where('user.role IN (:...roles)', { roles: [RoleType.ACCOUNTANT] })
            .andWhere('user.isDeleted = :isDeleted', { isDeleted: false });
        if (httpQueryString) {
            queryBuilder = this.buildGetAccountantQuery(
                queryBuilder,
                httpQueryString,
            );
        }
        const [users, pageMetaDto] = await queryBuilder.paginate(
            pageOptionsDto,
        );
        return new UsersPageDto(users, pageMetaDto);
    }

    buildGetAccountantQuery(
        query: SelectQueryBuilder<UserEntity>,
        httpQueryString: string,
    ): SelectQueryBuilder<UserEntity> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }
        // TODO: fix filter once the doctor has en and ar names
        if (httpQueryObject.search) {
            query.andWhere(
                '(user.id || user.name || user.phone || user.email || user.role ) ILIKE :searchKey',
                {
                    searchKey: `%${httpQueryObject.search.value}%`,
                },
            );
        }

        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.NAME:
                    query.orderBy('user.name', httpQueryObject.sort.type);
                    break;
                case SortByType.ROLE:
                    query.orderBy('user.role', httpQueryObject.sort.type);
            }
        }

        return query;
    }

    async viewAccountant(id: number, lang: string): Promise<UserDetailsDto> {
        const user = await this.usersRepository
            .createQueryBuilder('user')
            .where('user.id = :id', { id })
            .select([
                'user.id',
                'user.role',
                'user.name',
                'user.email',
                'user.phone',
                'user.accessibility',
                'user.isActive',
            ])
            .getOne();

        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.usersMapper.fromEntityToUserDetailsDTO(
            UserDetailsDto,
            user,
        );
    }

    async downloadAccountants(httpQueryString: string): Promise<string> {
        this.logger.log('formal');

        let query = this.usersRepository
            .createQueryBuilder('user')
            .select([
                'user.id',
                'user.role',
                'user.name',
                'user.phone',
                'user.email',
                'user.isActive',
            ])
            .where('user.role IN (:...roles)', {
                roles: [RoleType.ACCOUNTANT],
            });

        if (httpQueryString) {
            query = this.buildGetAccountantQuery(query, httpQueryString);
        }

        this.logger.log('loggable');

        const requests = await query.getMany();

        this.logger.log(JSON.stringify(requests));

        const mappedRequests = requests.map((request) =>
            this.usersMapper.fromEntityToDownloadData(
                DownloadAccountantsDto,
                request,
            ),
        );

        this.logger.log(JSON.stringify(mappedRequests));

        const fields = ['id', 'email', 'isActive', 'name', 'phone', 'role'];
        return this.dataDownloadService.downloadCsv(fields, mappedRequests);
    }

    async updateAccountant(
        id: number,
        updateAccountant: UpdateAccountantDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const user = await this.usersRepository.findOne({
            where: [{ id, role: RoleType.ACCOUNTANT }],
        });
        if (!user) {
            throw new HttpException(
                this.i18n.translate(AuthMessagesKeys.USER_NOT_FOUND, { lang }),
                HttpStatus.NOT_FOUND,
            );
        }
        const accountantDto = this.usersMapper.fromDTOToEntity(
            UserEntity,
            updateAccountant,
        );
        await this.usersRepository.update({ id }, accountantDto);
        return {
            isSuccessful: true,
            message: 'Accountant Updated successfully',
        };
    }

    async deleteAccountant(id: number): Promise<BasicOperationsResponse> {
        await this.accountantRepository.delete({
            user: {
                id,
            },
        });
        await this.usersRepository.delete({ id });
        return {
            isSuccessful: true,
            message: 'Accountant Deleted successfully',
        };
    }
}
