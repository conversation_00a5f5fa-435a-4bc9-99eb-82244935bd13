'use strict';

import { Expose } from 'class-transformer';
import {
    IsBoolean,
    IsEmail,
    IsOptional,
    IsPhoneNumber,
    IsString,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class UpdateAccountantDto extends AbstractDto {
    @IsString()
    @IsOptional()
    @Expose()
    name?: string;

    @IsEmail({}, { message: 'invalid email' })
    @IsOptional()
    @Expose()
    email?: string;

    @IsString()
    @Expose()
    @IsOptional()
    accessibility?: string;

    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @IsOptional()
    @Expose()
    phone?: string;

    @IsBoolean()
    @IsOptional()
    @Expose()
    isActive?: boolean;
}
