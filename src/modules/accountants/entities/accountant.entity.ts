import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from '../../users/entities/user.entity';

@Entity('accountants')
export class AccountantEntity extends AbstractEntity {
    @OneToOne((_type) => UserEntity, (user) => user.accountant, {
        onDelete: 'CASCADE',
        cascade: ['insert'],
    })
    @JoinColumn()
    user: UserEntity;
}
