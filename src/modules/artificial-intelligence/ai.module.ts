import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiModelController } from './controllers/ai-model.controller';
import { AiModel } from './entities/ai-model.entity';
import { AiModelService } from './services/ai-model.service';

@Module({
    imports: [TypeOrmModule.forFeature([AiModel])],
    controllers: [AiModelController],
    providers: [AiModelService],
    exports: [AiModelService],
})
export class AiModelModule {}
