import {
    Body,
    Controller,
    Get,
    NotFoundException,
    Param,
    Post,
    Put,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { AiModel } from '../entities/ai-model.entity';
import { AiModelService } from '../services/ai-model.service';

@ApiTags('Ai Models')
@Controller('ai-models')
export class AiModelController {
    constructor(private readonly modelService: AiModelService) {}

    @Get()
    @ApiOperation({ summary: 'Get all model' })
    @ApiResponse({ status: 200, type: AiModel })
    async getAllAiModel(): Promise<AiModel[]> {
        return this.modelService.getAllAiModels();
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get model by name' })
    @ApiResponse({ status: 200, type: AiModel })
    async getAiModel(@Param('id') id: string): Promise<AiModel> {
        const model = await this.modelService.getAiModel(+id);
        if (!model) {
            throw new NotFoundException('Model not found');
        }
        return model;
    }

    @Post(':name/rotate-key')
    @ApiOperation({ summary: 'Stage new API key for rotation' })
    @ApiResponse({ status: 200, type: AiModel })
    async rotateKey(
        @Param('name') name: string,
        @Body('newEncryptedKey') newEncryptedKey: string,
    ): Promise<AiModel> {
        const model = await this.modelService.rotateApiKey(
            name,
            newEncryptedKey,
        );
        if (!model) {
            throw new NotFoundException('Model not found');
        }
        return model;
    }

    @Put(':name/promote-key')
    @ApiOperation({ summary: 'Promote backup key to primary' })
    @ApiResponse({ status: 200, type: AiModel })
    async promoteKey(@Param('name') name: string): Promise<AiModel> {
        const model = await this.modelService.promoteBackupKey(name);
        if (!model) {
            throw new NotFoundException('Model not found');
        }
        return model;
    }
}
