import { Column, Entity } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';

@Entity('ai_models')
export class AiModel extends AbstractEntity {
    @Column({ unique: true })
    name: string;

    @Column()
    url: string;

    @Column('text')
    description: string;

    @Column()
    currentApiKey: string;

    @Column({ nullable: true })
    backupApiKey: string;
}
