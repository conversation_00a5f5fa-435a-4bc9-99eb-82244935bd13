import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AiModel } from '../entities/ai-model.entity';

@Injectable()
export class AiModelService {
    constructor(
        @InjectRepository(AiModel)
        private modelRepo: Repository<AiModel>,
    ) {}

    async getAllAiModels() {
        // eslint-disable-next-line no-return-await
        return await this.modelRepo
            .createQueryBuilder('aiModel')
            .select([
                'aiModel.id',
                'aiModel.name',
                'aiModel.url',
                'aiModel.currentApiKey',
                'aiModel.description',
            ])
            .orderBy('aiModel.id')
            .getMany();
    }
    async getAiModel(id: number): Promise<AiModel> {
        return this.modelRepo.findOne({ where: { id } });
    }

    async rotateApiKey(
        modelName: string,
        newEncryptedKey: string,
    ): Promise<AiModel> {
        const model = await this.modelRepo.findOne({
            where: { name: modelName },
        });

        // Stage new key as backup
        model.backupApiKey = newEncryptedKey;

        // Save and return
        return this.modelRepo.save(model);
    }

    async promoteBackupKey(modelName: string): Promise<AiModel> {
        const model = await this.modelRepo.findOne({
            where: { name: modelName },
        });

        // Swap keys
        model.currentApiKey = model.backupApiKey;
        model.backupApiKey = null;

        return this.modelRepo.save(model);
    }
}
