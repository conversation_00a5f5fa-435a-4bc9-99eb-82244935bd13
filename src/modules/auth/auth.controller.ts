import {
    Body,
    Controller,
    Get,
    Headers,
    HttpCode,
    HttpException,
    HttpStatus,
    Param,
    Post,
    Put,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    Api<PERSON>earerAuth,
    ApiHeader,
    ApiOkResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang, I18nService } from 'nestjs-i18n';
import * as path from 'path';

import { Languages, RoleType, SourceType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { AuthUser } from '../../decorators/auth-user.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { UserDto } from '../users/dto/user.dto';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { UserEntity } from '../users/entities/user.entity';
import { UsersMapper } from '../users/users.mapper';
import { UsersService } from '../users/users.service';
import { AuthService } from './auth.service';
import { AdminLoginDto } from './dto/admin-login.dto';
import { ForgetPasswordDto } from './dto/forget-password.dto';
import { PreLoginDto } from './dto/pre-login.dto';
import { RefreshTokenDTO } from './dto/refresh-token.dto';
import { SendVerificationDto } from './dto/send-verification.dto';
import { ServiceProviderForgotPasswordDTO } from './dto/service-provider-forgot-password.dto';
import { ServiceProviderResetPasswordDTO } from './dto/service-provider-reset-password.dto';
import { TokenPayloadDto } from './dto/token-payload.dto';
import { UpdatePasswordDto } from './dto/update-password.dto';
import { UpdateUserFcmTokenDto } from './dto/update-user-fcm-token.dto';
import { UserLoginResponseDto } from './dto/user-login-response.dto';
import { UserLoginDto } from './dto/user-login.dto';
import { VerifyCodeResponseDto } from './dto/verify-code-response.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';
import { AuthMessagesKeys } from './translate.enum';

@Controller('auth')
@ApiTags('auth')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(HeaderInterceptor)
export class AuthController {
    constructor(
        public readonly userService: UsersService,
        public readonly authService: AuthService,
        public readonly usersMapper: UsersMapper,
        private readonly i18n: I18nService,
    ) {}

    @Post('pre-login')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({
        type: UserLoginResponseDto,
        description: 'check user exist before login',
    })
    async checkPhoneNumberExists(
        @Headers('source-type') source: SourceType,
        @Body() preLogin: PreLoginDto,
        @I18nLang() lang: string,
    ): Promise<UserLoginResponseDto> {
        return this.authService.preLogin(preLogin, lang, source);
    }

    @Post('login')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({
        type: UserLoginResponseDto,
        description: 'User info with access token',
    })
    async userLogin(
        @Headers('source-type') source: SourceType,
        @Body() userLoginDto: UserLoginDto,
        @I18nLang() lang: string,
    ): Promise<UserLoginResponseDto> {
        return this.authService.login(userLoginDto, lang, source);
    }

    @Post('/admin/login')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({
        type: UserLoginResponseDto,
        description: 'Admin and Service Provider login',
    })
    async adminLogin(
        @Headers('source-type') source: SourceType,
        @Body() adminLoginDto: AdminLoginDto,
        @I18nLang() lang: string,
    ): Promise<UserLoginResponseDto> {
        await this.userService.findUserByEmail(adminLoginDto.email, lang);
        return this.authService.adminLogin(adminLoginDto, source, lang);
    }

    @Get('guest-login')
    @ApiOkResponse({
        type: UserLoginResponseDto,
        description: 'Guest info with access token',
    })
    guestLogin(): UserLoginResponseDto {
        return this.authService.guestLogin();
    }

    @Post('refresh')
    public async refreshToken(
        @Body() refreshTokenDTO: RefreshTokenDTO,
        @I18nLang() lang: string,
    ): Promise<TokenPayloadDto> {
        return this.authService.refreshToken(refreshTokenDTO, lang);
    }

    @Get('me')
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard)
    @UseInterceptors(AuthUserInterceptor)
    @ApiBearerAuth()
    @ApiOkResponse({ type: UserDto, description: 'current user info' })
    async getCurrentUser(
        @AuthUser() user: UserEntity,
        @I18nLang() lang: string,
    ): Promise<UserDetailsDto> {
        return this.userService.findOneById(user.id, lang);
    }

    @Post('forget-password')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({
        description: 'forget password',
        type: BasicOperationsResponse,
    })
    async forgetPassword(
        @Headers('source-type') source: SourceType,
        @Body() forgetPasswordDto: ForgetPasswordDto,
        @I18nLang() lang: string,
        @Req() req: Request,
    ): Promise<BasicOperationsResponse> {
        return this.authService.forgetPassword(
            req,
            forgetPasswordDto,
            lang,
            source,
        );
    }

    @Post('reset-password')
    @HttpCode(HttpStatus.OK)
    async resetPassword(
        @Body() body: ServiceProviderResetPasswordDTO,
        @I18nLang() lang = Languages.EN,
    ): Promise<BasicOperationsResponse> {
        return this.authService.resetPassword(body.token, body.password, lang);
    }

    @Post('verification/send')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({
        description: 'send verification code',
    })
    async sendVerificationCode(
        @Req() req: Request,
        @Body() sendVerificationDto: SendVerificationDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        const source = req.headers['source-type'];
        return this.authService.sendVerificationCode(
            sendVerificationDto,
            lang,
            source,
        );
    }

    @Post('code-verification')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({
        description: 'verify code',
    })
    async verifyCode(
        @Req() req: Request,
        @Body() forgetPasswordDto: VerifyCodeDto,
        @I18nLang() lang: string,
    ): Promise<VerifyCodeResponseDto> {
        const source = req.headers['source-type'];
        return this.authService.verify(forgetPasswordDto, lang, source);
    }

    @Put('password')
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard)
    @UseInterceptors(AuthUserInterceptor)
    @ApiBearerAuth()
    @ApiOkResponse({
        description: 'Update password',
        type: BasicOperationsResponse,
    })
    async updateNewPassword(
        @Req() req: Request,
        @Body() updatePasswordDto: UpdatePasswordDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        const id = req.user.id;
        return this.authService.updatePassword(id, updatePasswordDto, lang);
    }

    @Put('fcm/password')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({
        description: 'Update password',
        type: BasicOperationsResponse,
    })
    async changePasswordFromFCM(
        @Req() req: Request,
        @Body() updatePasswordDto: UpdatePasswordDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        const source = req.headers['source-type'];
        return this.authService.updatePasswordFromFCM(
            updatePasswordDto,
            lang,
            source,
        );
    }

    @Put('fcm-token')
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard)
    @UseInterceptors(AuthUserInterceptor)
    @ApiBearerAuth()
    @ApiOkResponse({
        description: 'update fcm token',
    })
    updateFcmToken(
        @Req() req: Request,
        @Body() updatefcmTokenDto: UpdateUserFcmTokenDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        const id = req.user.id;
        const deviceId = req.headers['device-id'];
        updatefcmTokenDto.deviceId = deviceId;
        return this.userService.updateFcmToken(id, updatefcmTokenDto, lang);
    }

    @Get('logout')
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard)
    @UseInterceptors(AuthUserInterceptor)
    @ApiBearerAuth()
    @ApiOkResponse({
        description: 'log out user',
    })
    logout(
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        const id = req.user.id;
        const deviceId = req.headers['device-id'];

        return this.userService.logOut(id, deviceId, lang);
    }

    @Post('email-confirmation')
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, RolesGuard)
    @UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
    @ApiOkResponse({
        status: HttpStatus.OK,
        description: 'use this api to send email verification to be verified',
    })
    emailConfirmation(
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        const id = req.user.id;
        return this.userService.confirmEmail(id, lang);
    }

    @Get('email-verification/:token/:lang')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({
        status: HttpStatus.OK,
        description: 'this api is used to verify email',
    })
    async verifyEmail(
        @Res() res: Response,
        @Param('token') token: string,
        @Param('lang') lang: string,
    ): Promise<any> {
        try {
            await this.userService.verifyEmail(token, lang);
            return res.render('sucess-emai-verification', {
                message: await this.i18n.translate(
                    AuthMessagesKeys.EMAIL_VERIFIED_SUCCESSFULLY,
                    {
                        lang,
                    },
                ),
            });
        } catch (err) {
            console.error(err);
            return res.render('failed-emai-verification', {
                message: await this.i18n.translate(
                    AuthMessagesKeys.EMAIL_VERIFICATION_FAILD,
                    {
                        lang,
                    },
                ),
            });
        }
    }

    @Post('service-provider/forgot-password')
    @HttpCode(HttpStatus.OK)
    async serviceProviderForgotPassword(
        @Body() body: ServiceProviderForgotPasswordDTO,
        @I18nLang() lang = Languages.EN,
    ): Promise<BasicOperationsResponse> {
        const user = await this.userService.findUserByEmail(body.email, lang);
        if (!user || user.role !== RoleType.SERVICE_PROVIDER) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.SERVICE_PROVIDER_FORGOT_PASSWORD_NOT_SERVICE_PROVIDER,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        const token = this.authService.generateResetPasswordToken(user);
        await this.authService.sendResetPasswordEmail(user.email, token);
        return {
            isSuccessful: true,
            message: 'Reset password email has been sent',
        };
    }

    @Post('service-provider/reset-password')
    @HttpCode(HttpStatus.OK)
    async serviceProviderResetPassword(
        @Body() body: ServiceProviderResetPasswordDTO,
        @I18nLang() lang = Languages.EN,
    ): Promise<BasicOperationsResponse> {
        const jwtPayload = await this.authService.decodeResetPasswordToken(
            body.token,
            lang,
        );
        const user = await this.userService.findUserById(jwtPayload.id, lang);
        // if (!user || user.role !== RoleType.SERVICE_PROVIDER) {
        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.SERVICE_PROVIDER_FORGOT_PASSWORD_NOT_SERVICE_PROVIDER,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        if (user.password !== jwtPayload.oldPassword) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.SERVICE_PROVIDER_RESET_PASSWORD_EXPIRED_TOKEN,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        await this.authService.updatePassword(
            jwtPayload.id,
            {
                password: body.password,
            },
            lang,
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                AuthMessagesKeys.SERVICE_PROVIDER_RESET_PASSWORD_SUCCESS,
                {
                    lang,
                },
            ),
        };
    }

    @Get('password/change')
    @HttpCode(HttpStatus.OK)
    changePassword(@Res() res: Response) {
        const htmlPath = path.join(process.cwd(), '/views/change-password.hbs');
        res.render(htmlPath, {
            action: '/auth/reset-password',
        });
    }
}
