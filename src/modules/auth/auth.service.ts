import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { Request } from 'express';
import * as admin from 'firebase-admin';
import { readFileSync } from 'fs';
import { handlebars } from 'hbs';
import { I18nService } from 'nestjs-i18n';
import * as path from 'path';

import { RoleType, SourceType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { ConfigService } from '../../config/config.service';
import { ContextService } from '../../providers/context.service';
import { GeneratorService } from '../../shared/services/generator.service';
import { DebugLogger } from '../../shared/services/logger.service';
import { tryCatch } from '../../utils/helpers';
import { UserEntity } from '../users/entities/user.entity';
import { UsersService } from '../users/users.service';
import { AdminLoginDto } from './dto/admin-login.dto';
import { ForgetPasswordDto } from './dto/forget-password.dto';
import { PreLoginDto } from './dto/pre-login.dto';
import { RefreshTokenDTO } from './dto/refresh-token.dto';
import { SendVerificationDto } from './dto/send-verification.dto';
import { TokenPayloadDto } from './dto/token-payload.dto';
import { UpdatePasswordDto } from './dto/update-password.dto';
import { UserLoginResponseDto } from './dto/user-login-response.dto';
import { UserLoginDto } from './dto/user-login.dto';
import { VerifyCodeResponseDto } from './dto/verify-code-response.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';
import { IResetPasswordJWTPayload } from './reset-password-jwt-payload.interface';
import { AuthMessagesKeys } from './translate.enum';

@Injectable()
export class AuthService {
    private static authUserKey = 'user_key';

    // private readonly logger = new PsuedoLogger();

    constructor(
        private readonly jwtService: JwtService,
        private readonly configService: ConfigService,
        private readonly usersService: UsersService,
        private readonly i18n: I18nService,
        private generatorService: GeneratorService,
        private logger: DebugLogger,
    ) {
    }

    async validateUserSource(
        source: SourceType,
        user: UserEntity,
        lang: string,
    ): Promise<void> {
        if (
            (source === SourceType.DOCTOR && user.role !== RoleType.DOCTOR) ||
            (source === SourceType.PATIENT && user.role !== RoleType.PATIENT)
        ) {
            throw new HttpException(
                await this.i18n.translate(AuthMessagesKeys.USER_NOT_FOUND, {
                    lang,
                }),
                HttpStatus.FORBIDDEN,
            );
        }
    }

    async preLogin(
        preLogin: PreLoginDto,
        lang: string,
        source: SourceType,
    ): Promise<UserLoginResponseDto> {
        let role = RoleType.PATIENT;
        if (source === SourceType.DOCTOR) {
            role = RoleType.DOCTOR;
        }
        const user = await this.usersService.findUserByPhone(
            preLogin.phone,
            role,
            lang,
        );

        await this.validateUserSource(source, user, lang);

        if (user.verificationCode !== '') {
            throw new HttpException(
                await this.i18n.translate(
                    AuthMessagesKeys.NEED_VERIFIED_FIRST,
                    {
                        lang,
                    },
                ),
                HttpStatus.FORBIDDEN,
            );
        }

        const userLoginResponse = new UserLoginResponseDto();
        userLoginResponse.userId = user.id;
        userLoginResponse.firebaseUid = user.firebaseUserId;
        userLoginResponse.isSuccessful = true;

        return userLoginResponse;
    }

    async login(
        userLoginDto: UserLoginDto,
        lang: string,
        source: SourceType,
    ): Promise<UserLoginResponseDto> {
        let role = RoleType.PATIENT;
        if (source === SourceType.DOCTOR) {
            role = RoleType.DOCTOR;
        }
        const { isSuccess, value, error } = await tryCatch(
            this.usersService.findUserByPhone(
                userLoginDto.phone,
                role,
                lang,
                userLoginDto.password,
            ),
        );

        if (!isSuccess) {
            throw new HttpException(
                {
                    message: error.message,
                },
                HttpStatus.FORBIDDEN,
            );
        }

        const user = value;

        await this.validateUserSource(source, user, lang);

        const userLoginResponse = new UserLoginResponseDto();
        let tokenPayload = new TokenPayloadDto();
        tokenPayload = this.generateTokens(user.id, user.role);
        userLoginResponse.token = tokenPayload;
        userLoginResponse.userId = user.id;
        userLoginResponse.isSuccessful = true;

        await this.usersService.updateRefreshToken(
            user.id,
            userLoginResponse.token.refreshToken,
        );

        return userLoginResponse;
    }

    async adminLogin(
        adminLoginDto: AdminLoginDto,
        source: SourceType,
        lang: string,
    ): Promise<UserLoginResponseDto> {
        const adminUser = await this.usersService.adminLogin(
            adminLoginDto.email,
            lang,
        );
        if (
            adminUser.role === RoleType.SERVICE_PROVIDER &&
            source !== SourceType.SERVICE_PROVIDER
        ) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        if (!adminUser.password || !adminLoginDto.password) {
            throw new HttpException(
                await this.i18n.translate(AuthMessagesKeys.WRONG_PASSWORD, {
                    lang,
                }),
                HttpStatus.FORBIDDEN,
            );
        }
        const isPasswordCorrect = await bcrypt.compare(
            adminLoginDto.password,
            adminUser.password,
        );

        if (!isPasswordCorrect) {
            throw new HttpException(
                await this.i18n.translate(AuthMessagesKeys.WRONG_PASSWORD, {
                    lang,
                }),
                HttpStatus.FORBIDDEN,
            );
        }

        const userLoginResponse = new UserLoginResponseDto();
        let tokenPayload = new TokenPayloadDto();
        tokenPayload = this.generateTokens(adminUser.id, adminUser.role);
        userLoginResponse.token = tokenPayload;
        userLoginResponse.userId = adminUser.id;
        userLoginResponse.isSuccessful = true;

        await this.usersService.updateRefreshToken(
            adminUser.id,
            userLoginResponse.token.refreshToken,
        );

        return userLoginResponse;
    }

    guestLogin(): UserLoginResponseDto {
        const guestUserId = -1;
        let tokenPayload = new TokenPayloadDto();
        tokenPayload = this.generateTokens(guestUserId, RoleType.GUEST);

        const userLoginResponse = new UserLoginResponseDto();
        userLoginResponse.token = tokenPayload;
        userLoginResponse.userId = guestUserId;
        userLoginResponse.isSuccessful = true;
        return userLoginResponse;
    }

    public generateTokens(id: number, role: string): TokenPayloadDto {
        const accessTokenExpiration =
            this.configService.JWT_ACCESS_TOKEN_EXPIRES_IN;

        const accessToken = this.jwtService.sign(
            { id, role },
            {
                expiresIn: accessTokenExpiration,
                secret: this.configService.JWT_ACCESS_TOKEN_SECRET_KEY,
            },
        );
        const refreshToken = this.jwtService.sign(
            { id, role },
            {
                expiresIn: this.configService.JWT_REFRESH_TOKEN_EXPIRES_IN,
                secret: this.configService.JWT_REFRESH_TOKEN_SECRET_KEY,
            },
        );
        return {
            accessToken,
            refreshToken,
            expiresIn: accessTokenExpiration,
        };
    }

    async refreshToken(
        refreshTokenDTO: RefreshTokenDTO,
        lang: string,
    ): Promise<TokenPayloadDto> {
        const user = await this.usersService.findUserByEmail(
            refreshTokenDTO.email,
            lang,
        );

        if (user.refreshToken !== refreshTokenDTO.refreshToken) {
            throw new HttpException(
                await this.i18n.translate(
                    AuthMessagesKeys.INVALID_REFRESH_TOKEN,
                    { lang },
                ),
                HttpStatus.UNAUTHORIZED,
            );
        }

        const isTokenValid = this.jwtService.verify(
            refreshTokenDTO.refreshToken,
            { secret: this.configService.JWT_REFRESH_TOKEN_SECRET_KEY },
        );

        if (!isTokenValid) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.INVALID_REFRESH_TOKEN,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.UNAUTHORIZED,
            );
        }
        const refreshResponse = this.generateTokens(user.id, user.role);

        await this.usersService.updateRefreshToken(
            user.id,
            refreshResponse.refreshToken,
        );
        return refreshResponse;
    }

    static setAuthUser(user: UserEntity): void {
        console.log(JSON.stringify(user));
        ContextService.set(AuthService.authUserKey, user);
    }

    static getAuthUser(): UserEntity {
        return ContextService.get(AuthService.authUserKey);
    }

    async forgetPassword(
        req: Request,
        forgetPasswordDto: ForgetPasswordDto,
        lang: string,
        source: SourceType,
    ): Promise<BasicOperationsResponse> {
        let role = RoleType.PATIENT;
        if (source === SourceType.DOCTOR) {
            role = RoleType.DOCTOR;
        }
        const user = await this.usersService.findUserByEmailAndRole(
            forgetPasswordDto.email,
            role,
            lang,
        );
        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        const token = this.generateResetPasswordToken(user);
        const url = `${req.protocol}://${req.get('host')}`;
        await this.sendUserResetPasswordEmail(url, user.email, token);
        return {
            isSuccessful: true,
            message: 'code sent successfully',
        };
    }

    async resetPassword(
        token: string,
        password: string,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const jwtPayload = await this.decodeResetPasswordToken(token, lang);
        const user = await this.usersService.findUserById(jwtPayload.id, lang);
        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        if (user.password !== jwtPayload.oldPassword) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.SERVICE_PROVIDER_RESET_PASSWORD_EXPIRED_TOKEN,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        await this.updatePassword(
            jwtPayload.id,
            {
                password,
            },
            lang,
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                AuthMessagesKeys.SERVICE_PROVIDER_RESET_PASSWORD_SUCCESS,
                {
                    lang,
                },
            ),
        };
    }

    async sendVerificationCode(
        sendVerificationDto: SendVerificationDto,
        lang: string,
        source: SourceType,
    ): Promise<BasicOperationsResponse> {
        let role = RoleType.PATIENT;
        if (source === SourceType.DOCTOR) {
            role = RoleType.DOCTOR;
        }

        const user = await this.usersService.findUserByPhone(
            sendVerificationDto.phone,
            role,
            lang,
        );

        await this.validateUserSource(source, user, lang);

        let firebaseUid = user.firebaseUserId;
        try {
            const decodedToken = await admin
                .auth()
                .verifyIdToken(sendVerificationDto.firebaseToken);

            if (
                !firebaseUid &&
                decodedToken.phone_number === sendVerificationDto.phone
            ) {
                await this.usersService.updateUser(
                    user.id,
                    {
                        firebaseUserId: decodedToken.uid,
                    },
                    lang,
                );
                firebaseUid = decodedToken.uid;
            }
            if (decodedToken.uid !== firebaseUid) {
                throw new HttpException(
                    await this.i18n.translate(AuthMessagesKeys.INVALID_TOKEN, {
                        lang,
                    }),
                    HttpStatus.FORBIDDEN,
                );
            }
        } catch (error) {
            throw new HttpException(
                await this.i18n.translate(AuthMessagesKeys.INVALID_TOKEN, {
                    lang,
                }),
                HttpStatus.FORBIDDEN,
            );
        }
        return {
            isSuccessful: true,
            message: 'code sent successfully',
        };
    }

    async verify(
        verifyCodeDto: VerifyCodeDto,
        lang: string,
        source: SourceType,
    ): Promise<VerifyCodeResponseDto> {
        let role = RoleType.PATIENT;
        if (source === SourceType.DOCTOR) {
            role = RoleType.DOCTOR;
        }
        const user = await this.usersService.findUserByPhone(
            verifyCodeDto.phone,
            role,
            lang,
        );
        if (user.verificationCode !== verifyCodeDto.code) {
            throw new HttpException(
                {
                    message: 'code didnt match',
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        await this.usersService.updateVerificationCode(user.id, '');

        const token = this.generateTokens(user.id, user.role);

        return {
            token: token.accessToken,
        };
    }

    async updatePassword(
        id: number,
        updatePasswordDto: UpdatePasswordDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const user = await this.usersService.findOneById(id, lang);

        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        if (updatePasswordDto.oldPassword && user.password) {
            const isPasswordCorrect = await bcrypt.compare(
                user.password,
                updatePasswordDto.oldPassword,
            );

            if (!isPasswordCorrect) {
                throw new HttpException(
                    await this.i18n.translate(AuthMessagesKeys.WRONG_PASSWORD, {
                        lang,
                    }),
                    HttpStatus.FORBIDDEN,
                );
            }
        }
        await this.usersService.updatePassword(id, updatePasswordDto.password);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                AuthMessagesKeys.UPDATE_PASSWORD,
                {
                    lang,
                },
            ),
        };
    }

    async updatePasswordFromFCM(
        updatePasswordDto: UpdatePasswordDto,
        lang: string,
        source: SourceType,
    ): Promise<BasicOperationsResponse> {
        // const user = await this.usersService.findOneById(id, lang);
        let role = RoleType.PATIENT;
        if (source === SourceType.DOCTOR) {
            role = RoleType.DOCTOR;
        }

        const user = await this.usersService.findUserByPhone(
            updatePasswordDto.phone,
            role,
            lang,
        );

        await this.validateUserSource(source, user, lang);

        let firebaseUid = user.firebaseUserId;
        try {
            const decodedToken = await admin
                .auth()
                .verifyIdToken(updatePasswordDto.firebaseToken);

            if (
                !firebaseUid &&
                decodedToken.phone_number === updatePasswordDto.phone
            ) {
                await this.usersService.updateUser(
                    user.id,
                    {
                        firebaseUserId: decodedToken.uid,
                    },
                    lang,
                );
                firebaseUid = decodedToken.uid;
            }
            if (decodedToken.uid !== firebaseUid) {
                throw new HttpException(
                    await this.i18n.translate(AuthMessagesKeys.INVALID_TOKEN, {
                        lang,
                    }),
                    HttpStatus.FORBIDDEN,
                );
            }
        } catch (error) {
            throw new HttpException(
                await this.i18n.translate(AuthMessagesKeys.INVALID_TOKEN, {
                    lang,
                }),
                HttpStatus.FORBIDDEN,
            );
        }

        await this.usersService.updatePassword(
            user.id,
            updatePasswordDto.password,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                AuthMessagesKeys.UPDATE_PASSWORD,
                {
                    lang,
                },
            ),
        };
    }

    generateResetPasswordToken(user: UserEntity): string {
        return this.jwtService.sign(
            {
                id: user.id,
                oldPassword: user.password,
            },
            {
                expiresIn: '2h',
                secret: this.configService.JWT_ACCESS_TOKEN_SECRET_KEY,
            },
        );
    }

    async decodeResetPasswordToken(
        token: string,
        lang: string,
    ): Promise<IResetPasswordJWTPayload> {
        try {
            const payload =
                await this.jwtService.verifyAsync<IResetPasswordJWTPayload>(
                    token,
                    {
                        secret: this.configService.JWT_ACCESS_TOKEN_SECRET_KEY,
                    },
                );
            // eslint-disable-next-line @typescript-eslint/tslint/config
            return payload;
        } catch (error) {
            throw new HttpException(
                await this.i18n.translate(
                    AuthMessagesKeys.SERVICE_PROVIDER_RESET_PASSWORD_EXPIRED_TOKEN,
                    {
                        lang,
                    },
                ),
                HttpStatus.FORBIDDEN,
            );
        }
    }

    async sendResetPasswordEmail(email: string, token: string): Promise<void> {
        try {
            const emailSource = readFileSync(
                path.join(
                    process.cwd(),
                    '/views/service-provider-reset-password.hbs',
                ),
                {
                    encoding: 'utf8',
                },
            );
            const template = handlebars.compile(emailSource);
            const link = `${this.configService.SERVICE_PROVIDER_URL}${this.configService.SERVICE_PROVIDER_RESET_PASSWORD_API}?token=${token}`;
            const html = template({ link });
            await this.usersService.emailService.sendEmail(
                email,
                'Kashf247 Reset Password',
                html,
            );
        } catch (error) {
            this.logger.error(error);
            throw error;
        }
    }

    async sendUserResetPasswordEmail(
        url: string,
        email: string,
        token: string,
    ): Promise<void> {
        try {
            const emailSource = readFileSync(
                path.join(
                    process.cwd(),
                    '/views/service-provider-reset-password.hbs',
                ),
                {
                    encoding: 'utf8',
                },
            );
            const template = handlebars.compile(emailSource);
            const link = `${url}/auth/password/change?token=${token}`;
            const html = template({ link });
            await this.usersService.emailService.sendEmail(
                email,
                'Kashf247 Reset Password',
                html,
            );
        } catch (error) {
            this.logger.error(error);
            throw error;
        }
    }
}
