'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsPhoneNumber, IsString } from 'class-validator';

export class SendVerificationDto {
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @ApiProperty()
    @Expose()
    readonly phone: string;

    @ApiProperty()
    @Expose()
    @IsString()
    firebaseToken: string;

    @ApiProperty()
    @Expose()
    @IsOptional()
    @IsString()
    firebaseUid: string;
}
