'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsPhoneNumber, IsString } from 'class-validator';

export class UpdatePasswordDto {
    @ApiProperty()
    @Expose()
    @IsString()
    password: string;

    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    oldPassword?: string;

    @ApiProperty()
    @Expose()
    @IsString()
    @IsOptional()
    firebaseToken?: string;

    @ApiProperty()
    @Expose()
    @IsString()
    @IsOptional()
    firebaseUid?: string;

    @ApiProperty()
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @IsOptional()
    @Expose()
    phone?: string;
}
