'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsString } from 'class-validator';

import { TokenPayloadDto } from './token-payload.dto';

export class UserLoginResponseDto {
    @ApiProperty({ type: TokenPayloadDto })
    @Expose()
    @Type(() => TokenPayloadDto)
    token: TokenPayloadDto;

    @ApiProperty()
    @IsNumber()
    @Expose()
    userId: number;

    @ApiProperty()
    @IsString()
    @Expose()
    firebaseUid: string;

    @ApiProperty()
    @IsBoolean()
    @Expose()
    isSuccessful: boolean;
}
