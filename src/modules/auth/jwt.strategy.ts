import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { RoleType } from '../../common/constants/types';
import { ConfigService } from '../../config/config.service';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { UsersService } from '../users/users.service';
import { IJwtPayload } from './jwtPayload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor(
        public readonly configService: ConfigService,
        public readonly usersService: UsersService,
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            secretOrKey: configService.JWT_ACCESS_TOKEN_SECRET_KEY,
        });
    }

    async validate(jwtPayload: IJwtPayload): Promise<UserDetailsDto> {
        const timeDiff = jwtPayload.exp - jwtPayload.iat;
        if (timeDiff <= 0) {
            throw new UnauthorizedException();
        }
        if (jwtPayload.id === -1) {
            return {
                id: jwtPayload.id,
                role: RoleType.GUEST,
            };
        }
        const user = await this.usersService.findOneById(
            jwtPayload.id,
            AvailableLanguageCodes.ar,
        );
        if (!user) {
            throw new UnauthorizedException();
        }
        return user;
    }
}
