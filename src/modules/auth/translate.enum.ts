export enum AuthMessagesKeys {
    DOCTOR_NOT_FOUND = 'auth.DOCTOR_NOT_FOUND',
    PATIENT_NOT_FOUND = 'auth.PATIENT_NOT_FOUND',
    REGISTERED_SUCCESSFULLY = 'auth.REGISTERED_SUCCESSFULLY',
    EMAIL_EXISTS = 'auth.EMAIL_EXISTS',
    PHONE_EXISTS = 'auth.PHONE_EXISTS',
    INVALID_REFRESH_TOKEN = 'auth.INVALID_REFRESH_TOKEN',
    INVALID_TOKEN = 'auth.INVALID_TOKEN',
    WRONG_PASSWORD = 'auth.WRONG_PASSWORD',
    UPDATE_PASSWORD = 'auth.UPDATE_PASSWORD',
    UPDATE_FCMTOKEN = 'auth.UPDATE_FCMTOKEN',
    USER_INACTIVE = 'auth.USER_INACTIVE',
    USER_NOT_FOUND = 'auth.USER_NOT_FOUND',
    EMAIL_NOT_FOUND = 'auth.EMAIL_NOT_FOUND',
    EMAIL_VERIFIED_SUCCESSFULLY = 'auth.EMAIL_VERIFIED_SUCCESSFULLY',
    NEED_VERIFIED_FIRST = 'auth.NEED_VERIFIED_FIRST',
    EMAIL_VERIFICATION_FAILD = 'auth.EMAIL_VERIFICATION_FAILD',
    EMAIL_VERIFICATION_SENT = 'auth.EMAIL_VERIFICATION_SENT',
    EMAIL_VERIFICATION_HEADER = 'auth.EMAIL_VERIFICATION_HEADER',
    EMAIL_VERIFICATION_BODY = 'auth.EMAIL_VERIFICATION_BODY',
    EMAIL_VERIFICATION_BUTTON = 'auth.EMAIL_VERIFICATION_BUTTON',
    EMAIL_VERIFICATION_SUBJECT = 'auth.EMAIL_VERIFICATION_SUBJECT',
    SERVICE_PROVIDER_FORGOT_PASSWORD_NOT_SERVICE_PROVIDER = 'auth.SERVICE_PROVIDER_FORGOT_PASSWORD_NOT_SERVICE_PROVIDER',
    SERVICE_PROVIDER_RESET_PASSWORD_EXPIRED_TOKEN = 'auth.SERVICE_PROVIDER_RESET_PASSWORD_EXPIRED_TOKEN',
    SERVICE_PROVIDER_RESET_PASSWORD_SUCCESS = 'auth.SERVICE_PROVIDER_RESET_PASSWORD_SUCCESS',
    WELCOME_MESSAGE = 'auth.WELCOME_MESSAGE',
    NEW_DOCTOR_REGISTER_SUBJECT = 'auth.NEW_DOCTOR_REGISTER_SUBJECT',
    NEW_DOCTOR_REGISTER = 'auth.NEW_DOCTOR_REGISTER',
    NEW_PATIENT_REGISTER_SUBJECT = 'auth.NEW_PATIENT_REGISTER_SUBJECT',
    NEW_PATIENT_REGISTER = 'auth.NEW_PATIENT_REGISTER',
    NEW_SERVICE_PROVIDER_REGISTER_SUBJECT = 'auth.NEW_SERVICE_PROVIDER_REGISTER_SUBJECT',
    NEW_SERVICE_PROVIDER_REGISTER = 'auth.NEW_SERVICE_PROVIDER_REGISTER',
}

export enum SyndicateUpdateMessageKeys {
    SYNDICATE_UPDATE_SUBJECT = 'doctor.SYNDICATE_UPDATE_SUBJECT',
    SYNDICATE_UPDATE_BODY = 'doctor.SYNDICATE_UPDATE_BODY',
}
