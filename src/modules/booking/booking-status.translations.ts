import { BookingStatus } from '../../common/constants/types';

export const BookingStatusTranslations: Record<
    BookingStatus,
    { en: string; ar: string }
> = {
    [BookingStatus.AWAITING_DATE_CONFIRMATION]: {
        en: 'Awaiting Date Confirmation',
        ar: 'في انتظار تأكيد الموعد',
    },
    [BookingStatus.PENDING]: {
        en: 'Pending',
        ar: 'قيد الانتظار',
    },
    [BookingStatus.CONFIRMED]: {
        en: 'Confirmed',
        ar: 'تم التأكيد',
    },
    [BookingStatus.COMPLETED]: {
        en: 'Completed',
        ar: 'مكتمل',
    },
    [BookingStatus.CANCELED]: {
        en: 'Canceled',
        ar: 'تم الالغاء',
    },
    [BookingStatus.PAID]: {
        en: 'Paid',
        ar: 'تم الدفع',
    },
    [BookingStatus.PENDINGPAYMENT]: {
        en: 'Pending Payment',
        ar: 'قيد انتظار الدفع',
    },
};
