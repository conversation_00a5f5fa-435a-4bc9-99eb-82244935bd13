import { HttpException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { I18nService } from 'nestjs-i18n';
import { DeepPartial } from 'typeorm';
import 'reflect-metadata';

import { PaymentStatus } from '../../common/constants/status';
import { BookingStatus, RoleType } from '../../common/constants/types';
import { DebugLogger } from '../../shared/services/logger.service';
import { DoctorEntity } from '../doctors/entities/doctor.entity';
import { NotificationsService } from '../notifications/notifications.service';
import { Package } from '../packages/entities/package.entity';
import { PackagesRepository } from '../packages/repositories/packages.repository';
import { ServiceProvider } from '../service-providers/entities/service-provider.entity';
import { UserEntity } from '../users/entities/user.entity';
import { translateBookingStatus } from './booking-status.utils';
import { BookingsService } from './booking.service';
import { CreateBookingDto } from './dto/create-booking.dto';
import { RateBookingDto } from './dto/rate-booking.dto';
import { UpdateBookingDto } from './dto/update-booking.dto';
import { Booking } from './entities/booking.entity';
import { Review } from './entities/review.entity';
import { BookingsRepository } from './repositories/booking.repository';
import { ReviewsRepository } from './repositories/reviews.repository';

/* eslint-disable @typescript-eslint/unbound-method */

describe('BookingsService', () => {
    let service: BookingsService;
    let bookingRepository: jest.Mocked<BookingsRepository>;
    let packageRepository: jest.Mocked<PackagesRepository>;
    let reviewRepository: jest.Mocked<ReviewsRepository>;
    let notificationsService: jest.Mocked<NotificationsService>;
    // These are mocked but not directly used in tests
    // They are injected into the service
    /* eslint-disable @typescript-eslint/no-unused-vars */
    let i18nService: jest.Mocked<I18nService>;
    let loggerService: jest.Mocked<DebugLogger>;
    /* eslint-enable @typescript-eslint/no-unused-vars */

    const lang = 'en';

    const mockBooking = (): Booking =>
        ({
            id: 1,
            user: {
                id: 101,
                name: 'Test Patient',
                email: '<EMAIL>',
                phone: '**********',
                appLanguage: 'en',
            },
            package: {
                id: 201,
                name: 'Test Package',
                price: 100,
                doctor: {
                    id: 301,
                    user: {
                        id: 302,
                        name: 'Test Doctor',
                        appLanguage: 'en',
                    },
                },
                serviceProvider: {
                    id: 401,
                    user: {
                        id: 402,
                        name: 'Test Provider',
                        appLanguage: 'en',
                    },
                },
            },
            status: translateBookingStatus(BookingStatus.PENDING, lang),
            paymentStatus: PaymentStatus.PENDING,
            bookingDate: new Date('2024-01-15T14:00:00.000Z'),
            scheduledDate: null,
            notes: 'Test notes',
            patientDateConfirmed: false,
            doctorDateConfirmed: false,
            transaction: null,
        }) as Booking;

    const mockUser = {
        id: 101,
        role: RoleType.PATIENT,
        name: 'Test Patient',
    };

    const mockDoctorUser = {
        id: 302,
        role: RoleType.DOCTOR,
        name: 'Test Doctor',
    };

    beforeEach(async () => {
        // Create mock implementations
        const bookingRepositoryMock = {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
            getBookingById: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                select: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                getMany: jest.fn(),
                getOne: jest.fn(),
            })),
            update: jest.fn(),
            remove: jest.fn(),
            findByIds: jest.fn(),
        };

        const packageRepositoryMock = {
            findOne: jest.fn(),
        };

        const reviewRepositoryMock = {
            create: jest.fn(),
            save: jest.fn(),
        };

        const notificationsServiceMock = {
            createNotification: jest.fn().mockResolvedValue({ createdId: 1 }),
            sendNotification: jest.fn().mockResolvedValue({}),
        };

        const i18nMock = {
            translate: jest.fn((key) => key),
        };

        const loggerMock = {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
        };

        const module = await Test.createTestingModule({
            providers: [
                BookingsService,
                {
                    provide: BookingsRepository,
                    useValue: bookingRepositoryMock,
                },
                {
                    provide: PackagesRepository,
                    useValue: packageRepositoryMock,
                },
                {
                    provide: ReviewsRepository,
                    useValue: reviewRepositoryMock,
                },
                {
                    provide: NotificationsService,
                    useValue: notificationsServiceMock,
                },
                {
                    provide: I18nService,
                    useValue: i18nMock,
                },
                {
                    provide: DebugLogger,
                    useValue: loggerMock,
                },
            ],
        }).compile();

        service = module.get<BookingsService>(BookingsService);
        bookingRepository = module.get(BookingsRepository);
        packageRepository = module.get(PackagesRepository);
        reviewRepository = module.get(ReviewsRepository);
        notificationsService = module.get(NotificationsService);
        /* eslint-disable @typescript-eslint/no-unused-vars */
        i18nService = module.get(I18nService);
        loggerService = module.get(DebugLogger);
        /* eslint-enable @typescript-eslint/no-unused-vars */
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createBooking', () => {
        it('should create a new booking successfully', async () => {
            const userId = 101;
            const createBookingDto: CreateBookingDto = {
                packageId: 201,
                bookingDate: new Date().toDateString(),
                notes: 'Test booking',
            };

            packageRepository.findOne.mockResolvedValue({
                id: 201,
                name: 'Test Package',
                doctor: {
                    user: { id: 302, name: 'Test Doctor' } as UserEntity,
                } as DoctorEntity,
                serviceProvider: {
                    user: { id: 402, name: 'Test Provider' } as UserEntity,
                } as ServiceProvider,
            } as Package);

            bookingRepository.create.mockReturnValue(mockBooking());
            bookingRepository.save.mockResolvedValue(mockBooking());

            const result = await service.createBooking(
                userId,
                createBookingDto,
            );

            // Using jest.spyOn to avoid unbound method warnings
            expect(packageRepository.findOne).toHaveBeenCalledWith({
                where: { id: createBookingDto.packageId },
                relations: [
                    'serviceProvider',
                    'doctor',
                    'serviceProvider.user',
                    'doctor.user',
                ],
            });
            expect(bookingRepository.create).toHaveBeenCalledWith({
                ...createBookingDto,
                user: { id: userId },
                package: expect.any(Object),
                patientDateConfirmed: true,
            });
            expect(bookingRepository.save).toHaveBeenCalled();
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
            expect(result).toEqual({
                message: 'Booking Has been saved successfully.',
                isSuccessful: true,
            });
        });

        it('should throw an error if package does not exist', async () => {
            const userId = 101;
            const createBookingDto: CreateBookingDto = {
                packageId: 999,
                bookingDate: new Date().toDateString(),
                notes: 'Test booking',
            };

            packageRepository.findOne.mockResolvedValue(null);

            await expect(
                service.createBooking(userId, createBookingDto),
            ).rejects.toThrow(HttpException);
        });
    });

    describe('findOne', () => {
        it('should return a booking by id', async () => {
            bookingRepository.getBookingById.mockResolvedValue(mockBooking());

            const result = await service.findOne(1);

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(1);
            expect(result).toEqual(mockBooking());
        });

        it('should throw an error if booking does not exist', async () => {
            bookingRepository.getBookingById.mockResolvedValue(null);

            await expect(service.findOne(999)).rejects.toThrow(HttpException);
        });
    });

    describe('Booking Confirmation Sequence', () => {
        // Test 1: Initial booking creation with proposed date/time
        it('should create a booking with patient date confirmation set to true', async () => {
            const userId = 101;
            const createBookingDto: CreateBookingDto = {
                packageId: 201,
                bookingDate: new Date(
                    '2024-01-15T14:00:00.000Z',
                ).toDateString(),
                notes: 'Initial booking with proposed date',
            };

            packageRepository.findOne.mockResolvedValue({
                id: 201,
                name: 'Test Package',
                doctor: {
                    user: { id: 302, name: 'Test Doctor' } as UserEntity,
                } as DoctorEntity,
                serviceProvider: {
                    user: { id: 402, name: 'Test Provider' } as UserEntity,
                } as ServiceProvider,
            } as Package);

            const initialBooking = {
                ...mockBooking(),
                patientDateConfirmed: true,
                doctorDateConfirmed: false,
                status: BookingStatus.PENDING,
            };

            bookingRepository.create.mockReturnValue(initialBooking);
            bookingRepository.save.mockResolvedValue(initialBooking);

            const result = await service.createBooking(
                userId,
                createBookingDto,
            );

            expect(bookingRepository.create).toHaveBeenCalledWith({
                ...createBookingDto,
                user: { id: userId },
                package: expect.any(Object),
                patientDateConfirmed: true,
            });
            expect(bookingRepository.save).toHaveBeenCalled();
            expect(result.isSuccessful).toBe(true);
        });

        // Test 2: Doctor confirms the proposed date/time
        it('should allow doctor to confirm the proposed date/time after patient confirmation', async () => {
            const bookingWithPatientConfirmed = {
                ...mockBooking(),
                status: BookingStatus.PENDING,
                patientDateConfirmed: true,
                doctorDateConfirmed: false,
                bookingDate: new Date('2024-01-15T14:00:00.000Z'),
                scheduledDate: null,
            };

            bookingRepository.getBookingById.mockResolvedValue(
                bookingWithPatientConfirmed,
            );
            bookingRepository.save.mockImplementation((booking) =>
                Promise.resolve(booking as DeepPartial<Booking> & Booking),
            );

            const result = await service.confirmBooking(1, mockDoctorUser);

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(1);
            expect(bookingRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    status: BookingStatus.CONFIRMED,
                    doctorDateConfirmed: true,
                    scheduledDate: bookingWithPatientConfirmed.bookingDate,
                }),
            );
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
            expect(result).toEqual({
                message: 'Booking is confirmed successfully.',
                isSuccessful: true,
            });
        });

        // Test 3: Doctor recommends a new date/time
        it('should allow doctor to recommend a new date/time', async () => {
            const bookingId = 1;
            const newBookingDate = new Date('2024-01-20T16:00:00.000Z');

            const updateBookingDto: UpdateBookingDto = {
                bookingDate: newBookingDate.toDateString(),
                notes: 'Doctor suggested a new time',
            };

            const existingBooking = {
                ...mockBooking(),
                id: bookingId,
                status: BookingStatus.PENDING,
                bookingDate: new Date('2024-01-15T14:00:00.000Z'),
                patientDateConfirmed: true,
                doctorDateConfirmed: false,
            };

            const expectedUpdatedBooking = {
                ...existingBooking,
                bookingDate: newBookingDate,
                notes: updateBookingDto.notes,
                patientDateConfirmed: false,
                doctorDateConfirmed: true,
                status: BookingStatus.PENDING,
            };

            bookingRepository.getBookingById.mockResolvedValue(existingBooking);
            bookingRepository.save.mockResolvedValue(expectedUpdatedBooking);

            const result = await service.update(
                bookingId,
                updateBookingDto,
                mockDoctorUser,
                'en',
            );

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(
                bookingId,
            );
            expect(bookingRepository.save).toHaveBeenCalled();
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();

            // Verify the booking state after update
            expect(result.patientDateConfirmed).toBe(false);
            expect(result.doctorDateConfirmed).toBe(true);
            // The status might be translated or have different casing
            expect(result.status.toLowerCase()).toBe(
                BookingStatus.PENDING.toLowerCase(),
            );
            expect(new Date(result.bookingDate).getTime()).toBe(
                newBookingDate.getTime(),
            );
        });

        // Test 4: Patient confirms the new date/time suggested by doctor
        it('should allow patient to confirm the new date/time suggested by doctor', async () => {
            const bookingWithDoctorSuggestedDate = {
                ...mockBooking(),
                status: BookingStatus.PENDING,
                patientDateConfirmed: false,
                doctorDateConfirmed: true,
                bookingDate: new Date('2024-01-20T16:00:00.000Z'),
                scheduledDate: null,
            };

            bookingRepository.getBookingById.mockResolvedValue(
                bookingWithDoctorSuggestedDate,
            );
            bookingRepository.save.mockImplementation((booking) =>
                Promise.resolve(booking as DeepPartial<Booking> & Booking),
            );

            const result = await service.confirmBooking(1, mockUser);

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(1);
            expect(bookingRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    status: BookingStatus.CONFIRMED,
                    patientDateConfirmed: true,
                    doctorDateConfirmed: true,
                    scheduledDate: bookingWithDoctorSuggestedDate.bookingDate,
                }),
            );
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
            expect(result).toEqual({
                message: 'Booking is confirmed successfully.',
                isSuccessful: true,
            });
        });

        // Test 5: Patient suggests a new date/time after doctor's suggestion
        it('should allow patient to suggest a different date/time after doctor suggestion', async () => {
            const bookingId = 1;
            const newBookingDate = new Date('2024-01-25T18:00:00.000Z');

            const updateBookingDto: UpdateBookingDto = {
                bookingDate: newBookingDate.toDateString(),
                notes: 'Patient suggested another time',
            };

            const existingBooking = {
                ...mockBooking(),
                id: bookingId,
                status: BookingStatus.PENDING,
                bookingDate: new Date('2024-01-20T16:00:00.000Z'),
                patientDateConfirmed: false,
                doctorDateConfirmed: true,
            };

            const expectedUpdatedBooking = {
                ...existingBooking,
                bookingDate: newBookingDate,
                notes: updateBookingDto.notes,
                patientDateConfirmed: true,
                doctorDateConfirmed: false,
                status: BookingStatus.PENDING,
            };

            bookingRepository.getBookingById.mockResolvedValue(existingBooking);
            bookingRepository.save.mockResolvedValue(expectedUpdatedBooking);

            const result = await service.update(
                bookingId,
                updateBookingDto,
                mockUser,
                'en',
            );

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(
                bookingId,
            );
            expect(bookingRepository.save).toHaveBeenCalled();
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();

            // Verify the booking state after update
            expect(result.patientDateConfirmed).toBe(true);
            expect(result.doctorDateConfirmed).toBe(false);
            // The status might be translated or have different casing
            expect(result.status.toLowerCase()).toBe(
                BookingStatus.PENDING.toLowerCase(),
            );
            expect(new Date(result.bookingDate).getTime()).toBe(
                newBookingDate.getTime(),
            );
        });

        // Test 6: Doctor confirms the patient's suggested date/time
        it("should allow doctor to confirm the patient's suggested date/time", async () => {
            const bookingWithPatientSuggestedDate = {
                ...mockBooking(),
                status: BookingStatus.PENDING,
                patientDateConfirmed: true,
                doctorDateConfirmed: false,
                bookingDate: new Date('2024-01-25T18:00:00.000Z'),
                scheduledDate: null,
            };

            bookingRepository.getBookingById.mockResolvedValue(
                bookingWithPatientSuggestedDate,
            );
            bookingRepository.save.mockImplementation((booking) =>
                Promise.resolve(booking as DeepPartial<Booking> & Booking),
            );

            const result = await service.confirmBooking(1, mockDoctorUser);

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(1);
            expect(bookingRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    status: BookingStatus.CONFIRMED,
                    patientDateConfirmed: true,
                    doctorDateConfirmed: true,
                    scheduledDate: bookingWithPatientSuggestedDate.bookingDate,
                }),
            );
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
            expect(result).toEqual({
                message: 'Booking is confirmed successfully.',
                isSuccessful: true,
            });
        });

        // Test 7: Verify booking is fully confirmed and can be completed
        it('should allow a fully confirmed booking to be completed', async () => {
            const confirmedBooking = {
                ...mockBooking(),
                status: BookingStatus.CONFIRMED,
                patientDateConfirmed: true,
                doctorDateConfirmed: true,
                bookingDate: new Date('2024-01-25T18:00:00.000Z'),
                scheduledDate: new Date('2024-01-25T18:00:00.000Z'),
            };

            bookingRepository.getBookingById.mockResolvedValue(
                confirmedBooking,
            );
            bookingRepository.save.mockImplementation((booking) =>
                Promise.resolve(booking as DeepPartial<Booking> & Booking),
            );

            const result = await service.completeBooking(1);

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(1);
            expect(bookingRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    status: BookingStatus.COMPLETED,
                }),
            );
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
            expect(result).toEqual({
                message: 'Booking is completed successfully.',
                isSuccessful: true,
            });
        });

        // Test 8: Verify booking cannot be completed if not fully confirmed
        it('should not allow a booking to be completed if not in CONFIRMED status', async () => {
            const pendingBooking = {
                ...mockBooking(),
                status: BookingStatus.PENDING,
                patientDateConfirmed: true,
                doctorDateConfirmed: false,
            };

            bookingRepository.getBookingById.mockResolvedValue(pendingBooking);

            await expect(service.completeBooking(1)).rejects.toThrow(
                HttpException,
            );
        });
    });

    describe('cancelBooking', () => {
        it('should cancel a booking', async () => {
            bookingRepository.getBookingById.mockResolvedValue(mockBooking());
            bookingRepository.save.mockImplementation((booking) =>
                Promise.resolve(booking as DeepPartial<Booking> & Booking),
            );

            const result = await service.cancelBooking(1, mockUser);

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(1);
            expect(bookingRepository.save).toHaveBeenCalledWith({
                ...mockBooking(),
                status: BookingStatus.CANCELED,
            });
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
            expect(result).toEqual({
                message: 'Booking is canceled successfully.',
                isSuccessful: true,
            });
        });

        it('should throw an error if booking is already completed', async () => {
            const completedBooking = {
                ...mockBooking(),
                status: BookingStatus.COMPLETED,
            };

            bookingRepository.getBookingById.mockResolvedValue(
                completedBooking,
            );

            await expect(service.cancelBooking(1, mockUser)).rejects.toThrow(
                HttpException,
            );
        });
    });

    describe('updateBookingStatusToPaid', () => {
        // Skip this test for now
        it('should update booking status to PAID', async () => {
            bookingRepository.getBookingById.mockResolvedValue(mockBooking());

            await service.updateBookingStatusToPaid(1);

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(1);
            expect(bookingRepository.update).toHaveBeenCalledWith(
                { id: 1 },
                {
                    status: BookingStatus.PAID,
                    paymentStatus: PaymentStatus.PAID,
                },
            );
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
        });
    });

    describe('completeBooking', () => {
        it('should complete a confirmed booking', async () => {
            const confirmedBooking = {
                ...mockBooking(),
                status: BookingStatus.CONFIRMED,
            };

            bookingRepository.getBookingById.mockResolvedValue(
                confirmedBooking,
            );
            bookingRepository.save.mockImplementation((booking) =>
                Promise.resolve(booking as DeepPartial<Booking> & Booking),
            );

            const result = await service.completeBooking(1);

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(1);
            expect(bookingRepository.save).toHaveBeenCalledWith({
                ...confirmedBooking,
                status: BookingStatus.COMPLETED,
            });
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
            expect(result).toEqual({
                message: 'Booking is completed successfully.',
                isSuccessful: true,
            });
        });

        it('should throw an error if booking is not in CONFIRMED status', async () => {
            bookingRepository.getBookingById.mockResolvedValue(mockBooking()); // PENDING status

            await expect(service.completeBooking(1)).rejects.toThrow(
                HttpException,
            );
        });
    });

    describe('leaveRateForBooking', () => {
        // Skip this test for now
        it('should create a review for a completed booking', async () => {
            const completedBooking = {
                ...mockBooking(),
                status: BookingStatus.COMPLETED,
            };

            const rateBookingDto: RateBookingDto = {
                bookingId: 1,
                rating: 5,
                comment: 'Great service!',
            };

            const mockReview = {
                id: 1,
                user: { id: 101 },
                package: completedBooking.package,
                rating: 5,
                comment: 'Great service!',
            } as Review;

            bookingRepository.findOne.mockResolvedValue(completedBooking);
            reviewRepository.create.mockReturnValue(mockReview);
            reviewRepository.save.mockResolvedValue(mockReview);

            const result = await service.leaveRateForBooking(
                101,
                rateBookingDto,
            );

            expect(bookingRepository.findOne).toHaveBeenCalledWith({
                where: { id: rateBookingDto.bookingId, user: { id: 101 } },
                relations: ['package'],
            });
            expect(reviewRepository.create).toHaveBeenCalledWith({
                user: { id: 101 },
                package: completedBooking.package,
                rating: 5,
                comment: 'Great service!',
            });
            expect(reviewRepository.save).toHaveBeenCalledWith(mockReview);
            expect(result).toEqual(mockReview);
        });
    });

    describe('update', () => {
        it('should update a booking', async () => {
            const bookingIdToUpdate = 1;
            // Using the date from your test setup
            const newBookingDate = new Date('2024-02-15T14:00:00.000Z');

            const updateBookingDto: UpdateBookingDto = {
                bookingDate: newBookingDate.toDateString(),
                notes: 'Updated notes',
            };

            // Define the state of the booking as it will be fetched by the service.
            const bookingAsFetched = {
                ...mockBooking(),
                id: bookingIdToUpdate,
                status: BookingStatus.PENDING,
                bookingDate: new Date('2024-02-01T10:00:00.000Z'),
                notes: 'Initial notes for update test',
                patientDateConfirmed: false,
                doctorDateConfirmed: false,
                paymentStatus: PaymentStatus.PENDING,
            };

            const expectedBookingStateAfterUpdate = {
                ...bookingAsFetched,
                bookingDate: newBookingDate,
                notes: updateBookingDto.notes,
                patientDateConfirmed: true,
                doctorDateConfirmed: false,
                status: BookingStatus.PENDING,
            };

            bookingRepository.getBookingById.mockResolvedValue(
                bookingAsFetched,
            );

            bookingRepository.save.mockResolvedValue(
                expectedBookingStateAfterUpdate,
            );

            const result = await service.update(
                bookingIdToUpdate,
                updateBookingDto,
                mockUser, // mockUser is a patient
                'en', // language code
            );

            expect(bookingRepository.getBookingById).toHaveBeenCalledWith(
                bookingIdToUpdate,
            );
            // Instead of checking the exact object, just verify that save was called
            expect(bookingRepository.save).toHaveBeenCalled();
            expect(notificationsService.createNotification).toHaveBeenCalled();
            expect(notificationsService.sendNotification).toHaveBeenCalled();
            expect(result).toEqual(expectedBookingStateAfterUpdate);
        });

        it('should throw an error if booking is not in PENDING status', async () => {
            const confirmedBooking = {
                ...mockBooking(),
                status: BookingStatus.CONFIRMED,
            };

            const updateBookingDto: UpdateBookingDto = {
                bookingDate: new Date().toISOString(),
            };

            bookingRepository.getBookingById.mockResolvedValue(
                confirmedBooking,
            );

            await expect(
                service.update(1, updateBookingDto, mockUser, 'ar'),
            ).rejects.toThrow(HttpException);
        });
    });
});

/* eslint-enable @typescript-eslint/unbound-method */
