import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import {
    ConsultationRequestStatus,
    PaymentStatus,
} from '../../common/constants/status';
import { BookingStatus, RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { DebugLogger } from '../../shared/services/logger.service';
import { ConsultationRequestMessagesKeys, NotificationTyps } from '../consultation-requests/notification.enum';
import { CreateNotificationDto } from '../notifications/dto/create-notification.dto';
import { NotificationsService } from '../notifications/notifications.service';
import { PackagesRepository } from '../packages/repositories/packages.repository';
import { translateBookingStatus } from './booking-status.utils';
import { CreateBookingDto } from './dto/create-booking.dto';
import { RateBookingDto } from './dto/rate-booking.dto';
import { UpdateBookingDto } from './dto/update-booking.dto';
import { Booking } from './entities/booking.entity';
import { Review } from './entities/review.entity';
import { BookingsRepository } from './repositories/booking.repository';
import { ReviewsRepository } from './repositories/reviews.repository';

@Injectable()
export class BookingsService {
    constructor(
        private readonly bookingRepository: BookingsRepository,
        private readonly packageRepository: PackagesRepository,
        private readonly reviewRepository: ReviewsRepository,
        private readonly notificationsService: NotificationsService,
        private readonly i18n: I18nService,
        private readonly logger: DebugLogger,
    ) {}

    // Create a new booking
    async createBooking(
        userId: number,
        createBookingDto: CreateBookingDto,
    ): Promise<BasicOperationsResponse> {
        // Validate package exists
        const pkg = await this.packageRepository.findOne({
            where: { id: createBookingDto.packageId },
            relations: [
                'serviceProvider',
                'doctor',
                'serviceProvider.user',
                'doctor.user',
            ],
        });

        if (!pkg) {
            throw new HttpException(
                {
                    message: `Package with ID ${createBookingDto.packageId} not found`,
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const booking = this.bookingRepository.create({
            ...createBookingDto,
            user: { id: userId },
            package: pkg,
            patientDateConfirmed: true,
        });

        const createdBooking = await this.bookingRepository.save(booking);
        await this.sendNewBookingNotification(createdBooking);

        return {
            message: 'Booking Has been saved successfully.',
            isSuccessful: true,
        };
    }

    // Fetch all bookings for a user
    async getPatientBookings(userId: number, lang: string): Promise<Booking[]> {
        let books = await this.bookingRepository
            .createQueryBuilder('booking')
            .leftJoinAndSelect('booking.user', 'user')
            .leftJoinAndSelect('booking.package', 'package')
            .select([
                'booking.id',
                'booking.doctorDateConfirmed',
                'booking.patientDateConfirmed',
                'booking.bookingDate',
                'booking.scheduledDate',
                'booking.notes',
                'booking.status',
                'user.id',
                'user.name',
                'package.id',
                'package.name',
            ])
            .where('user.id = :userId', { userId })
            .getMany();

        books = books.map((booking) => ({
            ...booking,
            status: translateBookingStatus(booking.status, lang),
        }));

        return books;
    }

    async getDoctorBookings(userId: number, lang: string): Promise<Booking[]> {
        let books = await this.bookingRepository
            .createQueryBuilder('booking')
            .leftJoinAndSelect('booking.user', 'user')
            .leftJoinAndSelect('booking.package', 'package')
            .leftJoinAndSelect('package.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'dUser')
            .select([
                'booking.id',
                'booking.doctorDateConfirmed',
                'booking.patientDateConfirmed',
                'booking.bookingDate',
                'booking.scheduledDate',
                'booking.notes',
                'booking.status',
                'user.id',
                'user.name',
                'package.id',
                'package.name',
            ])
            .where('dUser.id = :userId', { userId })
            .getMany();

        books = books.map((booking) => ({
            ...booking,
            status: translateBookingStatus(booking.status, lang),
        }));

        return books;
    }

    async getServiceProvidersBookings(
        userId: number,
        lang: string,
    ): Promise<Booking[]> {
        let books = await this.bookingRepository
            .createQueryBuilder('booking')
            .leftJoinAndSelect('booking.user', 'user')
            .leftJoinAndSelect('booking.package', 'package')
            .leftJoinAndSelect('package.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'spUser')
            .select([
                'booking.id',
                'booking.doctorDateConfirmed',
                'booking.patientDateConfirmed',
                'booking.bookingDate',
                'booking.scheduledDate',
                'booking.notes',
                'booking.status',
                'user.id',
                'user.name',
                'package.id',
                'package.name',
            ])
            .where('spUser.id = :userId', { userId })
            .getMany();

        books = books.map((booking) => ({
            ...booking,
            status: translateBookingStatus(booking.status, lang),
        }));

        return books;
    }

    async findAll(lang: string): Promise<Booking[]> {
        const query = this.bookingRepository
            .createQueryBuilder('booking')
            .leftJoinAndSelect('booking.user', 'user')
            .leftJoinAndSelect('booking.package', 'package')
            .select([
                'booking.id',
                'booking.doctorDateConfirmed',
                'booking.patientDateConfirmed',
                'booking.bookingDate',
                'booking.scheduledDate',
                'booking.notes',
                'booking.status',
                'user.id',
                'user.name',
                'package.id',
                'package.name',
            ]);

        // if (userId) {
        //     query.where('user.id = :userId', { userId });
        // }

        let books = await query.getMany();

        books = books.map((booking) => ({
            ...booking,
            status: translateBookingStatus(booking.status, lang),
        }));

        return books;
    }

    async findOne(id: number, lang = 'en'): Promise<Booking> {
        const booking = await this.bookingRepository.getBookingById(id);
        if (!booking) {
            throw new HttpException(
                {
                    message: `Booking with ID ${id} not found`,
                },
                HttpStatus.NOT_FOUND,
            );
        }

        booking.status = translateBookingStatus(booking.status, lang);

        return booking;
    }

    async remove(id: number): Promise<BasicOperationsResponse> {
        const booking = await this.bookingRepository.getBookingById(id);

        const removedBooking = await this.bookingRepository.remove(booking);

        await this.sendBookingRemovedNotification(removedBooking);
        return {
            message: 'Booking is deleted successfully.',
            isSuccessful: true,
        };
    }

    async confirmBooking(
        id: number,
        user: Express.User,
    ): Promise<BasicOperationsResponse> {
        const booking = await this.bookingRepository.getBookingById(id);

        if (booking.status !== BookingStatus.PENDING) {
            throw new HttpException(
                {
                    message: 'Booking can only be confirmed when pending',
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        switch (user.role) {
            case RoleType.DOCTOR:
            case RoleType.SERVICE_PROVIDER:
                booking.doctorDateConfirmed = true;
                break;
            case RoleType.PATIENT:
                booking.patientDateConfirmed = true;
        }

        if (booking.patientDateConfirmed && booking.doctorDateConfirmed) {
            booking.status = BookingStatus.CONFIRMED;
        }

        booking.scheduledDate = booking.bookingDate;
        const createdBooking = await this.bookingRepository.save(booking);
        await this.sendBookingConfirmationNotification(createdBooking, user);
        return {
            message: 'Booking is confirmed successfully.',
            isSuccessful: true,
        };
    }

    async cancelBooking(
        id: number,
        user: Express.User,
    ): Promise<BasicOperationsResponse> {
        const booking = await this.bookingRepository.getBookingById(id);

        if (booking.status === BookingStatus.COMPLETED) {
            throw new HttpException(
                {
                    message: 'Cannot cancel completed booking',
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        booking.status = BookingStatus.CANCELED;
        const savedBooking = await this.bookingRepository.save(booking);

        await this.sendBookingCancellationNotification(savedBooking, user);

        return {
            message: 'Booking is canceled successfully.',
            isSuccessful: true,
        };
    }

    async completeBooking(id: number): Promise<BasicOperationsResponse> {
        const booking = await this.bookingRepository.getBookingById(id);

        if (booking.status !== BookingStatus.CONFIRMED) {
            throw new HttpException(
                {
                    message: 'Only confirmed bookings can be completed',
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        booking.status = BookingStatus.COMPLETED;
        const completedBooking = await this.bookingRepository.save(booking);

        await this.sendBookingCompletedNotification(completedBooking);
        return {
            message: 'Booking is completed successfully.',
            isSuccessful: true,
        };
    }

    // Rate and review a package for a completed booking
    async leaveRateForBooking(
        userId: number,
        rateBookingDto: RateBookingDto,
    ): Promise<Review> {
        // Validate booking exists and belongs to the user
        const booking = await this.bookingRepository.findOne({
            where: { id: rateBookingDto.bookingId, user: { id: userId } },
            relations: ['package'],
        });

        if (!booking) {
            throw new HttpException(
                {
                    message: `Booking with ID ${rateBookingDto.bookingId} not found`,
                },
                HttpStatus.NOT_FOUND,
            );
        }

        // Ensure booking is completed before allowing a review
        if (booking.status !== BookingStatus.COMPLETED) {
            throw new HttpException(
                {
                    message: 'You can only review completed bookings',
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        // Create a review
        const review = this.reviewRepository.create({
            user: { id: userId },
            package: booking.package,
            rating: rateBookingDto.rating,
            comment: rateBookingDto.comment,
        });
        return this.reviewRepository.save(review);
    }

    async update(
        id: number,
        updateBookingDto: UpdateBookingDto,
        user: Express.User,
        lang: string,
    ): Promise<Booking> {
        const booking = await this.bookingRepository.getBookingById(id);

        if (!booking) {
            throw new HttpException(
                { message: `Booking with ID ${id} not found` },
                HttpStatus.NOT_FOUND,
            );
        }

        if (booking.status !== BookingStatus.PENDING) {
            throw new HttpException(
                { message: `Booking with ID ${id} cannot be updated` },
                HttpStatus.BAD_REQUEST,
            );
        }

        if (updateBookingDto.bookingDate) {
            // Convert string dates to Date objects for comparison
            const updatedDate = new Date(updateBookingDto.bookingDate);
            const currentBookingDate = new Date(booking.bookingDate);

            // Check if booking date is being updated
            if (currentBookingDate.getTime() !== updatedDate.getTime()) {
                await this.handleDateChange(booking, updateBookingDto, user);
            }
        }

        Object.assign(booking, updateBookingDto);

        const updatedBooking = await this.bookingRepository.save(booking);

        updatedBooking.status = translateBookingStatus(
            BookingStatus.PENDING,
            lang,
        );
        return updatedBooking;
    }

    private async handleDateChange(
        booking: Booking,
        updateBookingDto: UpdateBookingDto,
        user: Express.User,
    ): Promise<void> {
        const isDoctor = user.role === RoleType.DOCTOR;

        // Update confirmation status based on who made the change
        booking.patientDateConfirmed = !isDoctor;
        booking.doctorDateConfirmed = isDoctor;

        // Determine notification recipient
        const recipientId = isDoctor
            ? booking.user.id
            : (
                  booking.package.doctor?.user ||
                  booking.package.serviceProvider.user
              ).id;

        const recipientLanguage = isDoctor
            ? booking.user.appLanguage
            : (
                  booking.package.doctor?.user ||
                  booking.package.serviceProvider?.user
              ).appLanguage;

        // Create notification
        const notificationDto = this.createDateChangeNotification(
            recipientId,
            booking.id,
            updateBookingDto.bookingDate,
            isDoctor,
        );

        try {
            const createdNotification = await this.notificationsService.createNotification(
                notificationDto,
            );
            await this.notificationsService.sendNotification(
                createdNotification.createdId,
                recipientId,
                recipientLanguage,
            );
        } catch (error) {
            this.logger.error(
                `Failed to send notification: ${JSON.stringify(error)}`,
            );
        }
    }

    async updateBookingStatusToPaid(id: number): Promise<void> {
        const booking = await this.bookingRepository.getBookingById(id);

        await this.bookingRepository.update(
            { id },
            {
                status: BookingStatus.PAID,
                paymentStatus: PaymentStatus.PAID,
            },
        );

        await this.notifyDoctorWhenPatientPaid(booking);
    }

    private async notifyDoctorWhenPatientPaid(booking: Booking) {
        const patientName = booking.user.name;
        const userId =
            booking.package?.doctor?.user?.id ??
            booking.package?.serviceProvider?.user?.id;

        const appLang =
            booking?.package?.doctor?.user?.appLanguage ??
            booking?.package?.serviceProvider?.user?.appLanguage;

        const notification: CreateNotificationDto = {
            userId,
            data: {
                requestId: booking.id.toString(),
                type: NotificationTyps.NOTIFIY_DOCTOR_AFTER_PAID,
            },
            notificationTranslations: [
                {
                    title: ` ${patientName} تمت عمليه الدفع لحجز جديد من قبل `,
                    body: `${patientName} تمت عمليه الدفع لحجز جديد من قبل`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: `New booking is paid by ${patientName}.`,
                    body: `New booking is paid by ${patientName}.`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(createdNotification.createdId, userId, appLang)
            .catch((error) => this.logger.error(JSON.stringify(error)));
    }

    private createDateChangeNotification(
        userId: number,
        bookingId: number,
        newDate: string,
        isFromDoctor: boolean,
    ): CreateNotificationDto {
        const notificationDto = new CreateNotificationDto();
        notificationDto.userId = userId;

        const actor = isFromDoctor ? RoleType.DOCTOR : RoleType.PATIENT;

        notificationDto.data = {
            type: NotificationTyps.BOOKING_DATE_CHANGE,
            bookingId: bookingId.toString(),
            // eslint-disable-next-line @typescript-eslint/tslint/config
            newDate,
        };

        notificationDto.notificationTranslations = [
            {
                title: 'تحديث تاريخ الحجز',
                body: `تم تحديث تاريخ الحجز الخاص بك من قبل ال${
                    isFromDoctor ? 'طبيب' : 'مريض'
                }. يرجى تأكيد التاريخ الجديد.`,
                languageCode: AvailableLanguageCodes.ar,
            },
            {
                title: 'Booking Date Update',
                body: `Your booking date has been updated by the ${actor}. Please confirm the new date.`,
                languageCode: AvailableLanguageCodes.en,
            },
        ];

        return notificationDto;
    }

    async sendNewBookingNotification(booking: Booking): Promise<void> {
        try {
            // Determine the recipient (doctor or service provider)
            const recipientUser =
                booking.package.doctor?.user ||
                booking.package.serviceProvider?.user;

            if (!recipientUser) {
                this.logger.warn(
                    `No recipient found for booking ${booking.id}`,
                );
                return;
            }

            const notificationDto = new CreateNotificationDto();
            notificationDto.userId = recipientUser.id;
            notificationDto.data = {
                type: NotificationTyps.PATIENT_ADD_NEW_BOOKING,
                bookingId: booking.id.toString(),
                patientName: booking.user.name,
                bookingDate: booking.bookingDate.toString(),
                packageName: booking.package.name,
            };

            // Create translations for the notification
            notificationDto.notificationTranslations = [
                {
                    title: 'حجز جديد',
                    body: `قام ${
                        booking.user.name || 'مريض'
                    } بإنشاء حجز جديد في ${new Date(
                        booking.bookingDate,
                    ).toLocaleDateString('ar')}`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'New Booking',
                    body: `${
                        booking.user.name || 'A patient'
                    } has created a new booking for ${new Date(
                        booking.bookingDate,
                    ).toLocaleDateString('en-US')}`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ];

            // Create and send the notification
            const createdNotification = await this.notificationsService.createNotification(
                notificationDto,
            );

            this.logger.log(
                ` booking notification created but not sent yet to doctor/provider ID: ${recipientUser.id}`,
            );

            await this.notificationsService.sendNotification(
                createdNotification.createdId,
                recipientUser.id,
                recipientUser.appLanguage,
            );

            this.logger.log(
                `New booking notification sent to doctor/provider ID: ${recipientUser.id}`,
            );
        } catch (error) {
            this.logger.error(
                `Failed to send new booking notification: ${JSON.stringify(
                    error.message + ' ' + error.stack,
                )}`,
            );
        }
    }

    async sendBookingConfirmationNotification(
        booking: Booking,
        confirmedBy: Express.User,
    ): Promise<void> {
        try {
            const isDoctor = confirmedBy.role === RoleType.DOCTOR;

            // Determine recipient (if doctor confirmed, notify patient and vice versa)
            const recipientId = isDoctor
                ? booking.user.id
                : (
                      booking.package.doctor?.user ||
                      booking.package.serviceProvider.user
                  ).id;

            const recipientLanguage = isDoctor
                ? booking.user.appLanguage
                : (
                      booking.package.doctor?.user ||
                      booking.package.serviceProvider.user
                  ).appLanguage;

            // Format date according to locale
            const formattedDate = (locale: string) =>
                new Date(booking.bookingDate).toLocaleDateString(
                    locale === AvailableLanguageCodes.ar ? 'ar' : 'en-US',
                    {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                    },
                );

            const notificationDto = new CreateNotificationDto();
            notificationDto.userId = recipientId;
            notificationDto.data = {
                type: NotificationTyps.BOOKING_CONFIRMED,
                bookingId: booking.id.toString(),
                bookingDate: booking.bookingDate.toString(),
                packageName: booking.package.name,
            };

            // Create translations
            notificationDto.notificationTranslations = [
                {
                    title: 'تأكيد الحجز',
                    body: isDoctor
                        ? `قام الطبيب بتأكيد حجزك في ${formattedDate(
                              AvailableLanguageCodes.ar,
                          )}`
                        : `قام المريض ${
                              booking.user.name || ''
                          } بتأكيد الحجز في ${formattedDate(
                              AvailableLanguageCodes.ar,
                          )}`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Booking Confirmed',
                    body: isDoctor
                        ? `Your booking has been confirmed by the doctor for ${formattedDate(
                              AvailableLanguageCodes.en,
                          )}`
                        : `Patient ${
                              booking.user.name || ''
                          } has confirmed the booking for ${formattedDate(
                              AvailableLanguageCodes.en,
                          )}`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ];

            // Send notification
            const createdNotification = await this.notificationsService.createNotification(
                notificationDto,
            );
            await this.notificationsService.sendNotification(
                createdNotification.createdId,
                recipientId,
                recipientLanguage,
            );

            this.logger.log(
                `Booking confirmation notification sent for booking ID: ${booking.id}`,
            );
        } catch (error) {
            this.logger.error(
                `Failed to send booking confirmation notification: ${JSON.stringify(
                    error,
                )}`,
            );
        }
    }

    async updateStatusToPendingPayment(
        id: number,
        transactionId: number,
    ): Promise<void> {
        await this.bookingRepository.update(
            { id },
            {
                transaction: {
                    id: transactionId,
                },
                status: ConsultationRequestStatus.PENDINGPAYMENT,
            },
        );
    }

    async sendBookingCancellationNotification(
        booking: Booking,
        canceledBy: Express.User,
        reason?: string,
    ): Promise<void> {
        try {
            const isDoctor = canceledBy.role === RoleType.DOCTOR;
            const isAdmin = canceledBy.role === RoleType.ADMIN;

            // If admin cancels, notify both doctor and patient
            if (isAdmin) {
                await this.sendAdminCancellationNotifications(booking, reason);
                return;
            }

            // Determine recipient (if doctor canceled, notify patient and vice versa)
            const recipientId = isDoctor
                ? booking.user.id
                : (
                      booking.package.doctor?.user ||
                      booking.package.serviceProvider.user
                  ).id;

            const recipientLanguage = isDoctor
                ? booking.user.appLanguage
                : (
                      booking.package.doctor?.user ||
                      booking.package.serviceProvider.user
                  ).appLanguage;

            const notificationDto = new CreateNotificationDto();
            notificationDto.userId = recipientId;
            notificationDto.data = {
                type: NotificationTyps.BOOKING_CANCELED,
                bookingId: booking.id.toString(),
                packageName: booking.package.name,
                reason: reason || '',
            };

            const cancelerName = isDoctor
                ? 'The doctor'
                : `Patient ${booking.user.name || ''}`;

            const cancelerNameAr = isDoctor
                ? 'الطبيب'
                : `المريض ${booking.user.name || ''}`;

            // Create translations
            notificationDto.notificationTranslations = [
                {
                    title: 'إلغاء الحجز',
                    body: `قام ${cancelerNameAr} بإلغاء الحجز${
                        reason ? ` للسبب التالي: ${reason}` : ''
                    }`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Booking Canceled',
                    body: `${cancelerName} has canceled the booking${
                        reason ? ` for the following reason: ${reason}` : ''
                    }`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ];

            // Send notification
            const createdNotification = await this.notificationsService.createNotification(
                notificationDto,
            );
            await this.notificationsService.sendNotification(
                createdNotification.createdId,
                recipientId,
                recipientLanguage,
            );

            this.logger.log(
                `Booking cancellation notification sent for booking ID: ${booking.id}`,
            );
        } catch (error) {
            this.logger.error(
                `Failed to send booking cancellation notification: ${JSON.stringify(
                    error,
                )}`,
            );
        }
    }

    private async sendAdminCancellationNotifications(
        booking: Booking,
        reason?: string,
    ): Promise<void> {
        const doctorUser =
            booking.package.doctor?.user ||
            booking.package.serviceProvider.user;

        // Notify patient
        const patientNotificationDto = new CreateNotificationDto();
        patientNotificationDto.userId = booking.user.id;
        patientNotificationDto.data = {
            type: NotificationTyps.BOOKING_CANCELED,
            bookingId: booking.id.toString(),
            reason: reason || '',
        };

        patientNotificationDto.notificationTranslations = [
            {
                title: 'إلغاء الحجز',
                body: `تم إلغاء حجزك من قبل الإدارة${
                    reason ? ` للسبب التالي: ${reason}` : ''
                }`,
                languageCode: AvailableLanguageCodes.ar,
            },
            {
                title: 'Booking Canceled',
                body: `Your booking has been canceled by administration${
                    reason ? ` for the following reason: ${reason}` : ''
                }`,
                languageCode: AvailableLanguageCodes.en,
            },
        ];

        // Notify doctor
        const doctorNotificationDto = new CreateNotificationDto();
        doctorNotificationDto.userId = doctorUser.id;
        doctorNotificationDto.data = {
            type: NotificationTyps.BOOKING_CANCELED,
            bookingId: booking.id.toString(),
            patientName: booking.user.name || '',
            reason: reason || '',
        };

        doctorNotificationDto.notificationTranslations = [
            {
                title: 'إلغاء الحجز',
                body: `تم إلغاء حجز المريض ${
                    booking.user.name || ''
                } من قبل الإدارة${reason ? ` للسبب التالي: ${reason}` : ''}`,
                languageCode: AvailableLanguageCodes.ar,
            },
            {
                title: 'Booking Canceled',
                body: `Booking for patient ${
                    booking.user.name || ''
                } has been canceled by administration${
                    reason ? ` for the following reason: ${reason}` : ''
                }`,
                languageCode: AvailableLanguageCodes.en,
            },
        ];

        try {
            // Send to patient
            const patientNotification = await this.notificationsService.createNotification(
                patientNotificationDto,
            );
            await this.notificationsService.sendNotification(
                patientNotification.createdId,
                booking.user.id,
                booking.user.appLanguage,
            );

            // Send to doctor
            const doctorNotification = await this.notificationsService.createNotification(
                doctorNotificationDto,
            );
            await this.notificationsService.sendNotification(
                doctorNotification.createdId,
                doctorUser.id,
                doctorUser.appLanguage,
            );

            this.logger.log(
                `Admin cancellation notifications sent for booking ID: ${booking.id}`,
            );
        } catch (error) {
            this.logger.error(
                `Failed to send admin cancellation notifications: ${JSON.stringify(
                    error,
                )}`,
            );
        }
    }

    async sendBookingCompletedNotification(booking: Booking): Promise<void> {
        try {
            // Always notify the patient when booking is completed
            const notificationDto = new CreateNotificationDto();
            notificationDto.userId = booking.user.id;
            notificationDto.data = {
                type: NotificationTyps.BOOKING_COMPLETED,
                bookingId: booking.id.toString(),
                packageName: booking.package.name,
            };

            // Create translations
            notificationDto.notificationTranslations = [
                {
                    title: 'اكتمال الحجز',
                    body: `تم اكتمال حجزك مع ${
                        booking.package.doctor?.user.name ||
                        booking.package.serviceProvider?.user.name ||
                        'مقدم الخدمة'
                    }. نتمنى لك الشفاء العاجل!`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Booking Completed',
                    body: `Your booking with ${
                        booking.package.doctor?.user.name ||
                        booking.package.serviceProvider?.user.name ||
                        'service provider'
                    } has been completed. We wish you a speedy recovery!`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ];

            // Send notification
            const createdNotification = await this.notificationsService.createNotification(
                notificationDto,
            );
            await this.notificationsService.sendNotification(
                createdNotification.createdId,
                booking.user.id,
                booking.user.appLanguage,
            );

            this.logger.log(
                `Booking completion notification sent for booking ID: ${booking.id}`,
            );
        } catch (error) {
            this.logger.error(
                `Failed to send booking completion notification: ${JSON.stringify(
                    error,
                )}`,
            );
        }
    }

    async sendBookingRemovedNotification(
        booking: Booking,
        reason?: string,
    ): Promise<void> {
        try {
            // Notify both doctor and patient
            const doctorUser =
                booking.package.doctor?.user ||
                booking.package.serviceProvider.user;

            // Prepare notifications
            const patientNotificationDto = new CreateNotificationDto();
            patientNotificationDto.userId = booking.user.id;
            patientNotificationDto.data = {
                type: NotificationTyps.BOOKING_REMOVED,
                packageName: booking.package.name,
                reason: reason || '',
            };

            patientNotificationDto.notificationTranslations = [
                {
                    title: 'إزالة الحجز',
                    body: `تم إزالة حجزك من النظام${
                        reason ? ` للسبب التالي: ${reason}` : ''
                    }`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Booking Removed',
                    body: `Your booking has been removed from the system${
                        reason ? ` for the following reason: ${reason}` : ''
                    }`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ];

            const doctorNotificationDto = new CreateNotificationDto();
            doctorNotificationDto.userId = doctorUser.id;
            doctorNotificationDto.data = {
                type: NotificationTyps.BOOKING_REMOVED,
                patientName: booking.user.name || '',
                reason: reason || '',
            };

            doctorNotificationDto.notificationTranslations = [
                {
                    title: 'إزالة الحجز',
                    body: `تم إزالة حجز المريض ${
                        booking.user.name || ''
                    } من النظام${reason ? ` للسبب التالي: ${reason}` : ''}`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Booking Removed',
                    body: `Booking for patient ${
                        booking.user.name || ''
                    } has been removed from the system${
                        reason ? ` for the following reason: ${reason}` : ''
                    }`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ];

            // Send to patient
            const patientNotification = await this.notificationsService.createNotification(
                patientNotificationDto,
            );
            await this.notificationsService.sendNotification(
                patientNotification.createdId,
                booking.user.id,
                booking.user.appLanguage,
            );

            // Send to doctor
            const doctorNotification = await this.notificationsService.createNotification(
                doctorNotificationDto,
            );
            await this.notificationsService.sendNotification(
                doctorNotification.createdId,
                doctorUser.id,
                doctorUser.appLanguage,
            );

            this.logger.log(
                `Booking removal notifications sent for booking ID: ${booking.id}`,
            );
        } catch (error) {
            this.logger.error(
                `Failed to send booking removal notifications: ${JSON.stringify(
                    error,
                )}`,
            );
        }
    }

    async updateStatusToRefunded(id: number): Promise<void> {
        await this.bookingRepository.update(
            { id },
            {
                paymentStatus: PaymentStatus.REFUNDED,
            },
        );
    }

    async findByIds(ids: number[]): Promise<Booking[]> {
        return this.bookingRepository.findByIds(ids);
    }
}
