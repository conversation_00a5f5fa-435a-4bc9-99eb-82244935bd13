import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>A<PERSON>, ApiHeader, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { BookingsService } from './booking.service';
import { CreateBookingDto } from './dto/create-booking.dto';
import { RateBookingDto } from './dto/rate-booking.dto';
import { UpdateBookingDto } from './dto/update-booking.dto';

@Controller('bookings')
@ApiTags('bookings')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class BookingsController {
    constructor(private readonly bookingsService: BookingsService) {}

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT)
    async createBooking(
        @Body() createBookingDto: CreateBookingDto,
        @Req() req: Request,
    ) {
        return this.bookingsService.createBooking(
            req.user.id,
            createBookingDto,
        );
    }

    @Post('ratings')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT)
    async createRating(
        @Body() createRatingDto: RateBookingDto,
        @Req() req: Request,
    ) {
        return this.bookingsService.leaveRateForBooking(
            req.user.id,
            createRatingDto,
        );
    }

    @Get()
    @UseGuards(AuthGuard, RolesGuard)
    findAll(@Req() req: Request, @I18nLang() lang: string) {
        switch (req.user.role) {
            case RoleType.DOCTOR:
                return this.bookingsService.getDoctorBookings(
                    req.user.id,
                    lang,
                );
            case RoleType.PATIENT:
                return this.bookingsService.getPatientBookings(
                    req.user.id,
                    lang,
                );
            case RoleType.SERVICE_PROVIDER:
                return this.bookingsService.getServiceProvidersBookings(
                    req.user.id,
                    lang,
                );
            case RoleType.ADMIN:
            case RoleType.SUPER_ADMIN:
                return this.bookingsService.findAll(lang);
        }
    }

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    findOne(@Param('id') id: string, @I18nLang() lang: string) {
        return this.bookingsService.findOne(+id, lang);
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    update(
        @Param('id') id: string,
        @Body() updateBookingDto: UpdateBookingDto,
        @Req() req: Request,
        @I18nLang() lang: string,
    ) {
        return this.bookingsService.update(
            +id,
            updateBookingDto,
            req.user,
            lang,
        );
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT)
    remove(@Param('id') id: string) {
        return this.bookingsService.remove(+id);
    }

    @Post(':id/confirm')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.DOCTOR, RoleType.PATIENT)
    confirmBooking(@Param('id') id: string, @Req() req: Request) {
        return this.bookingsService.confirmBooking(+id, req.user);
    }

    @Post(':id/cancel')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.DOCTOR, RoleType.PATIENT)
    cancelBooking(@Param('id') id: string, @Req() req: Request) {
        return this.bookingsService.cancelBooking(+id, req.user);
    }

    @Post(':id/complete')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.DOCTOR)
    completeBooking(@Param('id') id: string) {
        return this.bookingsService.completeBooking(+id);
    }
}
