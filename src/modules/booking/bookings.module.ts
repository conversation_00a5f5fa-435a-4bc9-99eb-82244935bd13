import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { PackagesRepository } from '../packages/repositories/packages.repository';
import { BookingsService } from './booking.service';
import { BookingsController } from './bookings.controller';
import { BookingsRepository } from './repositories/booking.repository';
import { ReviewsRepository } from './repositories/reviews.repository';

@Module({
    imports: [SharedModule, NotificationsModule],
    controllers: [BookingsController],
    providers: [
        BookingsService,
        BookingsRepository,
        ReviewsRepository,
        PackagesRepository,
    ],
    exports: [BookingsService],
})
export class BookingsModule {}
