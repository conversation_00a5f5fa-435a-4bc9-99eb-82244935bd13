import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateBookingDto {
    @IsNumber()
    @IsNotEmpty()
    packageId: number;

    @IsString()
    @ApiProperty()
    @Expose()
    @IsNotEmpty()
    bookingDate: string;

    @IsString()
    @ApiProperty()
    @Expose()
    @IsOptional()
    scheduledDate?: string;

    @IsOptional()
    notes?: string;
}
