import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { BookingStatus } from '../../../common/constants/types';
import { Package } from '../../packages/entities/package.entity';
import { UserEntity as User } from '../../users/entities/user.entity';
import { PaymentStatus } from '../../../common/constants/status';
import { Expose } from 'class-transformer';
import { Transaction } from '../../payments/entities/transaction.entity';

@Entity('bookings')
export class Booking extends AbstractEntity {
    @ManyToOne(() => User, (user) => user.bookings, { onDelete: 'CASCADE' })
    user: User;

    @ManyToOne(() => Package, (pkg) => pkg.bookings, { onDelete: 'CASCADE' })
    package: Package;

    @OneToOne((_type) => Transaction, (transaction) => transaction.request, {
        onDelete: 'SET NULL',
    })
    @JoinColumn()
    @Expose()
    transaction: Transaction;

    @Column({
        type: 'enum',
        enum: BookingStatus,
        default: BookingStatus.PENDING,
    })
    status: string;

    @Column({
        type: 'enum',
        enum: PaymentStatus,
        default: PaymentStatus.PENDING,
    })
    @Expose()
    paymentStatus: PaymentStatus;

    @Column({ name: 'booking_date', type: 'timestamp without time zone' })
    bookingDate: Date;

    @Column({
        name: 'scheduled_date',
        type: 'timestamp without time zone',
        nullable: true,
    })
    scheduledDate: Date;

    @Column({ type: 'text', nullable: true })
    notes: string;

    @Column({ name: 'patient_date_confirmed', type: 'bool', default: false })
    patientDateConfirmed: boolean;

    @Column({ name: 'doctor_date_confirmed', type: 'bool', default: false })
    doctorDateConfirmed: boolean;
}
