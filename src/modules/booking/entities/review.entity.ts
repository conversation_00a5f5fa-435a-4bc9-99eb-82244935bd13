import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Package } from '../../packages/entities/package.entity';
import { UserEntity as User } from '../../users/entities/user.entity';

@Entity('reviews')
export class Review extends AbstractEntity {
    @ManyToOne(() => User, (user) => user.reviews, { onDelete: 'CASCADE' })
    user: User;

    @ManyToOne(() => Package, (pkg) => pkg.reviews, { onDelete: 'CASCADE' })
    package: Package;

    @Column({ type: 'int' })
    rating: number;

    @Column({ type: 'text', nullable: true })
    comment: string;
}
