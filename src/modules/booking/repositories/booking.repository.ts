import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { Booking } from '../entities/booking.entity';

@Injectable()
export class BookingsRepository extends Repository<Booking> {
    constructor(dataSource: DataSource) {
        super(Booking, dataSource.createEntityManager());
    }
    async getBookingById(id: number) {
        return this.createQueryBuilder('booking')
            .leftJoinAndSelect('booking.user', 'user')
            .leftJoinAndSelect('booking.package', 'package')
            .leftJoinAndSelect('package.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'spUser')
            .leftJoinAndSelect('package.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'dUser')
            .select([
                'booking.id',
                'booking.doctorDateConfirmed',
                'booking.patientDateConfirmed',
                'booking.bookingDate',
                'booking.scheduledDate',
                'booking.notes',
                'booking.status',
                'user.id',
                'user.name',
                'user.email',
                'user.phone',
                'user.appLanguage',
                'package.id',
                'package.name',
                'package.price',
                'serviceProvider.id',
                'spUser.id',
                'spUser.name',
                'spUser.appLanguage',
                'doctor.id',
                'dUser.id',
                'dUser.name',
                'dUser.appLanguage',
            ])
            .where({ id })
            .getOne();
    }
}
