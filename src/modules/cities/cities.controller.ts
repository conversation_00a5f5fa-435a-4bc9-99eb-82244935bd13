'use strict';

import {
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    Query,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CitiesService } from './cities.service';
import { CityDto } from './dto/city.dto';

@Controller('cities')
@ApiTags('cities')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(HeaderInterceptor)
@ApiBearerAuth()
export class CitiesController {
    constructor(private citiesService: CitiesService) {}

    @Get('')
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'list cities',
        type: [CityDto],
    })
    listCities(
        @Query('governorateId') governorateId: number,
        @I18nLang() lang: string,
    ): Promise<CityDto[]> {
        return this.citiesService.listCities(lang, governorateId);
    }
}
