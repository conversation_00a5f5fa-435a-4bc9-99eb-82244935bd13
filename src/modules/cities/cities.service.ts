import { Injectable } from '@nestjs/common';

import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { CityDto } from './dto/city.dto';
import { CitiesRepository } from './repositories/cities.repository';

@Injectable()
export class CitiesService {
    constructor(public readonly citiesRepository: CitiesRepository) {}

    async listCities(lang: string, governorateId: number): Promise<CityDto[]> {
        const query = this.citiesRepository
            .createQueryBuilder('city')
            .leftJoinAndSelect('city.governorate', 'governorate')
            .select(['city.id', 'city.name', 'city.nameEn', 'governorate.id']);
        if (governorateId) {
            query.where('governorate.id = :governorateId', {
                governorateId,
            });
        }
        let cities: any = await query.getMany();
        cities = cities.map((city) => {
            if (lang === AvailableLanguageCodes.en) {
                city.name = city.nameEn;
            }
            delete city.nameEn;
            return city;
        });
        return cities;
    }
}
