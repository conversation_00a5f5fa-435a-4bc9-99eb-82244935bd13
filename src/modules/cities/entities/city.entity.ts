import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Clinic } from '../../clinics/entities/clinic.entity';
import { Governorate } from '../../governorates/entities/governorate.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';

@Entity('cities')
export class City extends AbstractEntity {
    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    nameEn?: string;

    @ManyToOne((_type) => Governorate, (governorate) => governorate.cities, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    @Expose()
    @IsOptional()
    governorate?: Governorate;

    @OneToMany((_type) => Clinic, (clinic) => clinic.city, {
        cascade: true,
    })
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => Clinic)
    clinics?: Clinic[];

    @OneToMany(
        (_type) => ServiceProvider,
        (serviceProvider) => serviceProvider.city,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => ServiceProvider)
    serviceProviders?: ServiceProvider[];
}
