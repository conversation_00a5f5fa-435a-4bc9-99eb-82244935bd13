'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Put,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { ClinicsService } from './clinics.service';
import { ClinicDto } from './dto/clinic.dto';
import { CreateClinicDto } from './dto/create-clinic.dto';
import { UpdateClinicDto } from './dto/update-clinic.dto';

@Controller('clinics')
@ApiTags('clinics')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseGuards(AuthGuard, RolesGuard)
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class ClinicsController {
    constructor(private clinicsService: ClinicsService) {}

    @Get()
    @HttpCode(HttpStatus.OK)
    @Roles(RoleType.ADMIN)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Get All Clinics',
        type: [ClinicDto],
    })
    getClinics(@I18nLang() lang: string): Promise<ClinicDto[]> {
        return this.clinicsService.getAllClinics(lang);
    }

    @Get('/doctor/:doctorId')
    @HttpCode(HttpStatus.OK)
    @Roles(RoleType.DOCTOR, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        status: HttpStatus.OK,
        description: "Get Doctor's Clinics",
        type: [ClinicDto],
    })
    getDoctorClinics(
        @Param('doctorId') doctorId: number,
        @I18nLang() lang: string,
    ): Promise<ClinicDto[]> {
        return this.clinicsService.getDoctorClinics(doctorId, lang);
    }

    @Post()
    @Roles(RoleType.DOCTOR, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Add Clinic',
        type: CreateOperationsResponse,
    })
    create(
        @Req() req: Request,
        @Body() createClinicDto: CreateClinicDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        if (req.user.role === RoleType.DOCTOR) {
            createClinicDto.doctorId = req.user.doctor.id;
        }

        return this.clinicsService.createClinic(createClinicDto, lang);
    }

    @Put(':id')
    @Roles(RoleType.DOCTOR, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update Clinic',
        type: BasicOperationsResponse,
    })
    update(
        @Req() req: Request,
        @Param('id') id: number,
        @Body() clinicDto: UpdateClinicDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.clinicsService.updateClinic(id, clinicDto, req.user, lang);
    }

    @Roles(RoleType.DOCTOR, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete Clinic',
        type: BasicOperationsResponse,
    })
    @Delete(':id')
    delete(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.clinicsService.deleteClinic(id, lang);
    }
}
