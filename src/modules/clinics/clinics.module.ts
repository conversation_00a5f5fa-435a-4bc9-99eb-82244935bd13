import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { SchedulesModule } from '../schedules/schedules.module';
import { ClinicsController } from './clinics.controller';
import { ClinicsService } from './clinics.service';
import { ClinicsTranslationsMapper } from './mappers/clinics-translations.mapper';
import { ClinicsMapper } from './mappers/clinics.mapper';
import { ClinicsTranslationsRepository } from './repositories/clinics-translations.repository';
import { ClinicsRepository } from './repositories/clinics.repository';

@Module({
    imports: [SharedModule, SchedulesModule],
    controllers: [ClinicsController],
    exports: [ClinicsService],
    providers: [
        ClinicsService,
        ClinicsRepository,
        ClinicsTranslationsRepository,
        ClinicsMapper,
        ClinicsTranslationsMapper,
    ],
})
export class ClinicsModule {}
