import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { I18nService } from 'nestjs-i18n';

import { ConsultationType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { SchedulesService } from '../schedules/schedules.service';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { ClinicDto } from './dto/clinic.dto';
import { CreateClinicDto } from './dto/create-clinic.dto';
import { UpdateClinicDto } from './dto/update-clinic.dto';
import { ClinicTranslation } from './entities/clinic-translation.entity';
import { Clinic } from './entities/clinic.entity';
import { ClinicsTranslationsMapper } from './mappers/clinics-translations.mapper';
import { ClinicsMapper } from './mappers/clinics.mapper';
import { ClinicsTranslationsRepository } from './repositories/clinics-translations.repository';
import { ClinicsRepository } from './repositories/clinics.repository';
import { ClinicMessagesKeys } from './translate.enum';

@Injectable()
export class ClinicsService {
    constructor(
        public readonly clinicsRepository: ClinicsRepository,
        public readonly clinicsTranslationsRepository: ClinicsTranslationsRepository,
        public readonly clinicsMapper: ClinicsMapper,
        public readonly schedulesService: SchedulesService,
        public readonly clinicsTranslationMapper: ClinicsTranslationsMapper,
        private readonly i18n: I18nService,
        public readonly helperService: HelperService,
    ) { }

    async getAllClinics(lang: string): Promise<ClinicDto[]> {
        const clinics = await this.clinicsRepository
            .createQueryBuilder('clinics')
            .leftJoinAndSelect(
                'clinics.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('clinics.governorate', 'governorate')
            .leftJoinAndSelect('clinics.city', 'city')
            .orderBy('clinics.id', 'DESC')
            .getMany();
        if (!clinics.length) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ClinicMessagesKeys.CLINICS_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.clinicsMapper.fromEntitiesToDTOs(clinics, lang);
    }

    async getDoctorClinics(
        doctorId: number,
        lang: string,
    ): Promise<ClinicDto[]> {
        const clinics = await this.clinicsRepository
            .createQueryBuilder('clinics')
            .leftJoinAndSelect('clinics.doctor', 'doctor')
            .leftJoinAndSelect(
                'clinics.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('clinics.governorate', 'governorate')
            .leftJoinAndSelect('clinics.city', 'city')
            .where('doctor.id = :id', { id: doctorId })
            .orderBy('clinics.id', 'DESC')
            .getMany();

        if (!clinics) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ClinicMessagesKeys.CLINICS_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.clinicsMapper.fromEntitiesToDTOs(clinics, lang);
    }

    async createClinic(
        createClinicDto: CreateClinicDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const clinicEntity = this.clinicsMapper.fromDTOToEntity(
            Clinic,
            createClinicDto,
            lang,
            true,
        );
        const clinic = this.clinicsRepository.create(clinicEntity);
        const createdClinic = await this.clinicsRepository.save(clinic);

        const createdSchedule = await this.schedulesService.createSchedule(
            {
                doctorId: clinicEntity.doctor.id,
                clinicId: createdClinic.id,
                fees: createClinicDto.fees,
                type: ConsultationType.CLINIC,
            },
            lang,
        );
        await this.clinicsRepository.update(
            { id: createdClinic.id },
            { schedule: { id: createdSchedule.createdId } },
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ClinicMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
            createdId: createdClinic.id,
        };
    }

    async updateClinic(
        id: number,
        updateClinicDto: UpdateClinicDto,
        user: UserDetailsDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const clinicEntity = await this.clinicsRepository.findOne({
            where: { id },
            relations: ['translations', 'schedule', 'doctor', 'doctor.user'],
        });

        if (!clinicEntity) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ClinicMessagesKeys.CLINIC_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        const newClinicEntity = this.clinicsMapper.fromDTOToEntity(
            Clinic,
            plainToClass(ClinicDto, updateClinicDto),
            lang,
        );

        delete newClinicEntity.translations;

        this.helperService.removeEmptyKeys(newClinicEntity);
        this.helperService.removeEmptyKeys(updateClinicDto);

        await Promise.all([
            this.clinicsRepository.update(clinicEntity.id, newClinicEntity),
            this.createOrUpdateClinicTranslation(
                clinicEntity,
                updateClinicDto,
                lang,
            ),
            newClinicEntity.fees && clinicEntity.fees !== newClinicEntity.fees
                ? this.schedulesService.updateSchedule(
                    clinicEntity.schedule.id,
                    clinicEntity.doctor.id,
                    user,
                    { fees: newClinicEntity.fees },
                    lang,
                )
                : null,
        ]);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ClinicMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async createOrUpdateClinicTranslation(
        clinicEntity: Clinic,
        updateClinicDto: UpdateClinicDto,
        lang: string,
    ): Promise<void> {
        const existingTranslation = clinicEntity.translations.filter(
            (translation) => translation.languageCode === lang,
        );
        const clinicTranslation = this.clinicsTranslationMapper.fromDTOToEntity(
            ClinicTranslation,
            updateClinicDto,
            lang,
        );

        clinicTranslation.clinic = clinicEntity;

        if (clinicTranslation) {
            if (existingTranslation.length <= 0) {
                const createdClinicTranslation = this.clinicsTranslationsRepository.create(
                    clinicTranslation,
                );
                await this.clinicsTranslationsRepository.save(
                    createdClinicTranslation,
                );
            } else {
                await this.clinicsTranslationsRepository.update(
                    existingTranslation[0].id,
                    clinicTranslation,
                );
            }
        }
    }

    async deleteClinic(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const clinic = await this.clinicsRepository.findOne({
            where: { id },
        });

        if (!clinic) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ClinicMessagesKeys.CLINIC_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        await this.clinicsRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ClinicMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async findClinic(id: number): Promise<Clinic> {
        return this.clinicsRepository.findOne({ where: { id } });
    }
}
