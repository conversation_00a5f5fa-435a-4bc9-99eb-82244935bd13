'use strict';

import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ClinicDto } from '../dto/clinic.dto';

export class ClinicTranslationDto extends AbstractDto {
    @IsString()
    @Expose()
    @IsOptional()
    name?: string;

    @IsString()
    @Expose()
    @IsOptional()
    address?: string;

    @IsString()
    @Expose()
    @IsOptional()
    area?: string;

    @IsString()
    @Expose()
    @IsOptional()
    languageCode?: string;

    @Expose()
    @IsOptional()
    @Type(() => ClinicDto)
    clinic?: ClinicDto;
}
