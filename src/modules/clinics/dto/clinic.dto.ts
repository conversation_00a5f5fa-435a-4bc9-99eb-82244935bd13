'use strict';

import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ILocation } from '../../../interfaces/ILocation';
import { City } from '../../users/entities/city.entity';
import { Governorate } from '../../users/entities/governorate.entity';
import { ClinicTranslationDto } from './clinic-translation.dto';

export class ClinicDto extends AbstractDto {
    @IsString()
    @Expose()
    @IsOptional()
    name?: string;

    @IsNumber()
    @Expose()
    @IsOptional()
    fees?: number;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isFreeClinic?: boolean;

    @IsString()
    @Expose()
    @IsOptional()
    mobileNumber?: string;

    @IsString()
    @Expose()
    @IsOptional()
    email?: string;

    @IsString()
    @IsOptional()
    @Expose()
    landlineNumber?: string;

    @IsString()
    @Expose()
    @IsOptional()
    clinicLocation?: string;

    @Expose()
    @IsOptional()
    location?: ILocation;

    @IsString()
    @Expose()
    @IsOptional()
    address?: string;

    @Expose()
    @IsOptional()
    @Type(() => City)
    city?: City;

    @Expose()
    @IsOptional()
    @Type(() => Governorate)
    governorate?: Governorate;

    @IsString()
    @Expose()
    @IsOptional()
    area?: string;

    @IsNumber()
    @Expose()
    @IsOptional()
    doctorId?: number;

    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => ClinicTranslationDto)
    translations?: ClinicTranslationDto[];
}
