'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsEmail,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ILocation } from '../../../interfaces/ILocation';

export class CreateClinicDto extends AbstractDto {
    @IsString()
    @Expose()
    @ApiProperty()
    @IsNotEmpty()
    name: string;

    @IsNumber()
    @Expose()
    @ApiProperty()
    @IsNotEmpty()
    fees: number;

    @ApiPropertyOptional()
    @IsOptional()
    mobileNumber?: string;

    @IsOptional()
    @Expose()
    @ApiPropertyOptional()
    landlineNumber?: string;

    @Expose()
    @IsOptional()
    @IsEmail()
    @ApiPropertyOptional()
    email?: string;

    @Expose()
    @IsOptional()
    @ApiPropertyOptional()
    clinicLocation?: string;

    @Expose()
    @IsOptional()
    @ApiPropertyOptional()
    location?: ILocation;

    @IsOptional()
    @IsString()
    @Expose()
    @ApiPropertyOptional()
    address?: string;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    cityId?: number;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    governorateId?: number;

    @IsOptional()
    @IsString()
    @Expose()
    @ApiPropertyOptional()
    area?: string;

    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    doctorId?: number;
}
