'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsBoolean,
    IsEmail,
    IsNumber,
    IsOptional,
    IsPhoneNumber,
    IsString,
    Length,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ILocation } from '../../../interfaces/ILocation';

export class UpdateClinicDto extends AbstractDto {
    @IsString()
    @IsOptional()
    @Expose()
    @ApiPropertyOptional()
    @Length(1)
    name?: string;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    fees?: number;

    @IsOptional()
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @Expose()
    @ApiPropertyOptional()
    mobileNumber?: string;

    @Expose()
    @IsOptional()
    @IsEmail()
    @ApiPropertyOptional()
    email?: string;

    @IsOptional()
    @Expose()
    @ApiPropertyOptional()
    landlineNumber?: string;

    @Expose()
    @IsOptional()
    @ApiPropertyOptional()
    clinicLocation?: string;

    @Expose()
    @IsOptional()
    @ApiPropertyOptional()
    location?: ILocation;

    @IsOptional()
    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @Length(1)
    address?: string;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    cityId?: number;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    governorateId?: number;

    @IsOptional()
    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @Length(1)
    area?: string;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isFreeClinic?: boolean;

    doctorId?: number;
}
