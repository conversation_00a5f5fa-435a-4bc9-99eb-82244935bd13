import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Clinic } from './clinic.entity';

@Entity('clinics_translations')
export class ClinicTranslation extends AbstractEntity {
    @Column({ nullable: true })
    @Expose()
    address: string;

    @Column()
    languageCode: string;

    @Column({ nullable: true })
    @Expose()
    name: string;

    @Column({ nullable: true })
    @Expose()
    area: string;

    @ManyToOne((_type) => Clinic, (clinic) => clinic.translations, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    clinic?: Clinic;
}
