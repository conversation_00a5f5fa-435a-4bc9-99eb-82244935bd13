'use strict';

import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';
import {
    Column,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    OneToOne,
} from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { IGeometry } from '../../../interfaces/ILocation';
import { City } from '../../cities/entities/city.entity';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { Governorate } from '../../governorates/entities/governorate.entity';
import { ScheduleEntity } from '../../schedules/entities/schedule.entity';
import { SecretaryEntity } from '../../secretaries/entities/secretary.entity';
import { ClinicTranslation } from './clinic-translation.entity';

@Entity('clinics')
export class Clinic extends AbstractEntity {
    @Column({ nullable: true })
    @Expose()
    mobileNumber: string;

    @Column({ nullable: true })
    @Expose()
    email: string;

    @Column({ nullable: true })
    @Expose()
    landlineNumber: string;

    @Column({ nullable: true })
    @Expose()
    clinicLocation: string;

    @Column({
        name: 'location',
        type: 'geometry',
        nullable: true,
        spatialFeatureType: 'Point',
        srid: 4326,
    })
    @Expose()
    location: IGeometry;

    @Column({ default: 0 })
    @Expose()
    fees: number;

    @Column({ default: false })
    @Expose()
    isFreeClinic: boolean;

    @OneToOne((_type) => ScheduleEntity, (schedule) => schedule.clinic, {
        onDelete: 'CASCADE',
    })
    @JoinColumn()
    @Expose()
    schedule?: ScheduleEntity;

    @OneToMany(
        (_type) => ClinicTranslation,
        (clinicTranslation) => clinicTranslation.clinic,
        {
            cascade: true,
        },
    )
    translations: ClinicTranslation[];

    @ManyToOne((_type) => DoctorEntity, (doctor) => doctor.clinics, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    doctor: DoctorEntity;

    @OneToMany(
        (_type) => ConsultationRequestEntity,
        (consultationRequest) => consultationRequest.clinic,
        {
            onDelete: 'CASCADE',
        },
    )
    consultationRequests: ConsultationRequestEntity[];

    @ManyToOne((_type) => Governorate, (governorate) => governorate.clinics)
    @Expose()
    @IsOptional()
    governorate?: Governorate;

    @ManyToOne((_type) => City, (city) => city.clinics)
    @Expose()
    @IsOptional()
    city?: City;

    @OneToMany(() => SecretaryEntity, (secretary) => secretary.clinic)
    secretaries: SecretaryEntity[];
}
