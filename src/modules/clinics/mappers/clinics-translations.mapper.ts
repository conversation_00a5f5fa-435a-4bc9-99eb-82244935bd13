'use strict';

import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../../common/abstract.mapper';
import { HelperService } from '../../../shared/services/helper';
import { ClinicDto } from '../dto/clinic.dto';
import { UpdateClinicDto } from '../dto/update-clinic.dto';
import { ClinicTranslation } from '../entities/clinic-translation.entity';

@Injectable()
export class ClinicsTranslationsMapper extends AbstractMapper<
    UpdateClinicDto,
    ClinicTranslation
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<ClinicTranslation>,
        sourceObject: UpdateClinicDto,
        lang: string,
    ): ClinicTranslation {
        const clinicTranslation = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(clinicTranslation);

        clinicTranslation.languageCode = lang;
        return clinicTranslation;
    }

    fromEntityToDTO(
        destination: ClassType<ClinicDto>,
        sourceObject: ClinicTranslation,
    ): UpdateClinicDto {
        return super.fromEntityToDTO(destination, sourceObject);
    }
}
