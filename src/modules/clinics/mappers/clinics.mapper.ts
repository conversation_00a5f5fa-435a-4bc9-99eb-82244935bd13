/* eslint-disable complexity */
'use strict';

import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType, plainToClass } from 'class-transformer';

import { AbstractMapper } from '../../../common/abstract.mapper';
import { AvailableLanguageCodes } from '../../../i18n/languageCodes';
import { HelperService } from '../../../shared/services/helper';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { City } from '../../users/entities/city.entity';
import { Governorate } from '../../users/entities/governorate.entity';
import { ClinicDto } from '../dto/clinic.dto';
import { CreateClinicDto } from '../dto/create-clinic.dto';
import { UpdateClinicDto } from '../dto/update-clinic.dto';
import { ClinicTranslation } from '../entities/clinic-translation.entity';
import { Clinic } from '../entities/clinic.entity';

@Injectable()
export class ClinicsMapper extends AbstractMapper<ClinicDto, Clinic> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<Clinic>,
        sourceObject: CreateClinicDto | UpdateClinicDto,
        lang: string,
        create = false,
    ): Clinic {
        const clinicEntity = super.fromDTOToEntity(destination, sourceObject);

        const clinicTranslation = plainToClass(
            ClinicTranslation,
            sourceObject,
            {
                excludeExtraneousValues: true,
            },
        );

        if (sourceObject.location) {
            const { latitude, longitude } = sourceObject.location;

            clinicEntity.location = {
                type: 'Point',
                coordinates: [longitude, latitude],
            };
        }

        clinicTranslation.languageCode = lang;
        const clinicTranslations: ClinicTranslation[] = [];
        clinicTranslations.push(clinicTranslation);
        clinicEntity.translations = clinicTranslations;
        if (create) {
            clinicEntity.translations = [
                {
                    ...clinicTranslation,
                    languageCode: AvailableLanguageCodes.en,
                },
                {
                    ...clinicTranslation,
                    languageCode: AvailableLanguageCodes.ar,
                },
            ];
        }
        if (sourceObject.doctorId) {
            const doctor = new DoctorEntity();
            doctor.id = sourceObject.doctorId;
            clinicEntity.doctor = doctor;
        }
        if (sourceObject.governorateId) {
            const governorate = new Governorate();
            governorate.id = sourceObject.governorateId;
            clinicEntity.governorate = governorate;
        }
        if (sourceObject.cityId) {
            const city = new City();
            city.id = sourceObject.cityId;
            clinicEntity.city = city;
        }
        this.helperService.removeEmptyKeys(clinicEntity);

        return clinicEntity;
    }

    fromEntityToDTO(
        destination: ClassType<ClinicDto>,
        sourceObject: Clinic,
    ): ClinicDto {
        const clinicDto = super.fromEntityToDTO(destination, sourceObject);
        if (clinicDto.translations && clinicDto.translations.length) {
            clinicDto.name = clinicDto.translations[0].name;
        }

        delete clinicDto.translations;
        return clinicDto;
    }

    fromEntitiesToDTOs(sourceObject: Clinic[], lang: string): ClinicDto[] {
        const clinicDtos: ClinicDto[] = [];

        sourceObject.forEach((clinic) => {
            const clinicDto = super.fromEntityToDTO(ClinicDto, clinic);
            clinicDto.id = clinic.id;
            clinicDto.email = clinic.email;
            if (clinicDto.translations && clinicDto.translations.length) {
                clinicDto.name = clinicDto.translations[0].name;
                clinicDto.address = clinicDto.translations[0].address;
                clinicDto.area = clinicDto.translations[0].area;
            }
            delete clinicDto.translations;
            if (clinic.location) {
                clinicDto.location = {
                    latitude: clinic.location.coordinates[1],
                    longitude: clinic.location.coordinates[0],
                };
            }
            if (clinicDto.city) {
                let cityName = clinicDto.city.name;
                if (lang === AvailableLanguageCodes.en) {
                    cityName = clinicDto.city.nameEn;
                }
                clinicDto.city = {
                    id: clinicDto.city.id,
                    name: cityName,
                };
            }
            if (clinicDto.governorate) {
                let governorateName = clinicDto.governorate.name;
                if (lang === AvailableLanguageCodes.en) {
                    governorateName = clinicDto.governorate.nameEn;
                }

                clinicDto.governorate = {
                    id: clinicDto.governorate.id,
                    name: governorateName,
                };
            }

            clinicDtos.push(clinicDto);
        });
        return clinicDtos;
    }
}
