import * as AWS from 'aws-sdk';

import { ConfigService } from '../../../config/config.service';

export abstract class AbstractAws {
    constructor(configService: ConfigService) {
        if (
            configService.AWS_REGION &&
            configService.AWS_ACCESS_KEY_ID &&
            configService.AWS_SECRET_ACCESS_KEY
        ) {
            AWS.config.update({
                region: configService.AWS_REGION,
                accessKeyId: configService.AWS_ACCESS_KEY_ID,
                secretAccessKey: configService.AWS_SECRET_ACCESS_KEY,
                signatureVersion: 'v4',
            });
        }
    }
}
