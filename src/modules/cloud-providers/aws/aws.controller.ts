'use strict';

import {
    Controller,
    Get,
    HttpStatus,
    Query,
    ValidationPipe,
} from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';

import { AwsService } from './aws.service';
import { SignedUrlQueryDto } from './s3/dto/signed-url-query.dto';
import { SignedUrlResponseDto } from './s3/dto/signed-url-response.dto';
import { SessionTokenResponseDto } from './sts/dto/session-token-response.dto';

@Controller('aws')
@ApiTags('aws')
export class AwsController {
    constructor(private readonly awsService: AwsService) {}

    @Get('/upload-url')
    @ApiResponse({
        description: 'get upload pre-signed url',
        type: SignedUrlResponseDto,
    })
    getUploadUrl(
        @Query(new ValidationPipe({ transform: true }))
        uploadUrlQueryDto: SignedUrlQueryDto,
    ): SignedUrlResponseDto {
        return this.awsService.getUploadUrl(uploadUrlQueryDto);
    }

    @Get('/download-url')
    @ApiResponse({
        description: 'get download pre-signed url',
        type: SignedUrlResponseDto,
    })
    getDownloadUrl(
        @Query(new ValidationPipe({ transform: true }))
        downloadUrlQueryDto: SignedUrlQueryDto,
    ): SignedUrlResponseDto {
        return this.awsService.getDownloadUrl(downloadUrlQueryDto);
    }

    @Get('/session-token')
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'get temporary credentials',
        type: SessionTokenResponseDto,
    })
    getSessionToken(): Promise<SessionTokenResponseDto> {
        return this.awsService.getSessionToken();
    }
}
