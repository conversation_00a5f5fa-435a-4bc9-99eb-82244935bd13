import { HttpException, HttpStatus, Injectable } from '@nestjs/common';

import { ConfigService } from '../../../config/config.service';
import { SignedUrlQueryDto } from './s3/dto/signed-url-query.dto';
import { SignedUrlResponseDto } from './s3/dto/signed-url-response.dto';
import { S3Service } from './s3/s3.service';
import { SessionTokenResponseDto } from './sts/dto/session-token-response.dto';
import { StsService } from './sts/sts.service';

@Injectable()
export class AwsService {
    constructor(
        private readonly s3Service: S3Service,
        private readonly stsService: StsService,
        private readonly configService: ConfigService,
    ) {}

    getUploadUrl(uploadUrlQueryDto: SignedUrlQueryDto): SignedUrlResponseDto {
        try {
            const uploadUrl = this.s3Service.getUploadSignedUrl(
                uploadUrlQueryDto.key,
            );
            const key = uploadUrlQueryDto.key;
            return {
                uploadUrl,
                filedUrl: `https://${this.configService.AWS_S3_BUCKET_NAME}.s3.${this.configService.AWS_REGION}.amazonaws.com/${key}`,
            };
        } catch (e) {
            console.error(e);
            throw new HttpException(
                'Failed to get upload url',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
    getDownloadUrl(
        downloadUrlQueryDto: SignedUrlQueryDto,
    ): SignedUrlResponseDto {
        try {
            const uploadUrl = this.s3Service.getDownloadSignedUrl(
                downloadUrlQueryDto.key,
            );
            const key = downloadUrlQueryDto.key;
            return {
                uploadUrl,
                filedUrl: `https://${this.configService.AWS_S3_BUCKET_NAME}.s3.${this.configService.AWS_REGION}.amazonaws.com/${key}`,
            };
        } catch (e) {
            console.error(e);
            throw new HttpException(
                'Failed to get download url',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async getSessionToken(): Promise<SessionTokenResponseDto> {
        try {
            const token = await this.stsService.getSessionToken();
            return {
                accessKeyId: token.Credentials.AccessKeyId,
                secretAccessKey: token.Credentials.SecretAccessKey,
                sessionToken: token.Credentials.SessionToken,
                expiration: token.Credentials.Expiration,
            };
        } catch (e) {
            console.error(e);
            throw new HttpException(
                'Failed to get session token',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
}
