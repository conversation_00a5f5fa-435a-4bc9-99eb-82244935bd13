import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import * as fs from 'fs';
import * as path from 'path';

import { ConfigService } from '../../../../config/config.service';
import { AbstractAws } from '../aws.abstract';
import { promisify } from 'util';
import Mail from 'nodemailer/lib/mailer';

@Injectable()
export class S3Service extends AbstractAws {
    constructor(private readonly configService: ConfigService) {
        super(configService);
    }

    getSignedUrl(operation: string, fileKey: string): string {
        const expirationTimeInMinutes = this.configService
            .AWS_EXPIRATION_TIME_IN_MINUTES;
        const bucketName = this.configService.AWS_S3_BUCKET_NAME;
        const s3 = new AWS.S3({ apiVersion: '2006-03-01' });
        return s3.getSignedUrl(operation, {
            Bucket: bucketName,
            Key: fileKey,
            Expires: expirationTimeInMinutes * 60,
            ACL: 'public-read',
        });
    }

    getUploadSignedUrl(fileKey: string): string {
        return this.getSignedUrl('putObject', fileKey);
    }

    getDownloadSignedUrl(fileKey: string): string {
        return this.getSignedUrl('getObject', fileKey);
    }

    async downloadImageFromS3(key: string): Promise<Mail.Attachment> {
        const params = {
            Bucket: this.configService.AWS_S3_BUCKET_NAME,
            Key: key,
        };
        const s3 = new AWS.S3({ apiVersion: '2006-03-01' });
        const data = await s3.getObject(params).promise();

        const filePath = path.join(__dirname, key);
        await promisify(fs.writeFile)(
            filePath,
            Buffer.from(data.Body as Buffer).toString('base64'),
        );

        return {
            filename: path.basename(key),
            path: filePath,
        };
    }
}
