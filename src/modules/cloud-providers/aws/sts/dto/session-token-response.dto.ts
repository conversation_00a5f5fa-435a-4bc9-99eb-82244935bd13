'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsNotEmpty, IsString } from 'class-validator';

export class SessionTokenResponseDto {
    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    accessKeyId: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    secretAccessKey: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    sessionToken: string;

    @ApiProperty()
    @IsDate()
    @IsNotEmpty()
    expiration: Date;
}
