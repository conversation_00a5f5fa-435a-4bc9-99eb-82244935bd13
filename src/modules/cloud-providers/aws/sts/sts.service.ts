import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';

import { ConfigService } from '../../../../config/config.service';
import { AbstractAws } from '../aws.abstract';

@Injectable()
export class StsService extends AbstractAws {
    constructor(private readonly configService: ConfigService) {
        super(configService);
    }

    async getSessionToken(): Promise<AWS.STS.GetSessionTokenResponse> {
        const expirationTimeInMinutes = this.configService
            .AWS_EXPIRATION_TIME_IN_MINUTES;
        const sts = new AWS.STS({ apiVersion: '2011-06-15' });
        return sts
            .getSessionToken({
                DurationSeconds: expirationTimeInMinutes * 60,
            })
            .promise();
    }
}
