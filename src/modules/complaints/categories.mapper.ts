import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CategoryDto } from './dto/category.dto';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Category } from './entities/category.entity';

type CategoryDTOs = CreateCategoryDto | UpdateCategoryDto | CategoryDto;
@Injectable()
export class CategoriesMapper extends AbstractMapper<CategoryDTOs, Category> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<Category>,
        sourceObject: CategoryDTOs,
    ): Category {
        const categoryEntity = super.fromDTOToEntity(destination, sourceObject);
        this.helperService.removeEmptyKeys(categoryEntity);
        return categoryEntity;
    }

    fromEntityToDTO(
        destination: ClassType<CategoryDto>,
        sourceObject: Category,
    ): CategoryDto {
        const gradeDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(gradeDto);
        return gradeDto;
    }
}
