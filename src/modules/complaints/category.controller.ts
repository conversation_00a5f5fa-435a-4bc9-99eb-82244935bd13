import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { GradeDto } from '../grades/dto/grade.dto';
import { CategoryService } from './category.service';
import { CategoryDto } from './dto/category.dto';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';

@Controller('categories')
@ApiTags('categories')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseGuards(AuthGuard, RolesGuard)
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class CategoryController {
    constructor(private readonly categoryService: CategoryService) {
    }

    // @Post()
    // @HttpCode(HttpStatus.OK)
    // @Roles(RoleType.ADMIN)
    // create(@Body() createCategoryDto: CreateCategoryDto): Promise<Category> {
    //     return this.categoryService.create(createCategoryDto);
    // }
    //
    // @Get()
    // @HttpCode(HttpStatus.OK)
    // @Roles(
    //     RoleType.DOCTOR,
    //     RoleType.PATIENT,
    //     RoleType.ADMIN,
    //     RoleType.SUPER_ADMIN,
    // )
    // findAll(): Promise<Category[]> {
    //     return this.categoryService.findAll();
    // }
    //
    // @Get(':id')
    // @HttpCode(HttpStatus.OK)
    // @Roles(
    //     RoleType.DOCTOR,
    //     RoleType.PATIENT,
    //     RoleType.ADMIN,
    //     RoleType.SUPER_ADMIN,
    // )
    // findOne(@Param('id') id: string): Promise<Category> {
    //     return this.categoryService.findOne(+id);
    // }

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get Category',
        type: CategoryDto,
    })
    async getCategory(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<GradeDto> {
        return this.categoryService.getCategory(id, lang);
    }

    @Get()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get all Categories',
        type: [CategoryDto],
    })
    getAllCategories(): Promise<CategoryDto[]> {
        return this.categoryService.getAllCategoriesForAdmin();
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create Category',
        type: CreateOperationsResponse,
    })
    createCategory(
        @Body() createCategoryDto: CreateCategoryDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.categoryService.createCategory(createCategoryDto, lang);
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update Category',
        type: BasicOperationsResponse,
    })
    async updateCategory(
        @Param('id') id: number,
        @Body() updateCategoryDto: UpdateCategoryDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.categoryService.updateCategory(id, updateCategoryDto, lang);
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete Category',
        type: BasicOperationsResponse,
    })
    async deleteCategory(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.categoryService.deleteCategory(id, lang);
    }
}
