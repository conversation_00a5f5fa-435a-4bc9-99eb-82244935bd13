import { Modu<PERSON> } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { CategoriesMapper } from './categories.mapper';
import { CategoryController } from './category.controller';
import { CategoryService } from './category.service';
import { CategoriesTranslationsRepository } from './repositories/categories-translations.repository';
import { CategoriesRepository } from './repositories/category.repository';

@Module({
    imports: [SharedModule],
    controllers: [CategoryController],
    exports: [CategoryService],
    providers: [
        CategoryService,
        CategoriesMapper,
        CategoriesRepository,
        CategoriesTranslationsRepository,
    ],
})
export class CategoryModule {}
