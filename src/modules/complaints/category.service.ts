import {
    HttpException,
    HttpStatus,
    Injectable,
    NotFoundException,
} from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { DebugLogger } from '../../shared/services/logger.service';
import { GradeMessagesKeys } from '../grades/translate.enum';
import { CategoriesMapper } from './categories.mapper';
import { CategoryDto } from './dto/category.dto';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryTranslation } from './entities/category-translation.entity';
import { Category } from './entities/category.entity';
import { CategoriesTranslationsRepository } from './repositories/categories-translations.repository';
import { CategoriesRepository } from './repositories/category.repository';

@Injectable()
export class CategoryService {
    constructor(
        private readonly categoriesRepository: CategoriesRepository,
        private readonly categoriesTranslationsRepository: CategoriesTranslationsRepository,
        private readonly categoriesMapper: CategoriesMapper,
        private readonly i18n: I18nService,
        private readonly helperService: HelperService,
        private readonly logger: DebugLogger,
    ) { }

    async create(createCategoryDto: CreateCategoryDto): Promise<Category> {
        const category = this.categoriesRepository.create(createCategoryDto);
        return this.categoriesRepository.save(category);
    }

    async findAll(): Promise<Category[]> {
        return this.categoriesRepository.find();
    }

    async findOne(id: number): Promise<Category> {
        const category = await this.categoriesRepository.findOne({
            where: { id },
        });
        if (!category) {
            throw new NotFoundException('Category not found');
        }
        return category;
    }

    async getCategory(id: number, lang: string): Promise<CategoryDto> {
        const category = await this.categoriesRepository.findOne({
            where: { id },
            relations: ['translations'],
        });
        if (!category) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        GradeMessagesKeys.GRADE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.categoriesMapper.fromEntityToDTO(CategoryDto, category);
    }

    async getAllCategories(lang: string): Promise<CategoryDto[]> {
        const catgoriesEntities = await this.categoriesRepository
            .createQueryBuilder('category')
            .leftJoinAndSelect(
                'category.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .getMany();

        const catgoriesDtos = catgoriesEntities.map((categoryEntity) =>
            this.categoriesMapper.fromEntityToDTO(CategoryDto, categoryEntity),
        );

        return catgoriesDtos
            .filter((category) => category.translations.length > 0)
            .map((category) => ({
                id: category.id,
                title: category.translations[0].title,
            }));
    }

    async getAllCategoriesForAdmin(): Promise<CategoryDto[]> {
        const categories = await this.categoriesRepository.find({
            relations: ['translations'],
        });
        return categories.map((category) =>
            this.categoriesMapper.fromEntityToDTO(CategoryDto, category),
        );
    }

    async createCategory(
        createCategoryDto: CreateCategoryDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const category = this.categoriesMapper.fromDTOToEntity(
            Category,
            createCategoryDto,
        );

        const categoryEntity = this.categoriesRepository.create(category);
        const createdCategory = await this.categoriesRepository.save(
            categoryEntity,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                GradeMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdCategory.id,
        };
    }

    async updateCategory(
        id: number,
        updateCategoryDto: UpdateCategoryDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const category = await this.categoriesRepository.findOne({
            where: { id },
        });

        if (!category) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        GradeMessagesKeys.GRADE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },

                HttpStatus.NOT_FOUND,
            );
        }

        const categoryEntity = this.categoriesMapper.fromDTOToEntity(
            Category,
            updateCategoryDto,
        );

        if (categoryEntity.translations) {
            await this.updateTranslation(id, categoryEntity.translations);
            delete category.translations;
        }

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                GradeMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async deleteCategory(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const category = await this.categoriesRepository.findOne({
            where: { id },
        });

        if (!category) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        GradeMessagesKeys.GRADE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.categoriesRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                GradeMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async updateTranslation(
        parentId: number,
        updatedTranslation: CategoryTranslation[],
    ): Promise<void> {
        await Promise.all(
            updatedTranslation.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);
                await this.categoriesTranslationsRepository.update(
                    {
                        category: { id: parentId },
                        languageCode: translation.languageCode,
                    },
                    translation,
                );
            }),
        );
    }
}
