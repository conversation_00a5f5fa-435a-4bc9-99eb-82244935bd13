import {
    Body,
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Query,
    Req,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import { Api<PERSON><PERSON>erA<PERSON>, ApiHeader, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { ComplaintService } from './complaint.service';
import { ComplaintDto } from './dto/complaint.dto';
import { ComplaintsPageDto } from './dto/complaintPage.dto';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { Complaint } from './entities/complaint.entity';

@Controller('complaints')
@ApiTags('complaints')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseGuards(AuthGuard, RolesGuard)
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class ComplaintController {
    constructor(private readonly complaintService: ComplaintService) {}

    @Post()
    @HttpCode(HttpStatus.OK)
    @Roles(RoleType.DOCTOR, RoleType.PATIENT)
    create(
        @Req() req: Request,
        @Body() createComplaintDto: CreateComplaintDto,
    ): Promise<Complaint> {
        return this.complaintService.create(req.user.id, createComplaintDto);
    }

    @Get()
    @HttpCode(HttpStatus.OK)
    @Roles(
        RoleType.DOCTOR,
        RoleType.PATIENT,
        RoleType.ADMIN,
        RoleType.SUPER_ADMIN,
    )
    findAll(
        @Req() req: Request,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @I18nLang() lang: string,
    ): Promise<ComplaintsPageDto> {
        // switch (req.user.role) {
        //     case RoleType.PATIENT:
        //     case RoleType.DOCTOR:
        //         return this.complaintService.findAll(req.user.id);
        //     case RoleType.ADMIN:
        //     case RoleType.SUPER_ADMIN:
        return this.complaintService.findAllForAdmin(lang, pageOptionsDto);
        // }
    }

    @Get(':id')
    @HttpCode(HttpStatus.OK)
    @Roles(
        RoleType.DOCTOR,
        RoleType.PATIENT,
        RoleType.ADMIN,
        RoleType.SUPER_ADMIN,
    )
    findOne(
        @Param('id') id: string,
        @I18nLang() lang: string,
    ): Promise<ComplaintDto> {
        return this.complaintService.findOne(+id, lang);
    }

    // @Patch(':id/status')
    // @HttpCode(HttpStatus.OK)
    // @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    // updateStatus(
    //     @Param('id') id: string,
    //     @Body() updateComplaintStatusDto: UpdateComplaintStatusDto,
    // ): Promise<Complaint> {
    //     return this.complaintService.updateStatus(
    //         +id,
    //         updateComplaintStatusDto,
    //     );
    // }
}
