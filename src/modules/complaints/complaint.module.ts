import { Module } from '@nestjs/common';

import { CategoryModule } from './category.module';
import { ComplaintController } from './complaint.controller';
import { ComplaintService } from './complaint.service';
import { ComplaintsMapper } from './complaints.mapper';
import { CategoriesRepository } from './repositories/category.repository';
import { ComplaintsRepository } from './repositories/complaint.repository';

@Module({
    imports: [CategoryModule],
    providers: [
        ComplaintService,
        ComplaintsMapper,
        ComplaintsRepository,
        CategoriesRepository,
    ],
    controllers: [ComplaintController],
})
export class ComplaintModule {
}
