import {
    HttpException,
    HttpStatus,
    Injectable,
    NotFoundException,
} from '@nestjs/common';

import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { DebugLogger } from '../../shared/services/logger.service';
import { tryCatch } from '../../utils/helpers';
import { ConsultationRequestEntity } from '../consultation-requests/entities/consultation-request.entity';
import { UserEntity } from '../users/entities/user.entity';
import { ComplaintsMapper } from './complaints.mapper';
import { ComplaintDto } from './dto/complaint.dto';
import { ComplaintsPageDto } from './dto/complaintPage.dto';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { Complaint } from './entities/complaint.entity';
import { CategoriesRepository } from './repositories/category.repository';
import { ComplaintsRepository } from './repositories/complaint.repository';

@Injectable()
export class ComplaintService {
    constructor(
        private complaintRepository: ComplaintsRepository,
        private categoryRepository: CategoriesRepository,
        private readonly complaintsMapper: ComplaintsMapper,
        private readonly logger: DebugLogger,
    ) {}

    async create(
        userId: number,
        createComplaintDto: CreateComplaintDto,
    ): Promise<Complaint> {
        const category = await this.categoryRepository.findOne({
            where: {
                id: createComplaintDto.categoryId,
            },
        });
        if (!category) {
            throw new NotFoundException('Category not found');
        }

        const user = new UserEntity();
        user.id = userId;

        const request = new ConsultationRequestEntity();

        request.id = createComplaintDto.requestId;

        const complaint = this.complaintRepository.create({
            description: createComplaintDto.description,
            request,
            category,
            user,
        });

        return this.complaintRepository.save(complaint);
    }

    async findAll(userId: number): Promise<Complaint[]> {
        return this.complaintRepository.find({
            relations: ['category'],
            where: {
                user: {
                    id: userId,
                },
            },
        });
    }

    async findAllForAdmin(
        lang: string,
        pageOptionsDto: PageOptionsDto,
    ): Promise<ComplaintsPageDto> {
        const query = this.complaintRepository
            .createQueryBuilder('complaint')
            .leftJoinAndSelect('complaint.category', 'category')
            .leftJoinAndSelect(
                'category.translations',
                'categoryTranslations',
                'categoryTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('complaint.request', 'request')
            .leftJoinAndSelect('complaint.user', 'user')
            .select([
                'complaint.id',
                'complaint.status',
                'complaint.description',
                'category.id',
                'categoryTranslations.title',
                'user.id',
                'user.name',
                'user.email',
                'user.phone',
                'request.id',
                'request.status',
                'request.type',
            ]);

        const { isSuccess, value, error } = await tryCatch(
            query.paginate(pageOptionsDto),
        );

        if (!isSuccess) {
            throw new HttpException(
                {
                    message: error.message,
                },
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }

        const [complaints, pageMetaDto] = value;

        const mappedComplaints = complaints.map((complaint) =>
            this.complaintsMapper.fromEntityToDTO(ComplaintDto, complaint),
        );

        return new ComplaintsPageDto(mappedComplaints, pageMetaDto);
    }

    async findOne(id: number, lang: string): Promise<ComplaintDto> {
        const complaint = await this.complaintRepository
            .createQueryBuilder('complaint')
            .leftJoinAndSelect('complaint.category', 'category')
            .leftJoinAndSelect(
                'category.translations',
                'categoryTranslations',
                'categoryTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('complaint.request', 'request')
            .leftJoinAndSelect('complaint.user', 'user')
            .where('complaint.id = :id', { id })
            .select([
                'complaint.id',
                'complaint.status',
                'complaint.description',
                'category.id',
                'categoryTranslations.title',
                'user.id',
                'user.name',
                'user.email',
                'user.phone',
                'request.id',
                'request.status',
                'request.type',
            ])
            .getOne();

        if (!complaint) {
            throw new NotFoundException('Complaint not found');
        }
        return this.complaintsMapper.fromEntityToDTO(ComplaintDto, complaint);
    }

    // async updateStatus(
    //     id: number,
    //     updateComplaintStatusDto: UpdateComplaintStatusDto,
    // ): Promise<Complaint> {
    //     const complaint = await this.findOne(id);
    //     complaint.status = updateComplaintStatusDto.status;
    //     return this.complaintRepository.save(complaint);
    // }
}
