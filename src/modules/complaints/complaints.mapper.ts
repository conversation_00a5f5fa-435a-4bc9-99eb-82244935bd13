import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { ComplaintDto } from './dto/complaint.dto';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { Complaint } from './entities/complaint.entity';

type ComplaintDtos = CreateComplaintDto | ComplaintDto;

@Injectable()
export class ComplaintsMapper extends AbstractMapper<ComplaintDtos, Complaint> {
    constructor(private readonly helperService: HelperService) {
        super();
    }

    fromEntityToDTO(
        destination: ClassType<ComplaintDto>,
        sourceObject: Complaint,
    ): ComplaintDto {
        const doctorDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(doctorDto);
        return doctorDto;
    }

    fromDTOToEntity(
        destination: ClassType<Complaint>,
        sourceObject: ComplaintDtos,
    ): Complaint {
        const accountantEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(accountantEntity);
        return accountantEntity;
    }

    // fromEntityToDownloadData(
    //     destination: ClassType<DownloadAccountantsDto>,
    //     sourceObject: AccountantEntity,
    // ): DownloadAccountantsDto {
    //     return {
    //         id: sourceObject.id,
    //         email: sourceObject.user.email,
    //         isActive: sourceObject.user.isActive,
    //         name: sourceObject.user.name,
    //         phone: sourceObject.user.phone,
    //         role: sourceObject.user.role,
    //     };
    // }
}
