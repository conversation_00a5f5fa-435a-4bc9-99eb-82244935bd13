'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { CategoryTranslation } from '../entities/category-translation.entity';

export class CategoryDto extends AbstractDto {
    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => CategoryTranslation)
    translations?: CategoryTranslation[];
}
