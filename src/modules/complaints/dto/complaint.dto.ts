import { Expose, Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ConsultationRequestDto } from '../../consultation-requests/dto/consultation-request.dto';
import { UserDto } from '../../users/dto/user.dto';
import { ComplaintStatus } from '../entities/complaint.entity';
import { CategoryDto } from './category.dto';

export class ComplaintDto extends AbstractDto {
    @IsEnum(ComplaintStatus)
    @IsOptional()
    @Expose()
    status?: ComplaintStatus;

    @IsString()
    @IsOptional()
    @Expose()
    description?: string;

    @Expose()
    @IsOptional()
    @Type(() => CategoryDto)
    category?: CategoryDto;

    @Expose()
    @IsOptional()
    @Type(() => UserDto)
    user?: UserDto;

    @Expose()
    @IsOptional()
    @Type(() => ConsultationRequestDto)
    request?: ConsultationRequestDto;
}
