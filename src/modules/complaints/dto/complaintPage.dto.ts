import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { ComplaintDto } from './complaint.dto';

export class ComplaintsPageDto {
    @ApiProperty({
        type: ComplaintDto,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => ComplaintDto)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: ComplaintDto[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: ComplaintDto[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
