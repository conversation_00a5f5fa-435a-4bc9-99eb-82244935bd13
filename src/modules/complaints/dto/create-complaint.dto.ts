import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreateComplaintDto extends AbstractDto {
    @IsString()
    @Expose()
    name: string;

    @IsString()
    @Expose()
    description: string;

    @IsNumber()
    @Expose()
    categoryId: number;

    @IsNumber()
    @Expose()
    @IsOptional()
    requestId: number;
}
