import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { CategoryTranslation } from '../entities/category-translation.entity';

export class UpdateCategoryDto extends AbstractDto {
    @ApiPropertyOptional({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @Type(() => CategoryTranslation)
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: CategoryTranslation[];
}
