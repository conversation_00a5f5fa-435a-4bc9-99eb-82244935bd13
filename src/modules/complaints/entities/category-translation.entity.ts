import { Expose, Type } from 'class-transformer';
import { IsObject, IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Category } from './category.entity';

@Entity('categories_translations')
export class CategoryTranslation extends AbstractEntity {
    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    title?: string;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    languageCode?: string;

    @ManyToOne((_type) => Category, (category) => category.translations, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => Category)
    category?: Category;
}
