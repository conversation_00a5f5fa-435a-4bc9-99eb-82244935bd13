import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional } from 'class-validator';
import { Entity, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { CategoryTranslation } from './category-translation.entity';
import { Complaint } from './complaint.entity';

@Entity('categories')
export class Category extends AbstractEntity {
    @OneToMany(
        (_type) => CategoryTranslation,
        (categoryTranslation) => categoryTranslation.category,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => CategoryTranslation)
    translations?: CategoryTranslation[];

    @OneToMany(() => Complaint, (complaint) => complaint.category)
    complaints: Complaint[];
}
