import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { Category } from './category.entity';

export enum ComplaintStatus {
    PENDING = 'pending',
    REVIEWED = 'reviewed',
    CLOSED = 'closed',
}

@Entity('complaints')
export class Complaint extends AbstractEntity {
    @Column()
    description: string;

    @ManyToOne(() => Category, (category) => category.complaints, {
        eager: true,
    })
    category: Category;

    @ManyToOne(
        () => ConsultationRequestEntity,
        (request) => request.complaints,
        {
            eager: true,
        },
    )
    request: ConsultationRequestEntity;

    @ManyToOne(() => UserEntity, (user) => user.complaints)
    user: UserEntity;

    @Column({
        type: 'enum',
        enum: ComplaintStatus,
        default: ComplaintStatus.PENDING,
    })
    status: ComplaintStatus;
}
