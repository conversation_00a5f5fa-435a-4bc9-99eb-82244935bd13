'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { IFilter } from './consultation-requests.model';
import { ConsultationRequestsService } from './consultation-requests.service';
import { ConsultationRequestDto } from './dto/consultation-request.dto';
import { ConsultationRequestsPageDto } from './dto/consultation-requests-page.dto';
import { CreateConsultationRequestDto } from './dto/create-consultation-request.dto';
import { DownloadConsultationRequestDto } from './dto/download-consultation-request.dto';
import { UpdateConsultationRequestDto } from './dto/update-consultation-request.dto';
import { VerifyConsultationRequestDto } from './dto/verify-consultation-request.dto';

@Controller('consultation-requests')
@ApiTags('consultationRequests')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class ConsultationRequestsController {
    constructor(
        private consultationRequestsService: ConsultationRequestsService,
    ) {}

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT)
    @ApiResponse({
        description: 'Create Consultation Request',
        type: CreateOperationsResponse,
    })
    create(
        @Body() createConsultationRequestDto: CreateConsultationRequestDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.consultationRequestsService.createRequest(
            createConsultationRequestDto,
            lang,
        );
    }



    @Put('/:id')
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Update Consultation Request',
        type: BasicOperationsResponse,
    })
    update(
        @Req() req: Request,
        @Param('id') id: number,
        @Body() requestDto: UpdateConsultationRequestDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.consultationRequestsService.updateRequest(
            requestDto,
            id,
            req.user,
            lang,
        );
    }

    @Get('/download')
    @ApiResponse({
        description: 'Download consultation requests',
        type: [DownloadConsultationRequestDto],
    })
    async downloadRequests(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
    ): Promise<void> {
        const data = await this.consultationRequestsService.downloadRequests(
            providerId,
            query,
        );
        res.setHeader(
            'Content-disposition',
            'attachment; filename=requests.csv',
        );
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get()
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get All Requests',
        type: [ConsultationRequestDto],
    })
    getRequests(
        @Req() req: Request,
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @I18nLang() lang: string,
    ): Promise<ConsultationRequestDto[] | ConsultationRequestsPageDto> {
        const userId = req.user.id;
        const filter: IFilter = req.query;

        switch (req.user.role) {
            case RoleType.ADMIN:
            case RoleType.SUPER_ADMIN:
            case RoleType.SERVICE_PROVIDER:
                return this.consultationRequestsService.getAllRequestsForAdmin(
                    req.user,
                    query,
                    pageOptionsDto,
                );
            case RoleType.DOCTOR:
                filter.doctorId = req.user.doctor.id;
                return this.consultationRequestsService.getAllDoctorRequests(
                    filter,
                    lang,
                );
            case RoleType.PATIENT:
                filter.patientId = req.user.patient.id;
                return this.consultationRequestsService.getAllPatientRequests(
                    userId,
                    filter,
                    lang,
                );
        }
    }

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get one request by ID',
        type: ConsultationRequestDto,
    })
    getRequest(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<ConsultationRequestDto> {
        switch (req.user.role) {
            case RoleType.ADMIN:
            case RoleType.SUPER_ADMIN:
            case RoleType.SERVICE_PROVIDER:
                return this.consultationRequestsService.getRequestForAdmin(
                    id,
                    lang,
                );
            default:
                return this.consultationRequestsService.getRequest(id, lang);
        }
    }

    @Delete()
    deleteRequest(@I18nLang() lang: string): string {
        return `delete request: ${lang}`;
    }

    @Post(':id/verification')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.DOCTOR)
    @ApiResponse({
        description: 'Verify Consultation Request Code',
        type: BasicOperationsResponse,
    })
    verifyConsultationRequest(
        @Req() req: Request,
        @Param('id') id: number,
        @Body() verifyConsultationRequestDto: VerifyConsultationRequestDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.consultationRequestsService.verifyConsultationRequest(
            id,
            verifyConsultationRequestDto.code,
            req.user.doctor.id,
            lang,
        );
    }
}
