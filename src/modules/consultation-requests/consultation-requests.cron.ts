import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { PsuedoLogger } from '../../common/PsuedoLogger';
import { ConsultationRequestsService } from './consultation-requests.service';

@Injectable()
export class CronConsultationRequest {
    private readonly logger = new PsuedoLogger();

    constructor(
        private consultationRequestsService: ConsultationRequestsService,
    ) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async updateRequestsAsExpired(): Promise<void> {
        const [
            scheduledRequestsIds,
            nowHomeVisitRequestsIds,
            nowOnlineRequestsIds,
            nowOnlineAcceptedRequestsIds,
            onlineAcceptedRequestsIds,
            scheduledRequestsIdsPendingPayment,
            nowHomeVisitRequestsIdsPendingPayment,
            nowOnlineRequestsIdsPendingPayment,
            nowOnlineAcceptedRequestsIdsPendingPayment,
            onlineAcceptedRequestsIdsPendingPayment,
        ] = await Promise.all([
            this.consultationRequestsService.markScheduledRequestsAsExpired(),
            this.consultationRequestsService.markNowHomeVisitRequestsAsExpired(),
            this.consultationRequestsService.markNowOnlineRequestsAsExpired(),
            this.consultationRequestsService.markNowOnlineRequestsAsExpiredAfterAccept(),
            this.consultationRequestsService.markOnlineRequestsAsExpiredAfterAccept(),
            this.consultationRequestsService.markScheduledRequestsAsExpired(
                true,
            ),
            this.consultationRequestsService.markNowHomeVisitRequestsAsExpired(
                true,
            ),
            this.consultationRequestsService.markNowOnlineRequestsAsExpired(
                true,
            ),
            this.consultationRequestsService.markNowOnlineRequestsAsExpiredAfterAccept(
                true,
            ),
            this.consultationRequestsService.markOnlineRequestsAsExpiredAfterAccept(
                true,
            ),
        ]);
        const requestsIds = [
            ...scheduledRequestsIds,
            ...nowHomeVisitRequestsIds,
            ...nowOnlineRequestsIds,
            ...nowOnlineAcceptedRequestsIds,
            ...onlineAcceptedRequestsIds,
            ...scheduledRequestsIdsPendingPayment,
            ...nowHomeVisitRequestsIdsPendingPayment,
            ...nowOnlineRequestsIdsPendingPayment,
            ...nowOnlineAcceptedRequestsIdsPendingPayment,
            ...onlineAcceptedRequestsIdsPendingPayment,
        ];
        this.consultationRequestsService
            .SendExpirationNotifications(requestsIds)
            .catch((error) => this.logger.error(error));
    }

    @Cron(CronExpression.EVERY_MINUTE)
    async updateRequestsAsExpiredForNoResponseFromPatient(): Promise<void> {
        const ids = await this.consultationRequestsService.markRequestsAsExpiredIfNotPaid();
        this.consultationRequestsService
            .sendExpiredNotificationForUserNotResponding(ids)
            .catch((error) => this.logger.error(error));
    }

    @Cron(CronExpression.EVERY_MINUTE)
    async updateOnlineConsulationWithAgoraToken(): Promise<void> {
        await this.consultationRequestsService.updateOnlineConsulationWithAgoraToken();
    }

    @Cron(CronExpression.EVERY_HOUR)
    async updateDoctorPaymentUnsettledState(): Promise<void> {
        const requests = await this.consultationRequestsService.getUnsettledPaymentRequests();

        for (const request of requests) {
            await this.consultationRequestsService.activatePendingShareDoctorAndSendPaymentUnsettledNotification(
                request.doctor.user.id,
                request.doctor.id,
                request.id,
                request.doctor.user.appLanguage,
            );
        }
    }

    // @Cron(CronExpression.EVERY_HOUR)
    // async remindPatientAndDoctorsForAppointmentsDayBefore(): Promise<void> {
    //     const ids = await this.consultationRequestsService.findRequestsOneDayBefore();
    //     this.consultationRequestsService
    //         .sendReminderNotifications(ids)
    //         .catch((error) => this.logger.error(error));
    // }
    //
    // @Cron(CronExpression.EVERY_HOUR)
    // async remindPatientAndDoctorsForAppointments2HoursBefore(): Promise<void> {
    //     const ids = await this.consultationRequestsService.findRequestsTwoHoursBefore();
    //     this.consultationRequestsService
    //         .sendReminderNotifications(ids)
    //         .catch((error) => this.logger.error(error));
    // }
    //
    // @Cron(CronExpression.EVERY_MINUTE)
    // async remindPatientAndDoctorsForAppointments15MinutesBefore(): Promise<
    //     void
    // > {
    //     const ids = await this.consultationRequestsService.findRequestsFifteenMinutesBefore();
    //     this.consultationRequestsService
    //         .sendReminderNotifications(ids)
    //         .catch((error) => this.logger.error(error));
    // }
}
