import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';
import { get } from 'lodash';

import { AbstractMapper } from '../../common/abstract.mapper';
import { ConsultationRequestDto } from './dto/consultation-request.dto';
import { DownloadConsultationRequestDto } from './dto/download-consultation-request.dto';
import { ConsultationRequestEntity } from './entities/consultation-request.entity';

@Injectable()
export class ConsultationRequestsMapper extends AbstractMapper<
    ConsultationRequestDto,
    ConsultationRequestEntity
> {
    constructor() {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<ConsultationRequestEntity>,
        sourceObject: ConsultationRequestDto,
    ): ConsultationRequestEntity {
        return super.fromDTOToEntity(destination, sourceObject);
    }

    fromEntityToDTO(
        destination: ClassType<ConsultationRequestDto>,
        sourceObject: ConsultationRequestEntity,
    ): ConsultationRequestDto {
        const consultationRequestDto = super.fromEntityToDTO(
            destination,
            sourceObject,
        );
        if (sourceObject.patientLocation) {
            consultationRequestDto.patientLocation = {
                latitude: sourceObject.patientLocation.coordinates[1],
                longitude: sourceObject.patientLocation.coordinates[0],
            };
        }

        return consultationRequestDto;
    }

    fromEntityToDownloadData(
        destination: ClassType<DownloadConsultationRequestDto>,
        sourceObject: ConsultationRequestEntity,
    ): DownloadConsultationRequestDto {
        return {
            id: sourceObject.id,
            consultationType: sourceObject.type,
            date: sourceObject.date.toString(),
            time: sourceObject.from ? sourceObject.from.toString() : '',
            status: sourceObject.status,
            doctorName: get(sourceObject, 'doctor.user.name', ''),
            doctorMobileNumber: get(sourceObject, 'doctor.user.phone', ''),
            patientName: get(sourceObject, 'patient.user.name', ''),
            patientMobileNumber: get(sourceObject, 'patient.user.phone', ''),
        };
    }
}
