import { forwardRef, Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ConfigModule } from '../../config/config.module';
import { SharedModule } from '../../shared/shared.module';
import { ClinicsModule } from '../clinics/clinics.module';
import { ClinicsMapper } from '../clinics/mappers/clinics.mapper';
import { DoctorsMapper } from '../doctors/doctors.mapper';
import { DoctorsModule } from '../doctors/doctors.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { PatientsModule } from '../patients/patients.module';
import { PaymentsModule } from '../payments/payments.module';
import { RatingsModule } from '../ratings/ratings.module';
import { RemindersModule } from '../reminders/reminders.module';
import { SchedulesDaysRepository } from '../schedules/repositories/schedules.days.repository';
import { SchedulesRepository } from '../schedules/repositories/schedules.repository';
import { SchedulesMapper } from '../schedules/schedules.mapper';
import { SchedulesService } from '../schedules/schedules.service';
import { ServiceProvidersModule } from '../service-providers/service-providers.module';
import { ServiceProvidersRepository } from '../service-providers/repositories/service-providers.repository';
import { ConsultationRequestsController } from './consultation-requests.controller';
import { CronConsultationRequest } from './consultation-requests.cron';
import { ConsultationRequestsMapper } from './consultation-requests.mapper';
import { ConsultationRequestsService } from './consultation-requests.service';
import { ConsultationRequestsHistoryRepository } from './repositories/consultation-requests-history.repository';
import { ConsultationRequestsRepository } from './repositories/consultation-requests.repository';

@Module({
    imports: [
        ConfigModule,
        SharedModule,
        forwardRef(() => DoctorsModule),
        PatientsModule,
        forwardRef(() => ScheduleModule),
        forwardRef(() => RatingsModule),
        NotificationsModule,
        forwardRef(() => PaymentsModule),
        forwardRef(() => ClinicsModule),
        forwardRef(() => ServiceProvidersModule),
        forwardRef(() => RemindersModule),
    ],
    controllers: [ConsultationRequestsController],
    exports: [ConsultationRequestsService],
    providers: [
        ConsultationRequestsService,
        ConsultationRequestsMapper,
        CronConsultationRequest,
        SchedulesService,
        SchedulesMapper,
        DoctorsMapper,
        ClinicsMapper,
        ConsultationRequestsRepository,
        ConsultationRequestsHistoryRepository,
        SchedulesRepository,
        SchedulesDaysRepository,
        ServiceProvidersRepository,
    ],
})
export class ConsultationRequestsModule {}
