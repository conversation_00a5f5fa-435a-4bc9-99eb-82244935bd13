import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

import { ConsultationRequestStatus } from '../../../common/constants/status';
import { RequestUpdateType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { UserDto } from '../../users/dto/user.dto';
import { ConsultationRequestDto } from '../dto/consultation-request.dto';

export class ConsultationRequestHistoryDto extends AbstractDto {
    @IsString()
    @IsOptional()
    @Expose()
    status?: ConsultationRequestStatus;

    @IsString()
    @IsOptional()
    @Expose()
    updateType?: RequestUpdateType;

    @Type(() => ConsultationRequestDto)
    @IsOptional()
    @Expose()
    consultationRequest?: ConsultationRequestDto;

    @Type(() => UserDto)
    @IsOptional()
    @Expose()
    createdBy?: UserDto;
}
