'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsDate,
    IsDateString,
    IsEnum,
    IsLatLong,
    IsNumber,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';

import {
    ConsultationRequestStatus,
    PaymentStatus,
} from '../../../common/constants/status';
import {
    ConsultationType,
    GenderType,
    PaymentMethod,
} from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ILocation } from '../../../interfaces/ILocation';
import { ClinicDto } from '../../clinics/dto/clinic.dto';
import { DoctorDto } from '../../doctors/dto/doctor.dto';
import { PatientDto } from '../../patients/dto/patient.dto';
import { Transaction } from '../../payments/entities/transaction.entity';
import { ServiceProviderDto } from '../../service-providers/dto/service-provider.dto';
// import { SpecialityDto } from '../../specialities/dto/speciality.dto';
import { UserDto } from '../../users/dto/user.dto';
import { ConsultationRequestHistoryDto } from './consultation-request-history.dto';

export class ConsultationRequestDto extends AbstractDto {
    @IsEnum(ConsultationRequestStatus)
    @IsOptional()
    @Expose()
    status?: ConsultationRequestStatus;

    @IsEnum(ConsultationType)
    @IsOptional()
    @Expose()
    type?: ConsultationType;

    @IsBoolean()
    @Expose()
    forOtherPatient: boolean;

    @Expose()
    @IsBoolean()
    @IsOptional()
    isFreeRequest?: boolean;

    @IsString()
    @IsOptional()
    @Expose()
    otherPatientName?: string;

    @IsString()
    @IsOptional()
    @Expose()
    otherPatientNumber?: string;

    @IsDate()
    @IsOptional()
    @Expose()
    otherPatientDateOfBirth?: Date;

    @IsEnum(GenderType)
    @IsOptional()
    @Expose()
    otherPatientGender?: GenderType;

    @IsString()
    @IsOptional()
    @Expose()
    patientNotes?: string;

    @IsDateString()
    @IsOptional()
    @Expose()
    date?: Date;

    @IsString()
    @IsOptional()
    @Expose()
    from?: string;

    @IsNumber()
    @IsOptional()
    @Expose()
    duration?: number;

    @IsString()
    @IsOptional()
    @Expose()
    diagnosis?: string;

    @IsString()
    @IsOptional()
    @Expose()
    patientAddress?: string;

    @IsLatLong()
    @IsOptional()
    @Expose()
    patientLocation?: ILocation;

    @IsString()
    @IsOptional()
    @Expose()
    patientLocationAddress?: string;

    @IsString()
    @IsOptional()
    @Expose()
    prescription?: string;

    @IsString()
    @IsOptional()
    @Expose()
    doctorNotes?: string;

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    doctorNotesImages?: string[];

    @IsString()
    @IsOptional()
    @ApiPropertyOptional()
    @Expose()
    code?: string;

    @IsString()
    @IsOptional()
    @ApiPropertyOptional()
    @Expose()
    promoCode?: string;

    @IsString()
    @IsOptional()
    @Expose()
    agoraToken?: string;

    @IsDate()
    @IsOptional()
    @Expose()
    expiredAt?: Date;

    @IsDate()
    @IsOptional()
    @Expose()
    canceledAt?: Date;

    @IsDate()
    @IsOptional()
    @Expose()
    acceptedAt?: Date;

    @IsDate()
    @IsOptional()
    @Expose()
    startedAt?: Date;

    @IsDate()
    @IsOptional()
    @Expose()
    finishedAt?: Date;

    @IsBoolean()
    @IsOptional()
    @Expose()
    isFeeCollected?: boolean;

    @IsBoolean()
    @IsOptional()
    @Expose()
    isKashfFeePaid?: boolean;

    @IsBoolean()
    @IsOptional()
    @Expose()
    isRated?: boolean;

    @IsNumber()
    @IsOptional()
    @Expose()
    fees?: number;

    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @Expose()
    kashfPercentage?: number;

    @IsNumber()
    @IsOptional()
    @Expose()
    discountAmount?: number;

    @IsEnum(PaymentMethod)
    @IsOptional()
    @Expose()
    paymentMethod?: PaymentMethod;

    @IsEnum(PaymentStatus)
    @IsOptional()
    @Expose()
    paymentStatus?: PaymentStatus;

    @ValidateNested()
    @Type(() => DoctorDto)
    @IsOptional()
    @Expose()
    doctor?: DoctorDto;

    @ValidateNested()
    @Type(() => ServiceProviderDto)
    @IsOptional()
    @Expose()
    serviceProvider?: ServiceProviderDto;

    @IsNumber()
    @IsOptional()
    @Expose()
    doctorId?: number;

    @ValidateNested()
    @Type(() => PatientDto)
    @IsOptional()
    @Expose()
    patient?: PatientDto;

    @IsNumber()
    @IsOptional()
    @Expose()
    patientId?: number;

    @Type(() => ClinicDto)
    @IsOptional()
    @Expose()
    clinic?: ClinicDto;

    @IsNumber()
    @IsOptional()
    @Expose()
    clinicId?: number;

    // @Type(() => SpecialityDto)
    // @IsOptional()
    // @Expose()
    // choosenSpeciality?: SpecialityDto;

    @IsNumber()
    @Expose()
    @IsOptional()
    choosenSpecialityId?: number;

    @Type(() => ConsultationRequestHistoryDto)
    @IsOptional()
    @Expose()
    history?: ConsultationRequestHistoryDto;

    @ValidateNested()
    @Type(() => Transaction)
    @IsOptional()
    @Expose()
    transaction?: Transaction;

    @Type(() => UserDto)
    @IsOptional()
    @Expose()
    createdBy?: UserDto;
}
