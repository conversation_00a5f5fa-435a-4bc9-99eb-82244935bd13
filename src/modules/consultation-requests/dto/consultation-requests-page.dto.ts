import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { ConsultationRequestDto } from './consultation-request.dto';

export class ConsultationRequestsPageDto {
    @ApiProperty({
        type: ConsultationRequestDto,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => ConsultationRequestDto)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: ConsultationRequestDto[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: ConsultationRequestDto[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
