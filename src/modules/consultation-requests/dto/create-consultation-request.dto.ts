'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsBoolean,
    IsEnum,
    IsISO8601,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsPhoneNumber,
    IsString,
    ValidateIf,
} from 'class-validator';

import {
    ConsultationType,
    GenderType,
    PaymentMethod,
} from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ILocation } from '../../../interfaces/ILocation';

export class CreateConsultationRequestDto extends AbstractDto {
    @ApiProperty({ enum: ConsultationType })
    @Expose()
    @IsEnum(ConsultationType)
    type: ConsultationType;

    @ApiProperty()
    @Expose()
    @IsBoolean()
    forOtherPatient: boolean;

    @Expose()
    @IsBoolean()
    @IsOptional()
    isFreeRequest?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @ValidateIf((dto) => dto.forOtherPatient === true)
    @IsString()
    @IsNotEmpty()
    otherPatientName?: string;

    @ApiPropertyOptional()
    @Expose()
    @ValidateIf((dto) => dto.forOtherPatient === true)
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    otherPatientNumber?: string;

    @ApiPropertyOptional({ example: 'YYYY-MM-DD' })
    @Expose()
    @ValidateIf((dto) => dto.forOtherPatient === true)
    @IsISO8601()
    otherPatientDateOfBirth?: Date;

    @ApiPropertyOptional({ enum: GenderType })
    @Expose()
    @ValidateIf((dto) => dto.forOtherPatient === true)
    @IsEnum(GenderType)
    otherPatientGender?: GenderType;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    patientNotes?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    from?: string;

    @ApiProperty({ example: 'YYYY-MM-DD' })
    @Expose()
    @IsISO8601()
    date: Date;

    @ApiPropertyOptional()
    @Expose()
    @ValidateIf((dto) => dto.type === ConsultationType.ONLINE_CONSULTATION)
    @IsNumber()
    duration?: number;

    @ApiPropertyOptional()
    @Expose()
    @ValidateIf((dto) => dto.type === ConsultationType.HOME_VISIT)
    @IsString()
    @IsNotEmpty()
    patientAddress?: string;

    @ApiPropertyOptional()
    @Expose()
    @ValidateIf((dto) => dto.type === ConsultationType.HOME_VISIT)
    patientLocation?: ILocation;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    patientLocationAddress?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    code?: string;

    @ApiProperty()
    @Expose()
    @IsNumber()
    fees: number;

    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @Expose()
    kashfPercentage?: number;

    @ApiProperty()
    @Expose()
    @IsNumber()
    doctorId: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsNumber()
    serviceProviderId?: number;

    @ApiProperty()
    @Expose()
    @IsNumber()
    patientId: number;

    @ApiPropertyOptional()
    @Expose()
    @ValidateIf(
        (dto: CreateConsultationRequestDto) =>
            dto.type === ConsultationType.CLINIC && !dto.serviceProviderId,
    )
    @IsNumber()
    clinicId?: number;

    @IsNumber()
    @Expose()
    @IsOptional()
    choosenSpecialityId?: number;

    @ApiPropertyOptional({ enum: PaymentMethod })
    @Expose()
    @IsOptional()
    @IsEnum(PaymentMethod)
    paymentMethod?: PaymentMethod;
}
