import { Expose } from 'class-transformer';
import { IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class DownloadConsultationRequestDto extends AbstractDto {
    @IsString()
    @Expose()
    consultationType: string;

    @IsString()
    @Expose()
    date: string;

    @IsString()
    @Expose()
    time: string;

    @IsString()
    @Expose()
    status: string;

    @IsString()
    @Expose()
    doctorName: string;

    @IsString()
    @Expose()
    doctorMobileNumber: string;

    @IsString()
    @Expose()
    patientName: string;

    @IsString()
    @Expose()
    patientMobileNumber: string;
}
