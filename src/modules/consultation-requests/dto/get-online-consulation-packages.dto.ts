'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class GetOnlineConsulationPackages extends AbstractDto {
    @ApiProperty()
    @IsString()
    @Expose()
    duration: string;

    @ApiProperty()
    @IsString()
    @Expose()
    value: number;

    @ApiProperty()
    @IsNumber()
    @Expose()
    fees?: number;
}
