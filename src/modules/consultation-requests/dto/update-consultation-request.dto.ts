'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsEnum,
    IsISO8601,
    IsN<PERSON>ber,
    IsOptional,
    IsString,
} from 'class-validator';

import { ConsultationRequestStatus } from '../../../common/constants/status';
import { CancelledByType, PaymentMethod } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

export class UpdateConsultationRequestDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsISO8601()
    @IsOptional()
    date?: Date;

    acceptedAt?: Date;

    startedAt?: Date;

    finishedAt?: Date;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    from?: Date;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    doctorNotes?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    doctorNotesImages?: string[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    prescription?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    diagnosis?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isFeeCollected?: boolean;

    @ApiPropertyOptional({ enum: ConsultationRequestStatus })
    @Expose()
    @IsOptional()
    @IsEnum(ConsultationRequestStatus)
    status?: ConsultationRequestStatus;

    @ApiPropertyOptional({ enum: PaymentMethod })
    @Expose()
    @IsOptional()
    @IsEnum(PaymentMethod)
    paymentMethod?: PaymentMethod;

    @IsOptional()
    isVerified?: boolean;

    @IsNumber()
    @Expose()
    @IsOptional()
    choosenSpecialityId?: number;

    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @Expose()
    kashfPercentage?: number;

    canceledAt?: Date;

    @ApiPropertyOptional({ enum: CancelledByType })
    @Expose()
    @IsOptional()
    @IsEnum(CancelledByType)
    cancelledBy?: CancelledByType;
}
