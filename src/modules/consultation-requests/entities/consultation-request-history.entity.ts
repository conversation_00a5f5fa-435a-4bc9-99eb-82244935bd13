import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ConsultationRequestStatus } from '../../../common/constants/status';
import { RequestUpdateType } from '../../../common/constants/types';
import { UserEntity } from '../../../modules/users/entities/user.entity';
import { ConsultationRequestEntity } from './consultation-request.entity';

@Entity('consultation_request_history')
export class ConsultationRequestHistoryEntity extends AbstractEntity {
    @Column({
        type: 'enum',
        enum: ConsultationRequestStatus,
        default: ConsultationRequestStatus.REQUESTED,
    })
    @Expose()
    status: ConsultationRequestStatus;

    @Column({ type: 'enum', enum: RequestUpdateType })
    @Expose()
    updateType: RequestUpdateType;

    @ManyToOne(
        (_type) => ConsultationRequestEntity,
        (consultationRequest) => consultationRequest.history,
    )
    @Expose()
    consultationRequest: ConsultationRequestEntity;

    @ManyToOne((_type) => UserEntity)
    @Expose()
    createdBy: UserEntity;
}
