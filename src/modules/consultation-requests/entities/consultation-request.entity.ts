'use strict';
import { Expose, Type } from 'class-transformer';
import { IsObject, IsOptional } from 'class-validator';
import {
    Column,
    DeleteDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    OneToMany,
    OneToOne,
} from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import {
    ConsultationRequestStatus,
    PaymentStatus,
} from '../../../common/constants/status';
import {
    CancelledByType,
    ConsultationType,
    GenderType,
    PaymentMethod,
} from '../../../common/constants/types';
import { IGeometry } from '../../../interfaces/ILocation';
import { ColumnNumericTransformer } from '../../../shared/services/column.numeric.transform';
import { Clinic } from '../../clinics/entities/clinic.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { Transaction } from '../../payments/entities/transaction.entity';
import { Rating } from '../../ratings/entities/rating.entity';
import { ReminderEntity } from '../../reminders/entities/reminder.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { Speciality } from '../../specialities/entities/speciality.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { ConsultationRequestHistoryEntity } from './consultation-request-history.entity';
import { Complaint } from '../../complaints/entities/complaint.entity';
@Entity('consultation_requests')
export class ConsultationRequestEntity extends AbstractEntity {
    @Column({
        type: 'enum',
        enum: ConsultationRequestStatus,
        default: ConsultationRequestStatus.REQUESTED,
    })
    @Expose()
    status: ConsultationRequestStatus;

    @Column({ type: 'enum', enum: ConsultationType })
    @Expose()
    type: ConsultationType;

    @Column({ nullable: true })
    @Expose()
    otherPatientName: string;

    @Column({ nullable: true })
    @Expose()
    otherPatientNumber: string;

    @Column({ type: 'date', nullable: true })
    @Expose()
    otherPatientDateOfBirth: Date;

    @Column({ nullable: true, type: 'enum', enum: GenderType })
    @Expose()
    otherPatientGender: GenderType;

    @Column({ nullable: true, type: 'enum', enum: CancelledByType })
    @Expose()
    cancelledBy: CancelledByType;

    @Column({ nullable: true })
    @Expose()
    patientNotes: string;

    @Column({ type: 'date' })
    @Index()
    @Expose()
    date: Date;

    @Column({ type: 'time', nullable: true })
    @Expose()
    from: Date;

    @Column({ nullable: true })
    @Expose()
    duration: number;

    @Column({ nullable: true })
    @Expose()
    diagnosis: string;

    @Column({ nullable: true })
    @Expose()
    patientAddress: string;

    @Column({
        name: 'location',
        type: 'geometry',
        nullable: true,
        spatialFeatureType: 'Point',
        srid: 4326,
    })
    @Expose()
    patientLocation: IGeometry;

    @Column({ nullable: true })
    @Expose()
    patientLocationAddress: string;

    @Column({ nullable: true })
    @Expose()
    prescription: string;

    @Column({ nullable: true })
    @Expose()
    doctorNotes: string;

    @Column('simple-array', { nullable: true })
    @Expose()
    doctorNotesImages: string[];

    @Column({ nullable: true })
    @Expose()
    agoraToken: string;

    @Column({ nullable: true })
    @Expose()
    code: string;

    @Column({ nullable: true })
    @Expose()
    promoCode: string;

    @Column({ type: 'timestamp without time zone', nullable: true })
    @Expose()
    expiredAt?: Date;

    @DeleteDateColumn({
        type: 'timestamp without time zone',
        name: 'deleted_at',
    })
    deletedAt?: Date;

    @Column({ type: 'timestamp without time zone', nullable: true })
    @Expose()
    canceledAt?: Date;

    @Column({ type: 'timestamp without time zone', nullable: true })
    @Expose()
    acceptedAt?: Date;

    @Column({ type: 'timestamp without time zone', nullable: true })
    @Expose()
    startedAt?: Date;

    @Column({ type: 'timestamp without time zone', nullable: true })
    @Expose()
    finishedAt?: Date;

    @Column({ nullable: true })
    @Expose()
    choosenSpecialityId?: number;

    @Column({ default: false })
    @Expose()
    isFeeCollected: boolean;

    @Column({ default: false })
    @Expose()
    isKashfFeePaid: boolean;

    @Column({ default: false })
    @Expose()
    isVerified: boolean;

    @Column({ default: false })
    @Expose()
    isFreeRequest: boolean;

    @Column({ default: false })
    @Expose()
    forOtherPatient: boolean;

    @Column({
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    fees: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    kashfPercentage: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    discountAmount: number;

    @Column({ type: 'enum', enum: PaymentMethod, nullable: true })
    @Expose()
    paymentMethod: PaymentMethod;

    @Column({
        type: 'enum',
        enum: PaymentStatus,
        default: PaymentStatus.PENDING,
    })
    @Expose()
    paymentStatus: PaymentStatus;

    @ManyToOne(
        (_type) => DoctorEntity,
        (doctor) => doctor.consultationRequests,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    doctor: DoctorEntity;

    @OneToMany(() => ReminderEntity, (reminder) => reminder.request, {
        onDelete: 'CASCADE',
    })
    @Expose()
    reminders: ReminderEntity[];

    @ManyToOne((_type) => Patient, (patient) => patient.consultationRequests, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    patient: Patient;

    @ManyToOne((_type) => Clinic, (clinic) => clinic.consultationRequests, {
        onDelete: 'CASCADE',
    })
    clinic: Clinic;

    @OneToMany(
        (_type) => ConsultationRequestHistoryEntity,
        (history) => history.consultationRequest,
    )
    @Expose()
    history: ConsultationRequestHistoryEntity;

    @OneToOne((_type) => Transaction, (transaction) => transaction.request, {
        onDelete: 'SET NULL',
    })
    @JoinColumn()
    @Expose()
    transaction: Transaction;

    @OneToMany(() => Rating, (rating) => rating.consultationRequest, {
        onDelete: 'CASCADE',
    })
    @Expose()
    ratings: Rating[];

    @ManyToOne((_type) => UserEntity)
    @Expose()
    createdBy: UserEntity;

    @Expose()
    @IsObject()
    @IsOptional()
    @Type(() => ServiceProvider)
    @ManyToOne(
        (_type) => ServiceProvider,
        (serviceProvider) => serviceProvider.consultationRequests,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    serviceProvider?: ServiceProvider;

    @IsOptional()
    @ManyToOne(
        (_type) => Speciality,
        (speciality) => speciality.consultationRequests,
        {
            onDelete: 'SET NULL',
        },
    )
    choosenSpeciality?: Speciality;

    @OneToMany(() => Complaint, (complaint) => complaint.request)
    complaints: Complaint[];
}
