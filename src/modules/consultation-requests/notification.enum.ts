export enum ConsultationRequestMessagesKeys {
    CREATED_SUCCESSFULLY = 'request.CREATED_SUCCESSFULLY',
    UPDATED_SUCCESSFULLY = 'request.UPDATED_SUCCESSFULLY',
    REQUEST_NOT_FOUND = 'request.REQUEST_NOT_FOUND',
    WRONG_REQUEST_CODE = 'request.WRONG_REQUEST_CODE',
    CORRECT_REQUEST_CODE = 'request.CORRECT_REQUEST_CODE',
    UPDATE_FAILED = 'request.UPDATE_FAILED',
}

export enum NotificationTyps {
    CALL_READY = 'call_ready',
    REQUEST_CREATED = 'request_created',
    REQUEST_STARTED = 'request_started',
    REQUEST_FINISHED = 'request_finished',
    REQUEST_CANCELED = 'request_canceled',
    REQUEST_TIME_CHANGED = 'request_time_changed',
    REQUEST_CODE = 'request_code',
    PROCEED_TO_PAY = 'proceed_to_pay',
    FREE_REQUEST_ACCEPTED = 'free_request_accepted',
    VERIFY_SCHEDULE = 'verify_schedule',
    VERIFY_SERVICE_PROVIDER = 'verify_service_provider',
    NOTIFIY_DOCTOR_AFTER_PAID = 'notify_docotr_after_paid',
    NOTIFY_DOCTOR_KASHF_PAYMENT_UNSETTLED = 'notify_doctor_kashf_payment_unsettled',
    NOTIFIY_DOCTOR_AFTER_REJECT_UPDATE_SYNDICATE = 'notify_docotr_after_reject_update_syndicate',
    NOT_PAIED_EXPIRED_REQUEST = 'not_paied_expired_request',
    DOCTOR_WENT_OFFLINE = 'doctor_went_offline',
    DOCTOR_BACK_ONLINE = 'doctor_back_online',
    NOTIFY_PATIENT_REMINDER = 'notify_patient_reminder',
    BOOKING_DATE_CHANGE = 'booking_date_change',
    PATIENT_ADD_NEW_BOOKING = 'patient_add_new_booking',
    BOOKING_CONFIRMED = 'booking_confirmed',
    BOOKING_CANCELED = 'booking_canceled',
    BOOKING_REMOVED = 'booking_removed',
    BOOKING_COMPLETED = 'booking_completed',
}
