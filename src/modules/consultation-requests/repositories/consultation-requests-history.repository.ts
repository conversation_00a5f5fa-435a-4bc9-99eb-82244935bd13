import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { ConsultationRequestHistoryEntity } from '../entities/consultation-request-history.entity';
@Injectable()
export class ConsultationRequestsHistoryRepository extends Repository<ConsultationRequestHistoryEntity> {
    constructor(dataSource: DataSource) {
        super(
            ConsultationRequestHistoryEntity,
            dataSource.createEntityManager(),
        );
    }
}
