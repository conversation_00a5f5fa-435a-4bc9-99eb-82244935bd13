import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { ConsultationRequestStatus } from '../../../common/constants/status';
import { ConsultationRequestEntity } from '../entities/consultation-request.entity';

@Injectable()
export class ConsultationRequestsRepository extends Repository<ConsultationRequestEntity> {
    constructor(dataSource: DataSource) {
        super(ConsultationRequestEntity, dataSource.createEntityManager());
    }

    getDoctorConsultationsByDate(
        doctorId: number,
        date: Date,
    ): Promise<ConsultationRequestEntity[]> {
        return this.createQueryBuilder('consultationRequest')
            .leftJoinAndSelect('consultationRequest.doctor', 'doctor')
            .select([
                'consultationRequest.id',
                'consultationRequest.date',
                'consultationRequest.from',
                'consultationRequest.status',
                'doctor.id',
            ])
            .where(
                'doctor.id = :doctorId AND date = :date AND consultationRequest.status NOT IN (:...status)',
                {
                    doctorId,
                    date,
                    status: [
                        ConsultationRequestStatus.CANCELLED,
                        ConsultationRequestStatus.EXPIRED,
                        ConsultationRequestStatus.FINISHED,
                    ],
                },
            )
            .getMany();
    }
}
