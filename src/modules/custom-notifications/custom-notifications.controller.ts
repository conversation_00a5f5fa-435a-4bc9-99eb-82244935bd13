'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CustomNotificationsService } from './custom-notifications.service';
import { CreateCustomNotificationDto } from './dto/create-custom-notification.dto';
import { CustomNotificationDto } from './dto/custom-notification.dto';

@Controller('custom-notifications')
@ApiTags('custom-notifications')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class CustomNotificationsController {
    constructor(
        private customNotificationsService: CustomNotificationsService,
    ) {}

    @Get()
    @Roles(RoleType.SUPER_ADMIN, RoleType.ADMIN)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get all custom notifications',
        type: [CustomNotificationDto],
    })
    getAllCustomNotifications(): Promise<CustomNotificationDto[]> {
        return this.customNotificationsService.getAllCustomNotifications();
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN, RoleType.ADMIN)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        description: 'Create custom notification',
        type: CreateOperationsResponse,
    })
    createCustomNotification(
        @Body() createCustomNotificationDto: CreateCustomNotificationDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.customNotificationsService.createCustomNotification(
            createCustomNotificationDto,
            lang,
        );
    }

    @Post('/send/:id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN, RoleType.ADMIN)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        description: 'send custom notification',
        type: CreateOperationsResponse,
    })
    sendCustomNotification(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<void> {
        return this.customNotificationsService.sendCustomNotifiction(id, lang);
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN, RoleType.ADMIN)
    @ApiResponse({
        description: 'Delete custom notification',
        type: BasicOperationsResponse,
    })
    async deleteCustomNotification(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.customNotificationsService.deleteCustomNotification(
            id,
            lang,
        );
    }
}
