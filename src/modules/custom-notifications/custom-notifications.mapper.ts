import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreateCustomNotificationDto } from './dto/create-custom-notification.dto';
import { CustomNotificationDto } from './dto/custom-notification.dto';
import { CustomNotification } from './entities/custom-notification.entity';

type CustomNotificationDTOs =
    | CustomNotificationDto
    | CreateCustomNotificationDto;
@Injectable()
export class CustomNotificationsMapper extends AbstractMapper<
    CreateCustomNotificationDto,
    CustomNotification
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<CustomNotification>,
        sourceObject: CustomNotificationDTOs,
    ): CustomNotification {
        const customNotificationEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(customNotificationEntity);
        return customNotificationEntity;
    }

    fromEntityToDTO(
        destination: ClassType<CustomNotificationDto>,
        sourceObject: CustomNotification,
    ): CustomNotificationDto {
        const customNotificationDto = super.fromEntityToDTO(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(customNotificationDto);
        return customNotificationDto;
    }
}
