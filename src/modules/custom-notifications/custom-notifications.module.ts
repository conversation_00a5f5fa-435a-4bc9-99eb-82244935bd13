import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { UsersModule } from '../../modules/users/users.module';
import { SharedModule } from '../../shared/shared.module';
import { CustomNotificationsController } from './custom-notifications.controller';
import { CustomNotificationsMapper } from './custom-notifications.mapper';
import { CustomNotificationsService } from './custom-notifications.service';
import { CustomNotificationsRepository } from './repositories/custom-notifications.repository';

@Module({
    imports: [
        SharedModule,
        UsersModule,
    ],
    controllers: [CustomNotificationsController],
    exports: [CustomNotificationsService],
    providers: [
        CustomNotificationsService,
        CustomNotificationsMapper,
        CustomNotificationsRepository,
    ],
})
export class CustomNotificationsModule {}
