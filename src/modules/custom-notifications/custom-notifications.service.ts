import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { FirebaseTopics, Languages } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { ServiceProvider } from '../../modules/service-providers/entities/service-provider.entity';
import { FCMService } from '../../shared/services/fcm.service';
import { UsersService } from './../../modules/users/users.service';
import { CustomNotificationsMapper } from './custom-notifications.mapper';
import { CreateCustomNotificationDto } from './dto/create-custom-notification.dto';
import { CustomNotificationDto } from './dto/custom-notification.dto';
import { CustomNotification } from './entities/custom-notification.entity';
import { CustomNotificationsRepository } from './repositories/custom-notifications.repository';
import { CustomNotificationMessagesKeys } from './translate.enum';

@Injectable()
export class CustomNotificationsService {
    constructor(
        private readonly customNotificationsRepository: CustomNotificationsRepository,
        private readonly customNotificationsMapper: CustomNotificationsMapper,
        private readonly usersService: UsersService,
        private readonly fcmService: FCMService,
        private readonly i18n: I18nService,
    ) { }

    async getCustomNotification(
        id: number,
        lang: string,
    ): Promise<CustomNotificationDto> {
        const customNotification =
            await this.customNotificationsRepository.findOne({
                where: { id },
            });
        if (!customNotification) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        CustomNotificationMessagesKeys.CUSTOM_NOTIFICATION_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.customNotificationsMapper.fromEntityToDTO(
            CustomNotificationDto,
            customNotification,
        );
    }

    async sendCustomNotifiction(id: number, lang: string): Promise<void> {
        const { arabicText, audience, englishText, serviceProvider } =
            await this.getCustomNotification(id, lang);
        if (audience === FirebaseTopics.SERVICE_PROVIDERS) {
            const body = lang === Languages.AR ? arabicText : englishText;
            const fcmTokens = await this.usersService.getFcmTokenByUserId(
                serviceProvider.id,
            );
            await this.fcmService.sendNotification('', body, {}, fcmTokens);
        } else {
            const fcmTokens =
                audience === FirebaseTopics.ALL
                    ? await this.usersService.getUsersFcmTokens()
                    : await this.usersService.getUsersFcmTokens(audience);
            await this.fcmService.sendNotification(
                '',
                arabicText,
                {},
                fcmTokens.ar,
            );
            await this.fcmService.sendNotification(
                '',
                englishText,
                {},
                fcmTokens.en,
            );
        }
    }

    async getAllCustomNotifications(): Promise<CustomNotificationDto[]> {
        const customNotificationsEntities =
            await this.customNotificationsRepository
                .createQueryBuilder('custom-notifications')
                .orderBy('id', 'DESC')
                .getMany();
        return customNotificationsEntities.map((customNotificationEntity) =>
            this.customNotificationsMapper.fromEntityToDTO(
                CustomNotificationDto,
                customNotificationEntity,
            ),
        );
    }

    async createCustomNotification(
        createCustomNotificationDto: CreateCustomNotificationDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const customNotificationEntity =
            this.customNotificationsMapper.fromDTOToEntity(
                CustomNotification,
                createCustomNotificationDto,
            );
        const customNotification = this.customNotificationsRepository.create(
            customNotificationEntity,
        );
        if (createCustomNotificationDto.serviceProviderId) {
            customNotification.serviceProvider = new ServiceProvider();
            customNotification.serviceProvider.id =
                createCustomNotificationDto.serviceProviderId;
        }
        const createdCustomNotification =
            await this.customNotificationsRepository.save(customNotification);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                CustomNotificationMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdCustomNotification.id,
        };
    }

    async deleteCustomNotification(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const customNotification =
            await this.customNotificationsRepository.findOne({
                where: { id },
            });

        if (!customNotification) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        CustomNotificationMessagesKeys.CUSTOM_NOTIFICATION_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.customNotificationsRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                CustomNotificationMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }
}
