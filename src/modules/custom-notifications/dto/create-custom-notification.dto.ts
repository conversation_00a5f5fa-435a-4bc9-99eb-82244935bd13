'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { FirebaseTopics } from './../../../common/constants/types';

export class CreateCustomNotificationDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsString()
    arabicText: string;

    @ApiProperty()
    @Expose()
    @IsString()
    englishText: string;

    @ApiProperty()
    @Expose()
    @IsString()
    audience: FirebaseTopics;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsNumber()
    serviceProviderId?: number;
}
