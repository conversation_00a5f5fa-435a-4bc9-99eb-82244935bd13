'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ServiceProviderDto } from '../../../modules/service-providers/dto/service-provider.dto';
import { FirebaseTopics } from './../../../common/constants/types';

export class CustomNotificationDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsString()
    arabicText: string;

    @ApiProperty()
    @Expose()
    @IsString()
    englishText: string;

    @ApiProperty()
    @Expose()
    @IsString()
    audience: FirebaseTopics;

    @ValidateNested()
    @Type(() => ServiceProviderDto)
    @IsOptional()
    @Expose()
    serviceProvider?: ServiceProviderDto;

    @ValidateNested()
    @IsOptional()
    @Expose()
    createdAt?: Date;
}
