import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsObject, IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ServiceProvider } from '../../../modules/service-providers/entities/service-provider.entity';
import { FirebaseTopics } from './../../../common/constants/types';

@Entity('custom-notifications')
export class CustomNotification extends AbstractEntity {
    @ApiProperty()
    @Column()
    @Expose()
    @IsString()
    arabicText: string;

    @ApiProperty()
    @Column()
    @Expose()
    @IsString()
    englishText: string;

    @ApiProperty()
    @Column({ type: 'enum', enum: FirebaseTopics })
    @Expose()
    @IsString()
    audience: FirebaseTopics;

    @Expose()
    @IsObject()
    @IsOptional()
    @Type(() => ServiceProvider)
    @ManyToOne(
        (_type) => ServiceProvider,
        (serviceProvider) => serviceProvider.customNotifications,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    serviceProvider?: ServiceProvider;
}
