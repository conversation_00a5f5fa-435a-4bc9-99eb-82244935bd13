'use strict';

import {
    Body,
    Controller,
    Delete,
    ForbiddenException,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { EmailService } from '../../shared/services/email.service';
import { SimpleEmailBuilder } from '../../shared/utils/simple-email-builder';
import { GetRatingDto } from '../ratings/dto/get-rating.dto';
import { DoctorsService } from './doctors.service';
import { CreateDoctorRatingDto } from './dto/create-doctor-rating.dto';
import { CreateDoctorResponseDto } from './dto/create-doctor-response.dto';
import { CreateDoctorDto } from './dto/create-doctor.dto';
import { DoctorDayScheduleForPatientDto } from './dto/doctor-day-schedule-for-patient.dto';
import { DoctorForPatientDto } from './dto/doctor-for-patient.dto';
import { DoctorScheduleForPatientDto } from './dto/doctor-schedule-for-patient.dto';
import { DoctorDto } from './dto/doctor.dto';
import { DoctorsPageDto } from './dto/doctors-page.dto';
import { DownloadDoctorDto } from './dto/download-doctor.dto';
import { EasyKashCallbackDTO } from './dto/eazykash-callback.dto';
import { GetOnlineConsulationPackages } from './dto/get-online-packages.dto';
import { UpdateDoctorDto } from './dto/update-doctor.dto';
import { UpdateSyndicateIdStatusDto } from './dto/update-syndicate-id-url-doctor.dto';

@Controller('doctors')
@ApiTags('doctors')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(HeaderInterceptor)
@ApiBearerAuth()
export class DoctorsController {
    constructor(
        private doctorsService: DoctorsService,
        private emailService: EmailService,
    ) {}

    @Get('languages')
    getDoctorSpeekingLanguages(
        @I18nLang() lang: string,
    ): { label: string; value: string }[] {
        return this.doctorsService.getDoctorSpeekingLanguages(lang);
    }

    /*********************** CRUD Operations *****************************/

    @Get('/download')
    @ApiResponse({
        description: 'Download doctors',
        type: [DownloadDoctorDto],
    })
    async downloadDoctors(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
        @Query('lang') lang: string,
    ): Promise<void> {
        const data = await this.doctorsService.downloadDoctors(
            providerId,
            query,
            lang,
        );
        res.setHeader(
            'Content-disposition',
            'attachment; filename=doctors.csv',
        );
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get()
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get Doctors',
        type: [DoctorForPatientDto],
    })
    getDoctors(
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<DoctorForPatientDto[] | DoctorsPageDto> {
        switch (req.user.role) {
            case RoleType.ADMIN:
            case RoleType.SUPER_ADMIN:
                return this.doctorsService.getDoctorsForAdmin(
                    query,
                    pageOptionsDto,
                    req.user,
                    lang,
                );
            case RoleType.SERVICE_PROVIDER:
                return this.doctorsService.getDoctorsForServiceProvider(
                    query,
                    pageOptionsDto,
                    req.user,
                    lang,
                );
            default:
                return this.doctorsService.getDoctorsForPatient(
                    query,
                    req.user,
                    lang,
                );
        }
    }

    @Get(':id')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'View Doctor',
        type: DoctorDto,
    })
    getDoctor(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<DoctorDto> {
        switch (req.user.role) {
            case RoleType.DOCTOR:
                return this.doctorsService.getDoctorProfile(id, lang);
            case RoleType.ADMIN:
            case RoleType.SUPER_ADMIN:
                return this.doctorsService.getDoctorForAdmin(id, lang);
            case RoleType.SERVICE_PROVIDER:
                return this.doctorsService.getDoctorForServiceProvider(
                    id,
                    req.user,
                    lang,
                );
            default:
                return this.doctorsService.getDoctorForPatient(
                    id,
                    req.user,
                    lang,
                );
        }
    }

    @Post()
    create(
        @Body() doctorDto: CreateDoctorDto,
        @I18nLang() lang: string,
    ): Promise<CreateDoctorResponseDto> {
        return this.doctorsService.createDoctor(doctorDto, lang);
    }

    @Put(':id')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.DOCTOR, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update Doctor',
        type: BasicOperationsResponse,
    })
    update(
        @Req() req: Request,
        @Param('id') id: number,
        @Body() updateDoctorDto: UpdateDoctorDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        if (req.user.role === RoleType.DOCTOR && id !== req.user.doctor.id) {
            throw new ForbiddenException();
        }
        if (
            req.user.role === RoleType.DOCTOR &&
            (updateDoctorDto.isActive === true ||
                updateDoctorDto.kashfPercentage)
        ) {
            throw new ForbiddenException();
        }
        return this.doctorsService.updateDoctor(
            id,
            updateDoctorDto,
            req.user,
            lang,
        );
    }

    @Put(':id/syndicate-id-url')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update syndicate_id status ',
        type: BasicOperationsResponse,
    })
    updateSyndicate(
        @Param('id') id: number,
        @Body() updateSyndicateIdStatusDto: UpdateSyndicateIdStatusDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.doctorsService.updateSyndicate(
            id,
            updateSyndicateIdStatusDto,
            lang,
        );
    }

    @Delete(':id')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.DOCTOR, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'delete Doctor make doctor isActive false',
        type: BasicOperationsResponse,
    })
    delete(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        if (req.user.role === RoleType.DOCTOR && id !== req.user.doctor.id) {
            throw new ForbiddenException();
        }
        return this.doctorsService.deactivateDoctor(id, lang);
    }

    /*********************** Rating *****************************/

    @Get(':id/ratings')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get doctor reviews',
        type: [GetRatingDto],
    })
    getDoctorRatings(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<GetRatingDto[]> {
        return this.doctorsService.getDoctorRatings(id, req.user, lang);
    }

    @Post('ratings')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT)
    @ApiResponse({
        description: 'Add doctor review',
        type: BasicOperationsResponse,
    })
    addDoctorRating(
        @Req() req: Request,
        @Body() createDoctorRatingDto: CreateDoctorRatingDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.doctorsService.addDoctorRatings(
            createDoctorRatingDto,
            req.user.id,
            lang,
        );
    }

    /*********************** Schedule *****************************/

    @Get(':id/schedules/:scheduleId')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get doctor schedule',
        type: [DoctorScheduleForPatientDto],
    })
    getDoctorSchedule(
        @Param('id') id: number,
        @Param('scheduleId') scheduleId: number,
        @I18nLang() lang: string,
    ): Promise<DoctorScheduleForPatientDto[]> {
        return this.doctorsService.getDoctorScheduleForPatient(
            id,
            scheduleId,
            lang,
        );
    }

    @Get(':id/schedules/:scheduleId/working-days/:dayId')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get doctor day schedule',
        type: DoctorDayScheduleForPatientDto,
    })
    getDoctorDaySchedule(
        @Query('query') query: string,
        @Param('id') id: number,
        @Param('scheduleId') scheduleId: number,
        @Param('dayId') dayId: number,
        @I18nLang() lang: string,
    ): Promise<DoctorDayScheduleForPatientDto> {
        return this.doctorsService.getDoctorDayScheduleForPatient(
            query,
            id,
            scheduleId,
            dayId,
            lang,
        );
    }

    /**
     * @deprecated since 10/2/2021
     */
    @Get(':id/schedule')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        type: [DoctorScheduleForPatientDto],
    })
    getDoctorSchedules(
        @Query('query') query: string,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<DoctorScheduleForPatientDto[]> {
        return this.doctorsService.getDoctorSchedulesForPatient(
            query,
            id,
            lang,
        );
    }

    /**
     * @deprecated since 10/2/2021
     */
    @Get(':id/day-schedule')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        type: DoctorDayScheduleForPatientDto,
    })
    getDoctorDaysSchedules(
        @Query('query') query: string,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<DoctorDayScheduleForPatientDto> {
        return this.doctorsService.getDoctorDaysSchedulesForPatient(
            query,
            id,
            lang,
        );
    }

    @Get(':id/online-packages')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get All online consultation packages',
        type: [GetOnlineConsulationPackages],
    })
    getDoctorOnlinePackages(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<GetOnlineConsulationPackages[]> {
        if (req.user.role === RoleType.DOCTOR && id !== req.user.doctor.id) {
            throw new ForbiddenException();
        }

        return this.doctorsService.getDoctorOnlinePackages(id, lang);
    }

    /***************************Service provider*********************************/

    @Get(':id/service-provider')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get doctor service providers',
        type: [DoctorScheduleForPatientDto],
    })
    getDoctorServiceProviders(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<any> {
        return this.doctorsService.getDoctorServiceProviders(id, lang);
    }

    @Get(':id/service-provider/:serviceProviderId/verify')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'verify doctor to service providers',
        type: [DoctorScheduleForPatientDto],
    })
    veirfyDoctorServiceProviders(
        @Param('id') id: number,
        @Param('serviceProviderId') serviceProviderId: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.doctorsService.veirfyDoctorServiceProviders(
            id,
            serviceProviderId,
            lang,
        );
    }

    @Get(':id/service-provider-signup-code/:signupCode/verify')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'verify doctor to service providers',
        type: [DoctorScheduleForPatientDto],
    })
    verifyDoctorServiceProvidersWithSignupCode(
        @Param('id') id: number,
        @Param('signupCode') signupCode: string,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.doctorsService.verifyDoctorServiceProvidersWithSignupCode(
            signupCode,
            id,
            lang,
        );
    }

    @Delete(':id/service-providers/:serviceProviderId')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'remove doctor from service providers',
    })
    deleteDoctorFromServiceProviders(
        @Param('id') id: number,
        @Param('serviceProviderId') serviceProviderId: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.doctorsService.removeDoctorFromServiceProvider(
            serviceProviderId,
            id,
            lang,
        );
    }

    @Put(':id/service-provider/:serviceProviderId/check-in')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'checkin/checkout doctor to service providers',
        type: [DoctorScheduleForPatientDto],
    })
    checkinDoctorServiceProviders(
        @Param('id') id: number,
        @Param('serviceProviderId') serviceProviderId: number,
        @Body() updateDoctorDto: UpdateDoctorDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.doctorsService.checkinDoctorServiceProviders(
            id,
            serviceProviderId,
            updateDoctorDto,
            lang,
        );
    }

    @Put(':id/service-provider/:serviceProviderId/online-status')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'online doctor on service providers',
        type: [DoctorScheduleForPatientDto],
    })
    updateDoctorAsOnlineOnServiceProviders(
        @Param('id') id: number,
        @Param('serviceProviderId') serviceProviderId: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.doctorsService.setDoctorAsOnlineOnServiceProviders(
            id,
            lang,
            serviceProviderId,
        );
    }

    @Post('eazy-kash-callback')
    async easyKashCallback(
        @Body() payload: EasyKashCallbackDTO,
    ): Promise<BasicOperationsResponse> {
        try {
            const doctor = await this.doctorsService.getDoctorByProductCode(
                payload.ProductCode,
            );
            if (!doctor) {
                return {
                    isSuccessful: false,
                    message: 'No doctor exists with that product code.',
                };
            }
            await this.emailService.sendEmail(
                doctor.user.email,
                'A patient booked a session with you',
                new SimpleEmailBuilder()
                    .addLine('Dear Doctor,')
                    .addBreak()
                    .addLine(`This is to notify you that ${payload.BuyerName}`)
                    .addLine(
                        `from Kashf 247 has paid ${payload.Amount}. Please proceed to provide the service.`,
                    )
                    .addBreak()
                    .addLine('Patient Details:')
                    .addLine(`Name: ${payload.BuyerName}`)
                    .addLine(`Email: ${payload.BuyerEmail}`)
                    .addLine(`Mobile: ${payload.BuyerMobile}`)
                    .addBreak(2)
                    .addLine('Thanks,')
                    .addBreak()
                    .addLine('Kashf 247 for Medical Services team').content,
            );
            return {
                isSuccessful: true,
                message: 'Email sent successfully',
            };
        } catch (error) {
            return {
                isSuccessful: false,
                message: 'Something went wrong',
            };
        }
    }
}
