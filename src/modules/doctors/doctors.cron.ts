import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { PsuedoLogger } from '../../common/PsuedoLogger';
import { DoctorsService } from './doctors.service';

@Injectable()
export class DoctorsCron {
    private readonly logger = new PsuedoLogger();
    constructor(private doctorsService: DoctorsService) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async updateAvailabilityDoctor(): Promise<void> {
        await this.doctorsService.updateTheAvailability();
    }
}
