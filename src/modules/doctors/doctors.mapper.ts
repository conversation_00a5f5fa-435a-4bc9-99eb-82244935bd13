import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType, plainToClass } from 'class-transformer';
import { get } from 'lodash';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { ClinicsMapper } from '../clinics/mappers/clinics.mapper';
import { Grade } from '../grades/entities/grade.entity';
import { Speciality } from '../specialities/entities/speciality.entity';
import { CreateDoctorDto } from './dto/create-doctor.dto';
import { DoctorDto } from './dto/doctor.dto';
import { DownloadDoctorDto } from './dto/download-doctor.dto';
import { UpdateDoctorDto } from './dto/update-doctor.dto';
import { DoctorEntity } from './entities/doctor.entity';
type DoctorDTOs = CreateDoctorDto | UpdateDoctorDto | DoctorDto;

@Injectable()
export class DoctorsMapper extends AbstractMapper<DoctorDTOs, DoctorEntity> {
    constructor(
        private readonly clinicsMapper: ClinicsMapper,
        private readonly helperService: HelperService,
    ) {
        super();
    }

    fromEntityToDTO(
        destination: ClassType<DoctorDto>,
        sourceObject: DoctorEntity,
    ): DoctorDto {
        const doctorDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(doctorDto);
        return doctorDto;
    }

    fromDTOToEntity(
        destination: ClassType<DoctorEntity>,
        sourceObject: DoctorDTOs,
    ): DoctorEntity {
        const doctorEntity = super.fromDTOToEntity(destination, sourceObject);
        if (
            sourceObject instanceof CreateDoctorDto ||
            sourceObject instanceof UpdateDoctorDto
        ) {
            if (sourceObject.gradeId) {
                doctorEntity.grade = new Grade();
                doctorEntity.grade.id = sourceObject.gradeId;
            }
            if (sourceObject.specialityIds) {
                doctorEntity.specialities = sourceObject.specialityIds.map(
                    (specialityId) => {
                        const speciality = new Speciality();
                        speciality.id = specialityId;
                        return speciality;
                    },
                );
            }
        }
        this.helperService.removeEmptyKeys(doctorEntity);
        return doctorEntity;
    }

    fromEntityToDownloadData(
        destination: ClassType<DownloadDoctorDto>,
        sourceObject: DoctorEntity,
    ): DownloadDoctorDto {
        const specialities = sourceObject.specialities.map((speciality) =>
            get(speciality, 'translations[0].title', ''),
        );
        const grade = get(sourceObject, 'grade.translations[0].title', '');
        return {
            grade,
            id: sourceObject.id,
            name: get(sourceObject, 'user.name', ''),
            syndicateId: sourceObject.syndicateNumber,
            specialities: specialities.join('-'),
            mobileNumber: get(sourceObject, 'user.phone', ''),
        };
    }

    fromEntityToDoctorDetailsDTO(
        destination: ClassType<DoctorDto>,
        sourceObject: DoctorEntity,
        lang: string,
    ): DoctorDto {
        const doctor = plainToClass(destination, sourceObject);

        doctor.specialities = doctor.specialities
            ? doctor.specialities.map((speciality) => ({
                title: speciality.translations[0]
                    ? speciality.translations[0].title
                    : '',
            }))
            : [];

        doctor.grade = doctor.grade
            ? {
                id: doctor.grade.id,
                title: doctor.grade.translations[0]
                    ? doctor.grade.translations[0].title
                    : '',
            }
            : {
                id: null,
                title: '',
            };
        if (sourceObject.clinics && sourceObject.clinics.length) {
            doctor.clinics = this.clinicsMapper.fromEntitiesToDTOs(
                sourceObject.clinics,
                lang,
            );
        }
        return doctor;
    }
}
