import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ConfigModule } from '../../config/config.module';
import { SharedModule } from '../../shared/shared.module';
import { AuthModule } from '../auth/auth.module';
import { ClinicsMapper } from '../clinics/mappers/clinics.mapper';
import { ConsultationRequestsModule } from '../consultation-requests/consultation-requests.module';
import { FavouriteRepository } from '../favourites/repositories/favourites.repository';
import { DoctorMembershipRepository } from '../memberships/repositories/doctor-membership.repository';
import { NotificationsModule } from '../notifications/notifications.module';
import { RatingsModule } from '../ratings/ratings.module';
import { SchedulesRepository } from '../schedules/repositories/schedules.repository';
import { SchedulesModule } from '../schedules/schedules.module';
import { ServiceProvidersRepository } from '../service-providers/repositories/service-providers.repository';
import { ServiceProvidersModule } from '../service-providers/service-providers.module';
import { SettingsModule } from '../settings/settings.module';

import { UsersModule } from '../users/users.module';
import { DoctorsController } from './doctors.controller';
import { DoctorsCron } from './doctors.cron';
import { DoctorsMapper } from './doctors.mapper';
import { DoctorsService } from './doctors.service';
import { DoctorEntity } from './entities/doctor.entity';
import { DoctorServiceProviderCheckInLogs } from './entities/doctor.service-provider.check-in.logs.entity';
import { DoctorsRepository } from './repositories/doctors.repository';
import { DoctorServiceProviderCheckInLogsRepository } from './repositories/doctors.service-provider.check-in.logs.repository';
import { UsersTranslationsRepository } from '../users/repositories/users-translations.repository';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            DoctorEntity,
            DoctorServiceProviderCheckInLogs,
        ]),
        SharedModule,
        AuthModule,
        UsersModule,
        ConfigModule,
        SettingsModule,
        forwardRef(() => ConsultationRequestsModule),
        forwardRef(() => SchedulesModule),
        forwardRef(() => ServiceProvidersModule),
        forwardRef(() => NotificationsModule),
        RatingsModule,
    ],
    controllers: [DoctorsController],
    exports: [DoctorsService],
    providers: [
        DoctorsService,
        DoctorsRepository,
        DoctorServiceProviderCheckInLogsRepository,
        SchedulesRepository,
        FavouriteRepository,
        ServiceProvidersRepository,
        DoctorMembershipRepository,
        UsersTranslationsRepository,
        DoctorsMapper,
        ClinicsMapper,
        DoctorsCron,
    ],
})
export class DoctorsModule {}
