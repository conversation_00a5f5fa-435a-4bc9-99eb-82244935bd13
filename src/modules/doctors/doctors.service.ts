/* eslint-disable @typescript-eslint/tslint/config */
/* eslint-disable complexity */
/* eslint-disable simple-import-sort/sort */
import {
    BadRequestException,
    forwardRef,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import { SettingsService } from '../settings/settings.service';
import * as moment from 'moment';
import { I18nService } from 'nestjs-i18n';
import { Brackets, Like, SelectQueryBuilder } from 'typeorm';

import { UserStatus } from '../../common/constants/status';
import {
    ConsultationType,
    DEACTIVATION_REASON,
    FilterByType,
    FilterType,
    Languages,
    PaymentMethod,
    RoleType,
    SortByType,
    SyndicateIdStatus,
} from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { ConfigService } from '../../config/config.service';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { DataDownloadService } from '../../shared/services/data-download.service';
import { EmailService } from '../../shared/services/email.service';
import { GeneratorService } from '../../shared/services/generator.service';
import { HelperService } from '../../shared/services/helper';
import { AuthService } from '../auth/auth.service';
import { AuthMessagesKeys } from '../auth/translate.enum';
import { ClinicDto } from '../clinics/dto/clinic.dto';
import { ClinicsMapper } from '../clinics/mappers/clinics.mapper';
import { ConsultationRequestsService } from '../consultation-requests/consultation-requests.service';
import { NotificationTyps } from '../consultation-requests/notification.enum';
import { FavouriteRepository } from '../favourites/repositories/favourites.repository';
import { CreateNotificationDto } from '../notifications/dto/create-notification.dto';
import { NotificationsService } from '../notifications/notifications.service';
import { GetRatingDto } from '../ratings/dto/get-rating.dto';
import { RatingsService } from '../ratings/ratings.service';
import { ScheduleDto } from '../schedules/dto/schedule.dto';
import { SchedulesRepository } from '../schedules/repositories/schedules.repository';
import { SchedulesService } from '../schedules/schedules.service';
import { ScheduleMessagesKeys } from '../schedules/translate.enum';
import { ServiceProvidersRepository } from '../service-providers/repositories/service-providers.repository';
import { ServiceProvidersService } from '../service-providers/service-providers.service';
import { ServiceProviderMessagesKeys } from '../service-providers/translate.enum';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { UserEntity } from '../users/entities/user.entity';
import { UsersMapper } from '../users/users.mapper';
import { UsersService } from '../users/users.service';
import { DoctorsMapper } from './doctors.mapper';
import { CreateDoctorRatingDto } from './dto/create-doctor-rating.dto';
import { CreateDoctorResponseDto } from './dto/create-doctor-response.dto';
import { CreateDoctorDto } from './dto/create-doctor.dto';
import {
    DoctorDayScheduleForPatientDto,
    DoctorScheduleSlotsDto,
} from './dto/doctor-day-schedule-for-patient.dto';
import { DoctorForPatientDto } from './dto/doctor-for-patient.dto';
import { DoctorScheduleForPatientDto } from './dto/doctor-schedule-for-patient.dto';
import { DoctorDto } from './dto/doctor.dto';
import { DoctorsPageDto } from './dto/doctors-page.dto';
import { DownloadDoctorDto } from './dto/download-doctor.dto';
import { GetOnlineConsulationPackages } from './dto/get-online-packages.dto';
import { UpdateDoctorDto } from './dto/update-doctor.dto';
import { UpdateSyndicateIdStatusDto } from './dto/update-syndicate-id-url-doctor.dto';
import { DoctorEntity } from './entities/doctor.entity';
import { DoctorServiceProviderCheckInLogs } from './entities/doctor.service-provider.check-in.logs.entity';
import { DoctorsRepository } from './repositories/doctors.repository';
import { DoctorServiceProviderCheckInLogsRepository } from './repositories/doctors.service-provider.check-in.logs.repository';
import { DoctorMessagesKeys } from './translate.enum';
import { DebugLogger } from '../../shared/services/logger.service';
import { DoctorMembership } from '../memberships/entities/doctor-membership.entity';
import { DoctorMembershipRepository } from '../memberships/repositories/doctor-membership.repository';
import { ServiceProvider } from '../service-providers/entities/service-provider.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { UserTranslation } from '../users/entities/user-translation.entity';
import { tryCatch } from '../../utils/helpers';
import { UsersTranslationsRepository } from '../users/repositories/users-translations.repository';

@Injectable()
export class DoctorsService {
    constructor(
        public readonly doctorsRepository: DoctorsRepository,
        public readonly doctorsMapper: DoctorsMapper,
        public readonly usersService: UsersService,
        public readonly ratingsService: RatingsService,
        public readonly usersMapper: UsersMapper,
        private readonly i18n: I18nService,
        private readonly authService: AuthService,
        private readonly schedulesService: SchedulesService,
        private readonly schedulesRepository: SchedulesRepository,
        private readonly favouriteRepository: FavouriteRepository,
        private readonly doctorServiceProviderCheckInLogsRepository: DoctorServiceProviderCheckInLogsRepository,
        public readonly helperService: HelperService,
        public readonly userTranslationRepository: UsersTranslationsRepository,
        private readonly emailService: EmailService,
        private readonly settingsService: SettingsService,
        private generatorService: GeneratorService,
        private configService: ConfigService,
        @Inject(forwardRef(() => ConsultationRequestsService))
        private consultationRequestsService: ConsultationRequestsService,
        private clinicsMapper: ClinicsMapper,
        private serviceProvidersRepository: ServiceProvidersRepository,
        @Inject(forwardRef(() => ServiceProvidersService))
        private serviceProvidersService: ServiceProvidersService,
        private readonly dataDownloadService: DataDownloadService,
        @Inject(forwardRef(() => NotificationsService))
        private notificationsService: NotificationsService,
        private logger: DebugLogger,
        private doctorMembershipRepository: DoctorMembershipRepository,
    ) {}

    async createDoctor(
        createDoctorDto: CreateDoctorDto,
        lang: string,
    ): Promise<CreateDoctorResponseDto> {
        await this.usersService.checkUserExistence(
            createDoctorDto,
            lang,
            RoleType.DOCTOR,
        );
        const userEntity = this.usersMapper.fromDTOToEntity(
            UserEntity,
            createDoctorDto,
        );
        const doctorEntity = this.doctorsMapper.fromDTOToEntity(
            DoctorEntity,
            createDoctorDto,
        );

        doctorEntity.kashfPercentage = this.settingsService.kashf_percentage;

        userEntity.role = RoleType.DOCTOR;

        doctorEntity.user = userEntity;
        const doctor = this.doctorsRepository.create(doctorEntity);
        const createdDoctor = await this.doctorsRepository.save(doctor);
        // let tokenPayload = new TokenPayloadDto();
        const tokenPayload = this.authService.generateTokens(
            createdDoctor.user.id,
            createdDoctor.user.role,
        );

        const code = this.generatorService
            .getCustomLengthRandomNumber(4)
            .toString();

        const header = await this.i18n.translate(
            DoctorMessagesKeys.EMAIL_DOCTOR_REGISTERED_HEADER,
            { lang },
        );
        const body = await this.i18n.translate(
            DoctorMessagesKeys.EMAIL_DOCTOR_REGISTERED_MESSAGE,
            { lang },
        );

        await Promise.all([
            this.usersService.updateRefreshToken(
                createdDoctor.user.id,
                tokenPayload.refreshToken,
            ),
            this.createScheduleForDoctor(
                createdDoctor.id,
                createDoctorDto,
                lang,
            ),
            this.usersService.updateVerificationCode(
                createdDoctor.user.id,
                code,
            ),
            this.usersService.confirmEmail(createdDoctor.user.id, lang),
            this.usersService.sendEmailToAdminOnDoctorRegistration(lang),
            this.usersService.sendRegistrationEmail(
                createdDoctor.user.id,
                header,
                body,
                lang,
            ),
        ]);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                DoctorMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
            doctorId: createdDoctor.id,
            userId: createdDoctor.user.id,
            token: tokenPayload,
        };
    }

    async unsetPendingShareStatusForDoctor(id: number): Promise<void> {
        await this.doctorsRepository.update(
            { id },
            {
                // isActive: true, deactivationReason: null
                hasPendingKashfShare: false,
            },
        );
    }

    async updateDoctor(
        id: number,
        updateDoctorDto: UpdateDoctorDto,
        user: UserDetailsDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.user', 'user')
            .select([
                'doctor.id',
                'doctor.syndicateIdURL',
                'user.id',
                'user.email',
            ])
            .where('doctor.id = :id', {
                id,
            });

        const doctor = await query.getOne();
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.createScheduleForDoctor(id, updateDoctorDto, lang);
        this.helperService.removeEmptyKeys(updateDoctorDto);

        const userEntity = this.usersMapper.fromDTOToEntity(
            UserEntity,
            updateDoctorDto,
        );
        const doctorEntity = this.doctorsMapper.fromDTOToEntity(
            DoctorEntity,
            updateDoctorDto,
        );

        if (updateDoctorDto.doctorMemberships) {
            await this.doctorMembershipRepository.query(`
                delete
                from doctor_membership
                where doctor_id = '${id}'
            `);
            for (const item of updateDoctorDto.doctorMemberships) {
                const doctorMembership = new DoctorMembership();
                doctorMembership.doctorId = id;
                doctorMembership.membershipId = item.membership.id;
                doctorMembership.discountValue = item.discountValue;
                doctorMembership.validUntil = item.validUntil;
                await this.doctorMembershipRepository.save(doctorMembership);
            }
        }

        await this.userTranslationRepository.delete({
            user: {
                id: doctor.user.id,
            },
        });
        if (updateDoctorDto.translations) {
            await this.userTranslationRepository.save(
                updateDoctorDto.translations.map((item) => ({
                    ...item,
                    user: {
                        id: doctor.user.id,
                    },
                })),
            );
        }
        this.helperService.removeEmptyKeys(doctorEntity);

        if (userEntity.email) {
            await this.usersService.updateEmailVerified(doctor.user.id, false);
        }
        if (updateDoctorDto.syndicateIdURL && user.role === RoleType.DOCTOR) {
            doctorEntity.newSyndicateIdURL = updateDoctorDto.syndicateIdURL;
            doctorEntity.syndicateIdURL = doctor.syndicateIdURL;
            doctorEntity.syndicateIdStatus = SyndicateIdStatus.NEW;
            await this.usersService.sendEmailForSynicateUpdate(
                lang,
                doctor.user.email,
            );
        } else {
            doctorEntity.syndicateIdURL =
                updateDoctorDto.syndicateIdURL || doctor.syndicateIdURL;
            doctorEntity.syndicateIdStatus = SyndicateIdStatus.APPROVED;
        }
        doctorEntity.id = id;
        this.logger.debug(JSON.stringify(doctorEntity));
        await Promise.all([
            this.usersService.updateUser(doctor.user.id, userEntity, lang),
            this.doctorsRepository.save(doctorEntity),
        ]);
        if (doctorEntity.isActive) {
            await this.emailService
                .sendEmail(
                    doctor.user.email,
                    'Account Activated',
                    'your kashf account is now active',
                )
                .catch((error) => console.error(error));
        }
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                DoctorMessagesKeys.UPDATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async updateSyndicate(
        id: number,
        updateSyndicateIdStatusDto: UpdateSyndicateIdStatusDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.user', 'user')
            .select([
                'doctor.id',
                'doctor.syndicateIdURL',
                'doctor.newSyndicateIdURL',
                'user.id',
                'user.appLanguage',
            ])
            .where('doctor.id = :id', {
                id,
            });

        const doctor = await query.getOne();
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        let syndicateIdURL = doctor.syndicateIdURL;
        let syndicateIdStatus = doctor.syndicateIdStatus;
        if (
            updateSyndicateIdStatusDto.status === SyndicateIdStatus.APPROVED &&
            doctor.newSyndicateIdURL
        ) {
            syndicateIdURL = doctor.newSyndicateIdURL;
            syndicateIdStatus = SyndicateIdStatus.APPROVED;
        } else if (
            updateSyndicateIdStatusDto.status === SyndicateIdStatus.REJECTED
        ) {
            syndicateIdStatus = SyndicateIdStatus.REJECTED;
            this.notifyDoctorSyndicateIsRejected(doctor).catch((error) =>
                console.error(error),
            );
        }
        await this.doctorsRepository.update(
            { id },
            {
                syndicateIdURL,
                syndicateIdStatus,
                newSyndicateIdURL: null,
            },
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                DoctorMessagesKeys.UPDATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async notifyDoctorSyndicateIsRejected(doctor: DoctorEntity): Promise<void> {
        const notification: CreateNotificationDto = {
            userId: doctor.user.id,
            data: {
                doctorId: doctor.id.toString(),
                type: NotificationTyps.NOTIFIY_DOCTOR_AFTER_REJECT_UPDATE_SYNDICATE,
            },
            notificationTranslations: [
                {
                    title: 'تم رفض تعديل صوره كارنيه النقابه',
                    body: 'تم رفض تعديل صوره كارنيه النقابه',
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Your syndicate id update request is rejected',
                    body: 'Your syndicate id update request is rejected',
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                doctor.user.id,
                doctor.user.appLanguage,
            )
            .catch((error) => console.error(error));
    }

    async createScheduleForDoctor(
        id: number,
        doctorDto: UpdateDoctorDto | DoctorDto,
        lang: string,
    ): Promise<void> {
        const [scheduleExistForHomeVisit, scheduleExistForOnlineConsultation] =
            await Promise.all([
                this.schedulesRepository.findOne({
                    where: {
                        doctor: { id },
                        type: ConsultationType.HOME_VISIT,
                        serviceProvider: null,
                    },
                }),
                this.schedulesRepository.findOne({
                    where: {
                        doctor: { id },
                        type: ConsultationType.ONLINE_CONSULTATION,
                        serviceProvider: null,
                    },
                }),
            ]);

        if (!scheduleExistForHomeVisit) {
            await this.schedulesService.createSchedule(
                {
                    doctorId: id,
                    fees: doctorDto.homeVisitFees,
                    type: ConsultationType.HOME_VISIT,
                },
                lang,
            );
        }
        if (!scheduleExistForOnlineConsultation) {
            await this.schedulesService.createSchedule(
                {
                    doctorId: id,
                    fees: doctorDto.onlineConsultationFees,
                    type: ConsultationType.ONLINE_CONSULTATION,
                },
                lang,
            );
        }
        if (scheduleExistForHomeVisit) {
            await this.schedulesService.updateSchedule(
                scheduleExistForHomeVisit.id,
                id,
                {},
                { fees: doctorDto.homeVisitFees },
                lang,
            );
        }
        if (scheduleExistForOnlineConsultation) {
            await this.schedulesService.updateSchedule(
                scheduleExistForOnlineConsultation.id,
                id,
                {},
                { fees: doctorDto.onlineConsultationFees },
                lang,
            );
        }
    }

    async createScheduleDoctorServiceProvider(
        id: number,
        serviceProviderId: number,
        doctorDto: DoctorDto,
        lang: string,
    ): Promise<void> {
        await this.schedulesService.createSchedule(
            {
                doctorId: id,
                serviceProviderId,
                fees: doctorDto.homeVisitFees,
                type: ConsultationType.HOME_VISIT,
            },
            lang,
        );
        await this.schedulesService.createSchedule(
            {
                doctorId: id,
                serviceProviderId,
                fees: doctorDto.onlineConsultationFees,
                type: ConsultationType.ONLINE_CONSULTATION,
            },
            lang,
        );
    }

    async getDoctorStatus(
        doctorId: number,
        doctor: DoctorDto,
        serviceProviderId?: number,
    ): Promise<string> {
        let status = UserStatus.OFFLINE;

        if (serviceProviderId) {
            const lastCheckIn = await this.getLastCheckIn(
                doctorId,
                serviceProviderId,
                true,
            );
            if (lastCheckIn) {
                status = UserStatus.ONLINE;
            }
        }
        if (!serviceProviderId) {
            const schedules = await this.schedulesRepository.find({
                where: {
                    doctor: { id: doctorId },
                    type: ConsultationType.ONLINE_CONSULTATION,
                },
                relations: ['workingDays'],
            });
            const statuses = schedules.map((schedule) => {
                if (!schedule) {
                    return UserStatus.OFFLINE;
                }
                if (
                    schedule.isEnabled &&
                    schedule.workingDays &&
                    schedule.workingDays.length &&
                    schedule.workingDays[0].isActive
                ) {
                    const todayTime = moment()
                        .add(2, 'hours')
                        .format('HH:mm:ss');

                    const isOnline =
                        todayTime > schedule.workingDays[0].from.toString() &&
                        todayTime < schedule.workingDays[0].to.toString();

                    if (isOnline) {
                        return UserStatus.ONLINE;
                    }
                }
                return UserStatus.OFFLINE;
            });
            if (statuses.includes(UserStatus.ONLINE) || doctor.isASAPCalls) {
                status = UserStatus.ONLINE;
            }
            if (doctor.isInCall) {
                status = UserStatus.BUSY;
            }
        }
        return status;
    }

    async getDoctorsForPatient(
        httpQueryString: string,
        user: UserDetailsDto,
        lang: string,
    ): Promise<DoctorForPatientDto[]> {
        try {
            let query = this.doctorsRepository
                .createQueryBuilder('doctor')
                .leftJoinAndSelect(
                    'doctor.serviceProviders',
                    'serviceProviders',
                )
                .leftJoinAndSelect(
                    'serviceProviders.insuranceCompanies',
                    'insuranceCompanies',
                )
                .leftJoinAndSelect('doctor.specialities', 'specialities')
                .leftJoinAndSelect(
                    'specialities.translations',
                    'specialityTranslations',
                    'specialityTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect('doctor.grade', 'grade')
                .leftJoinAndSelect(
                    'grade.translations',
                    'gradeTranslations',
                    'gradeTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect(
                    'doctor.doctorMemberships',
                    'doctorMemberships',
                    'doctor.id = doctorMemberships.doctorId',
                )
                .leftJoinAndSelect(
                    'doctorMemberships.membership',
                    'membership',
                    'doctorMemberships.membershipId = membership.id',
                )
                .leftJoinAndSelect(
                    'membership.translations',
                    'membershipsTranslations',
                    'membershipsTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect('doctor.schedules', 'schedules')
                .leftJoinAndSelect('doctor.clinics', 'clinics')
                .leftJoinAndSelect('clinics.governorate', 'governorate')
                .leftJoinAndSelect('clinics.city', 'city')
                .leftJoinAndSelect('doctor.user', 'user')
                .select([
                    'doctor.id',
                    'doctor.homeVisitFees',
                    'doctor.onlineConsultationFees',
                    'doctor.hasPendingKashfShare',
                    'doctor.isActive',
                    'doctor.isASAPCalls',
                    'doctor.isASAPHomeVisit',
                    'doctor.status',
                    'doctor.languages',
                    'doctor.isAcceptCashOnhomeVisit',
                    'doctor.arabicName',
                    'doctor.isInCall',
                    'user.id',
                    'user.name',
                    'user.gender',
                    'user.avatar',
                    'user.averageRating',
                    'user.totalConsultations',
                    'schedules.id',
                    'schedules.fees',
                    'clinics.id',
                    'clinics.fees',
                    'governorate.id',
                    'city.id',
                    'specialities.id',
                    'grade.id',
                    'gradeTranslations',
                    'specialityTranslations',
                    'serviceProviders.id',
                    'insuranceCompanies.id',
                    'doctorMemberships',
                    'membership',
                    'membershipsTranslations',
                ])
                .where('doctor.isActive = :isActive', { isActive: true })
                .andWhere('user.isDeleted = :isDelete', {
                    isDelete: false,
                });

            let status;
            if (httpQueryString) {
                query = this.buildGetDoctorsQuery(query, httpQueryString);

                status = this.helperService.extractFilterValue(
                    JSON.parse(httpQueryString),
                    'status',
                );
            }

            const doctors = await query.getMany();

            let mappedDoctors = await Promise.all(
                doctors.map(async (doctor) => {
                    const doctorDto = this.doctorsMapper.fromEntityToDTO(
                        DoctorDto,
                        doctor,
                    );
                    doctorDto.grade = doctorDto.grade
                        ? {
                              id: doctorDto.grade.id,
                              title: doctorDto.grade.translations[0]
                                  ? doctorDto.grade.translations[0].title
                                  : '',
                          }
                        : {
                              id: null,
                              title: '',
                          };
                    doctorDto.specialities = doctorDto.specialities
                        ? doctorDto.specialities.map((speciality) => ({
                              id: speciality.id,
                              title: speciality.translations[0]
                                  ? speciality.translations[0].title
                                  : '',
                          }))
                        : [];
                    doctorDto.clinics = this.clinicsMapper.fromEntitiesToDTOs(
                        doctor.clinics,
                        lang,
                    );

                    doctorDto.doctorMemberships = this.mapToMembership(
                        doctorDto,
                        doctor,
                    );

                    doctorDto.isFavourite = false;

                    if (user.role === RoleType.PATIENT) {
                        await this.getIsFavouriteDoctor(
                            doctor,
                            user.id,
                            doctorDto,
                        );
                    }
                    doctorDto.status = await this.getDoctorStatus(
                        doctor.id,
                        doctorDto,
                    );
                    const fees = this.getDoctorFees(doctorDto);
                    return {
                        ...doctorDto,
                        feesMin: fees.length > 0 ? Math.min(...fees) : 0,
                        feesMax: fees.length > 0 ? Math.max(...fees) : 0,
                    };
                }),
            );
            if (status) {
                mappedDoctors = mappedDoctors.filter(
                    (doctor) => doctor.status === status,
                );
            }
            return mappedDoctors;
        } catch (error) {
            this.logger.error(
                `Error in getDoctorsForPatient: ${error.message}`,
                error.stack,
            );
            throw error;
        }
    }

    getDoctorFees(doctor: DoctorDto): number[] {
        if (!doctor.schedules || doctor.schedules.length === 0) {
            return [0]; // Return default fee to prevent Math.min/max crashes
        }
        return doctor.schedules.map((schedule) => {
            if (schedule.type === ConsultationType.ONLINE_CONSULTATION) {
                return schedule.fees * 5;
            }
            return schedule.fees;
        });
    }

    async downloadDoctors(
        providerId: string,
        httpQueryString: string,
        lang = 'ar',
    ): Promise<string> {
        let query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.user', 'user')
            .select([
                'doctor.id',
                'doctor.status',
                'doctor.syndicateNumber',
                'user.id',
                'user.name',
                'user.phone',
                'specialities.id',
                'specialityTranslations',
                'grade.id',
                'gradeTranslations',
            ])
            .where('user.isDeleted = :isDelete', {
                isDelete: false,
            });

        if (providerId) {
            query
                .leftJoin('doctor.serviceProviders', 'serviceProvider')
                .where('serviceProvider.id = :providerId', {
                    providerId: parseInt(providerId, 10),
                });
        }

        if (httpQueryString) {
            query = this.buildGetDoctorsQuery(query, httpQueryString);
        }

        const doctors = await query.getMany();

        const mappedDoctors = doctors.map((doctor) =>
            this.doctorsMapper.fromEntityToDownloadData(
                DownloadDoctorDto,
                doctor,
            ),
        );
        const fields = [
            'id',
            'name',
            'syndicateId',
            'specialities',
            'grade',
            'mobileNumber',
        ];
        return this.dataDownloadService.downloadCsv(fields, mappedDoctors);
    }

    async getDoctorsForServiceProvider(
        httpQueryString: string,
        pageOptionsDto: PageOptionsDto,
        user: UserDetailsDto,
        lang: string,
    ): Promise<DoctorsPageDto> {
        let query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.serviceProviders', 'serviceProvider')
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect(
                'doctor.doctorMemberships',
                'doctorMemberships',
                'doctor.id = doctorMemberships.doctorId',
            )
            .leftJoinAndSelect(
                'doctorMemberships.membership',
                'membership',
                'doctorMemberships.membershipId = membership.id',
            )
            .leftJoinAndSelect(
                'membership.translations',
                'membershipsTranslations',
                'membershipsTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.user', 'user')
            .where('serviceProvider.id = :id', {
                id: user.serviceProvider.id,
            })
            .andWhere('user.isDeleted = :isDelete', {
                isDelete: false,
            });
        if (httpQueryString) {
            query = this.buildGetDoctorsQuery(query, httpQueryString);
        }
        const [doctors, pageMetaDto] = await query.paginate(pageOptionsDto);

        const mappedDoctors = await Promise.all(
            doctors.map(async (doctor) => {
                const doctorDto = this.doctorsMapper.fromEntityToDTO(
                    DoctorDto,
                    doctor,
                );
                doctorDto.grade = doctorDto.grade
                    ? {
                          id: doctorDto.grade.id,
                          title: doctorDto.grade.translations[0]
                              ? doctorDto.grade.translations[0].title
                              : '',
                      }
                    : {
                          id: null,
                          title: '',
                      };
                doctorDto.specialities = doctorDto.specialities
                    ? doctorDto.specialities.map((speciality) => ({
                          id: speciality.id,
                          title: speciality.translations[0]
                              ? speciality.translations[0].title
                              : '',
                      }))
                    : [];
                doctorDto.doctorMemberships = this.mapToMembership(
                    doctorDto,
                    doctor,
                );
                const serviceProviderId =
                    user.serviceProvider && user.serviceProvider.id;

                if (serviceProviderId) {
                    const lastCheckIn = await this.getLastCheckIn(
                        doctorDto.id,
                        serviceProviderId,
                        true,
                    );
                    doctorDto.lastCheckIn =
                        lastCheckIn && lastCheckIn.createdAt;
                }
                doctorDto.status = await this.getDoctorStatus(
                    doctor.id,
                    doctorDto,
                    serviceProviderId,
                );
                return doctorDto;
            }),
        );
        return new DoctorsPageDto(mappedDoctors, pageMetaDto);
    }

    async getDoctorsForAdmin(
        httpQueryString: string,
        pageOptionsDto: PageOptionsDto,
        user: UserDetailsDto,
        lang: string,
    ): Promise<DoctorsPageDto> {
        let query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect(
                'doctor.doctorMemberships',
                'doctorMemberships',
                'doctor.id = doctorMemberships.doctorId',
            )
            .leftJoinAndSelect(
                'doctorMemberships.membership',
                'membership',
                'doctorMemberships.membershipId = membership.id',
            )
            .leftJoinAndSelect(
                'membership.translations',
                'membershipsTranslations',
                'membershipsTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.user', 'user')
            .leftJoinAndSelect('doctor.serviceProviders', 'serviceProviders')
            .select([
                'doctor.id',
                'doctor.homeVisitFees',
                'doctor.onlineConsultationFees',
                'doctor.isActive',
                'doctor.isBanned',
                'doctor.status',
                'doctor.nationalID',
                'doctor.nationalIdFrontURL',
                'doctor.nationalIdBackURL',
                'doctor.syndicateNumber',
                'doctor.syndicateIdURL',
                'doctor.newSyndicateIdURL',
                'doctor.syndicateIdStatus',
                'doctor.taxIdURL',
                'doctor.link',
                'doctor.certificates',
                'doctor.languages',
                'doctor.isInCall',
                'doctor.isASAPCalls',
                'doctor.isASAPHomeVisit',
                'doctor.isFreeCall',
                'doctor.isFreeVisit',
                'doctor.createdAt',
                'doctor.arabicName',
                'user.id',
                'user.name',
                'user.gender',
                'user.phone',
                'user.email',
                'user.avatar',
                'user.createdAt',
                'user.isComplete',
                'specialities.id',
                'specialityTranslations',
                'grade',
                'gradeTranslations',
                'serviceProviders',
                'doctorMemberships',
                'membership',
                'membershipsTranslations',
            ]);

        if (httpQueryString) {
            query = this.buildGetDoctorsQuery(query, httpQueryString);
        }

        const queryResult = await tryCatch(query.paginate(pageOptionsDto));

        if (!queryResult.isSuccess) {
            throw new BadRequestException(queryResult.error);
        }

        const [doctors, pageMetaDto] = queryResult.value;

        const mappedDoctors = await Promise.all(
            doctors
                .map(async (doctor) => {
                    const doctorDto = this.doctorsMapper.fromEntityToDTO(
                        DoctorDto,
                        doctor,
                    );
                    doctorDto.grade = doctorDto.grade
                        ? {
                              id: doctorDto.grade.id,
                              title: doctorDto.grade.translations[0]
                                  ? doctorDto.grade.translations[0].title
                                  : '',
                          }
                        : {
                              id: null,
                              title: '',
                          };
                    doctorDto.specialities = doctorDto.specialities
                        ? doctorDto.specialities.map((speciality) => ({
                              id: speciality.id,
                              title: speciality.translations[0]
                                  ? speciality.translations[0].title
                                  : '',
                          }))
                        : [];
                    doctorDto.doctorMemberships = this.mapToMembership(
                        doctorDto,
                        doctor,
                    );
                    const lastCheckInResult = await tryCatch(
                        this.getLastCheckInForAdmin(doctorDto.id, true),
                    );

                    if (lastCheckInResult.isSuccess) {
                        const lastCheckIn = lastCheckInResult.value;
                        doctorDto.lastCheckIn =
                            lastCheckIn && lastCheckIn.createdAt;
                    }

                    const statusResult = await tryCatch(
                        this.getDoctorStatus(doctor.id, doctorDto),
                    );

                    if (statusResult.isSuccess) {
                        doctorDto.status = statusResult.value;
                    }

                    return doctorDto;
                })
                .filter(Boolean),
        );
        return new DoctorsPageDto(mappedDoctors, pageMetaDto);
    }

    buildGetDoctorsQuery(
        query: SelectQueryBuilder<DoctorEntity>,
        httpQueryString: string,
    ): SelectQueryBuilder<DoctorEntity> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }
        // TODO: fix filter once the doctor has en and ar names
        if (httpQueryObject.search) {
            query
                .andWhere(
                    '(' +
                        'doctor.id || ' +
                        'doctor.created_at::text || ' +
                        'user.name || ' +
                        'user.phone || ' +
                        'user.email || ' +
                        'specialityTranslations.title ' +
                        ') ILIKE :searchKey',
                    {
                        searchKey: `%${httpQueryObject.search.value}%`,
                    },
                )
                .orWhere('doctor.arabic_name ILIKE :searchKey', {
                    searchKey: `%${httpQueryObject.search.value}%`,
                });
        }
        if (httpQueryObject.filters) {
            httpQueryObject.filters.forEach((filter) => {
                switch (filter.type) {
                    case FilterType.FIXED:
                        switch (filter.by) {
                            case FilterByType.SPECIALITY:
                                query.andWhere(
                                    'specialities.id = :specialityId',
                                    {
                                        specialityId: parseInt(
                                            filter.value,
                                            10,
                                        ),
                                    },
                                );
                                break;
                            case FilterByType.GOVERNORATE:
                                query.andWhere(
                                    'governorate.id = :governorateId',
                                    {
                                        governorateId: parseInt(
                                            filter.value,
                                            10,
                                        ),
                                    },
                                );
                                break;
                            case FilterByType.CITY:
                                query.andWhere('city.id = :cityId', {
                                    cityId: parseInt(filter.value, 10),
                                });
                                break;
                            case FilterByType.GRADE:
                                query.andWhere('grade.id = :gradeId', {
                                    gradeId: parseInt(filter.value, 10),
                                });
                                break;
                            case FilterByType.SERVICE_PROVIDER:
                                query.andWhere(
                                    'serviceProviders.id = :serviceProviderId',
                                    {
                                        serviceProviderId: parseInt(
                                            filter.value,
                                            10,
                                        ),
                                    },
                                );
                                break;
                            case FilterByType.INSURANCE_COMPANY:
                                query.andWhere(
                                    'insuranceCompanies.id = :insuranceCompanyId',
                                    {
                                        insuranceCompanyId: parseInt(
                                            filter.value,
                                            10,
                                        ),
                                    },
                                );
                                break;
                            case FilterByType.EMAIL:
                                query.andWhere('user.email = :email', {
                                    email: filter.value,
                                });
                                break;
                            case FilterByType.GENDER:
                                query.andWhere('user.gender = :gender', {
                                    gender: filter.value,
                                });
                                break;
                            case FilterByType.PAYMENT_METHOD:
                                if (filter.value === PaymentMethod.CASH) {
                                    query.andWhere(
                                        'doctor.isAcceptCashOnhomeVisit = :isAcceptCashOnhomeVisit',
                                        {
                                            isAcceptCashOnhomeVisit: true,
                                        },
                                    );
                                }
                        }
                        break;
                    case FilterType.RANGE:
                        switch (filter.by) {
                            case FilterByType.RATING:
                                query.andWhere(
                                    'user.averageRating between :minRating AND :maxRating',
                                    {
                                        minRating: filter.min,
                                        maxRating: filter.max,
                                    },
                                );
                                break;
                            case FilterByType.FEES:
                                query.andWhere(
                                    'schedules.fees between :minFees AND :maxFees',
                                    {
                                        minFees: filter.min,
                                        maxFees: filter.max,
                                    },
                                );
                        }
                        break;
                    case FilterType.MULTIPLE:
                        switch (filter.by) {
                            case FilterByType.LANGUAGES:
                                query.andWhere(
                                    new Brackets((nestedQuery) => {
                                        filter.values.forEach((op) => {
                                            nestedQuery.orWhere(
                                                'doctor.languages ILIKE :languages',
                                                {
                                                    languages: `%${op}%`,
                                                },
                                            );
                                        });
                                    }),
                                );
                        }
                }
            });
        }

        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.RATING:
                    query.orderBy(
                        'user.averageRating',
                        httpQueryObject.sort.type,
                    );
                    break;
                case SortByType.POPULARITY:
                    query.orderBy(
                        'user.totalConsultations',
                        httpQueryObject.sort.type,
                    );
                    break;
                case SortByType.FEES:
                    query.orderBy({
                        'doctor.onlineConsultationFees':
                            httpQueryObject.sort.type,
                        'doctor.homeVisitFees': httpQueryObject.sort.type,
                        'clinics.fees': httpQueryObject.sort.type,
                    });
                    break;
                case SortByType.NAME:
                    query.orderBy('user.name', httpQueryObject.sort.type);
                    break;
                case SortByType.SPECIALTY:
                    query.orderBy(
                        'specialityTranslations.title',
                        httpQueryObject.sort.type,
                    );
                    break;
                case SortByType.CREATED_AT:
                    query.orderBy(
                        'doctor.createdAt',
                        httpQueryObject.sort.type,
                    );
                    break;
                case SortByType.UPDATED_AT:
                    query.orderBy(
                        'doctor.updatedAt',
                        httpQueryObject.sort.type,
                    );
            }
        }

        return query;
    }

    async getDoctorForServiceProvider(
        id: number,
        user: UserDetailsDto,
        lang: string,
    ): Promise<DoctorDto> {
        const serviceProviderId = user.serviceProvider.id;
        const query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect(
                'doctor.doctorMemberships',
                'doctorMemberships',
                'doctor.id = doctorMemberships.doctorId',
            )
            .leftJoinAndSelect(
                'doctorMemberships.membership',
                'membership',
                'doctorMemberships.membershipId = membership.id',
            )
            .leftJoinAndSelect(
                'membership.translations',
                'membershipsTranslations',
                'membershipsTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.user', 'user')
            .leftJoinAndSelect(
                'doctor.serviceProviders',
                'serviceProviders',
                'serviceProviders.id = :serviceProviderId',
                { serviceProviderId },
            )
            .leftJoinAndSelect(
                'doctor.schedules',
                'schedules',
                'schedules.serviceProvider = :serviceProviderId',
                { serviceProviderId },
            )
            .leftJoinAndSelect('schedules.workingDays', 'workingDays')
            .leftJoinAndSelect('doctor.clinics', 'clinics')
            .leftJoinAndSelect('clinics.governorate', 'governorate')
            .leftJoinAndSelect('clinics.city', 'city')
            .leftJoinAndSelect(
                'clinics.translations',
                'clinicTranslations',
                'clinicTranslations.languageCode = :lang',
                { lang },
            )
            .select([
                'doctor.id',
                'doctor.homeVisitFees',
                'doctor.onlineConsultationFees',
                'doctor.urgentReservationPercentage',
                'doctor.higherPricingPercentage',
                'doctor.secretaryEmail',
                'doctor.status',
                'doctor.nationalID',
                'doctor.nationalIdFrontURL',
                'doctor.nationalIdBackURL',
                'doctor.syndicateNumber',
                'doctor.syndicateIdURL',
                'doctor.newSyndicateIdURL',
                'doctor.taxIdURL',
                'doctor.link',
                'doctor.arabicName',
                'doctor.certificates',
                'doctor.languages',
                'doctor.isInCall',
                'doctor.isASAPCalls',
                'doctor.isASAPHomeVisit',
                'doctor.isFreeCall',
                'doctor.isFreeVisit',
                'user.id',
                'user.name',
                'user.role',
                'user.phone',
                'user.email',
                'user.avatar',
                'user.averageRating',
                'specialities',
                'specialityTranslations',
                'grade',
                'gradeTranslations',
                'clinics.id',
                'clinics.fees',
                'clinics.isFreeClinic',
                'governorate',
                'city',
                'clinicTranslations.id',
                'clinicTranslations.name',
                'clinicTranslations.languageCode',
                'serviceProviders',
                'schedules',
                'workingDays',
                'doctorMemberships',
                'membership',
                'membershipsTranslations',
            ])
            .where('doctor.id = :id', { id })
            .andWhere('user.isDeleted = :isDelete', { isDelete: false });

        const doctor = await query.getOne();

        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const doctorDto = this.doctorsMapper.fromEntityToDTO(DoctorDto, doctor);

        doctorDto.status = await this.getDoctorStatus(
            doctor.id,
            doctorDto,
            serviceProviderId,
        );

        doctorDto.grade = {
            id: doctorDto.grade.id,
            title: doctorDto.grade.translations[0].title,
        };
        doctorDto.specialities = doctor.specialities
            ? doctor.specialities.map((speciality) => ({
                  id: speciality.id,
                  title: speciality.translations[0].title,
              }))
            : [];
        doctorDto.doctorMemberships = this.mapToMembership(doctorDto, doctor);
        doctorDto.clinics = this.clinicsMapper.fromEntitiesToDTOs(
            doctor.clinics,
            lang,
        );
        return doctorDto;
    }

    async getDoctorForPatient(
        id: number,
        user: UserDetailsDto,
        lang: string,
    ): Promise<DoctorDto> {
        try {
            const query = this.doctorsRepository
                .createQueryBuilder('doctor')
                .leftJoinAndSelect(
                    'doctor.onlineWithServiceProvider',
                    'onlineStatus',
                )
                .leftJoinAndSelect(
                    'onlineStatus.user',
                    'onlineWithServiceProviderUser',
                )
                .leftJoinAndSelect('doctor.grade', 'grade')
                .leftJoinAndSelect(
                    'grade.translations',
                    'gradeTranslations',
                    'gradeTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect('doctor.specialities', 'specialities')
                .leftJoinAndSelect(
                    'specialities.translations',
                    'specialityTranslations',
                    'specialityTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect('doctor.user', 'user')
                .leftJoinAndSelect(
                    'user.translations',
                    'userTranslations',
                    'userTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect(
                    'doctor.doctorMemberships',
                    'doctorMemberships',
                    'doctor.id = doctorMemberships.doctorId',
                )
                .leftJoinAndSelect(
                    'doctorMemberships.membership',
                    'membership',
                    'doctorMemberships.membershipId = membership.id',
                )
                .leftJoinAndSelect(
                    'membership.translations',
                    'membershipsTranslations',
                    'membershipsTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect('doctor.schedules', 'schedules')
                .leftJoin(
                    'schedules.serviceProvider',
                    'serviceProvider',
                    'serviceProvider.isActive = :isActive',
                    { isActive: true },
                )
                .leftJoinAndSelect(
                    'serviceProvider.user',
                    'serviceProviderUser',
                )
                .leftJoinAndSelect(
                    'serviceProviderUser.translations',
                    'serviceProviderUserTranslations',
                    'serviceProviderUserTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect('doctor.clinics', 'clinics')
                .leftJoinAndSelect(
                    'clinics.translations',
                    'clinicsTranslations',
                    'clinicsTranslations.languageCode = :lang',
                    { lang },
                )
                .leftJoinAndSelect('clinics.governorate', 'governorate')
                .leftJoinAndSelect('clinics.city', 'city')
                .leftJoinAndSelect('schedules.clinic', 'clinic')
                .leftJoinAndSelect(
                    'clinic.translations',
                    'clinicTranslations',
                    'clinicTranslations.languageCode = :lang',
                    { lang },
                )
                .select([
                    'doctor.id',
                    'doctor.homeVisitFees',
                    'doctor.onlineConsultationFees',
                    'doctor.urgentReservationPercentage',
                    'doctor.refundPercentageOnCancellation',
                    'doctor.higherPricingPercentage',
                    'doctor.hasPendingKashfShare',
                    'doctor.status',
                    'doctor.languages',
                    'doctor.isInCall',
                    'doctor.isASAPCalls',
                    'doctor.isASAPHomeVisit',
                    'doctor.isFreeCall',
                    'doctor.isFreeVisit',
                    'doctor.arabicName',
                    'user.id',
                    'user.name',
                    'user.role',
                    'user.avatar',
                    'user.averageRating',
                    'specialities',
                    'specialityTranslations',
                    'grade',
                    'gradeTranslations',
                    'clinics.id',
                    'clinics.fees',
                    'clinics.isFreeClinic',
                    'clinicTranslations.id',
                    'clinicsTranslations.name',
                    'clinicsTranslations.languageCode',
                    'governorate',
                    'city',
                    'clinic.id',
                    'clinic.isFreeClinic',
                    'clinicTranslations.id',
                    'clinicTranslations.name',
                    'clinicTranslations.languageCode',
                    'schedules.id',
                    'schedules.fees',
                    'schedules.clinic',
                    'schedules.type',
                    'serviceProvider.id',
                    'serviceProviderUser.id',
                    'serviceProviderUserTranslations.id',
                    'serviceProviderUserTranslations.name',
                    'userTranslations.about',
                    'doctorMemberships',
                    'membership',
                    'membershipsTranslations',
                    'onlineStatus.id',
                    'onlineWithServiceProviderUser.name',
                ])
                .where('doctor.id = :id', { id })
                .andWhere('user.isDeleted = :isDelete', { isDelete: false });

            const doctor = await query.getOne();
            this.logger.log(JSON.stringify(doctor));

            if (!doctor) {
                throw new HttpException(
                    {
                        message: await this.i18n.translate(
                            AuthMessagesKeys.USER_NOT_FOUND,
                            {
                                lang,
                            },
                        ),
                    },
                    HttpStatus.NOT_FOUND,
                );
            }

            const doctorDto = this.doctorsMapper.fromEntityToDTO(
                DoctorDto,
                doctor,
            );

            doctorDto.status = await this.getDoctorStatus(doctor.id, doctorDto);
            doctorDto.ratings = await this.ratingsService.getDoctorReviews(
                doctorDto.user.id,
                6,
            );

            doctorDto.grade = doctorDto.grade
                ? {
                      id: doctorDto.grade.id,
                      title: doctorDto.grade.translations[0]
                          ? doctorDto.grade.translations[0].title
                          : '',
                  }
                : {
                      id: null,
                      title: '',
                  };
            doctorDto.specialities = doctor.specialities
                ? doctor.specialities.map((speciality) => ({
                      id: speciality.id,
                      title: speciality.translations[0]
                          ? speciality.translations[0].title
                          : '',
                  }))
                : [];
            doctorDto.doctorMemberships = this.mapToMembership(
                doctorDto,
                doctor,
            );
            doctorDto.isFavourite = false;
            if (user.role === RoleType.PATIENT) {
                await this.getIsFavouriteDoctor(doctor, user.id, doctorDto);
            }

            doctorDto.clinics = this.clinicsMapper.fromEntitiesToDTOs(
                doctor.clinics,
                lang,
            );
            doctorDto.schedules?.map((schedule: any) => {
                if (schedule.clinic) {
                    schedule.clinic = this.clinicsMapper.fromEntityToDTO(
                        ClinicDto,
                        schedule.clinic,
                    );
                }

                if (schedule.serviceProvider) {
                    schedule.serviceProvider.user.name = schedule
                        .serviceProvider.user.translations[0]
                        ? schedule.serviceProvider.user.translations[0].name
                        : '';
                    delete schedule.serviceProvider.user.translations;
                }
            });
            return doctorDto;
        } catch (error) {
            this.logger.error(
                `Error in getDoctorForPatient: ${error.message}`,
                error.stack,
            );
            throw error;
        }
    }

    async getDoctorProfile(id: number, lang: string): Promise<DoctorDto> {
        const query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.user', 'user')
            .leftJoinAndSelect('user.creditcards', 'creditcards')
            .leftJoinAndSelect('doctor.clinics', 'clinics')
            .leftJoinAndSelect('clinics.governorate', 'governorate')
            .leftJoinAndSelect('clinics.city', 'city')
            .leftJoinAndSelect(
                'doctor.onlineWithServiceProvider',
                'onlineStatus',
            )
            .leftJoinAndSelect(
                'onlineStatus.user',
                'onlineWithServiceProviderUser',
            )
            .leftJoinAndSelect('doctor.serviceProviders', 'serviceProviders')
            .leftJoinAndSelect('serviceProviders.user', 'serviceProviderUser')
            .leftJoinAndSelect(
                'clinics.translations',
                'clinicTranslations',
                'clinicTranslations.languageCode = :lang',
                { lang },
            )
            .where('doctor.id = :id', { id })
            .andWhere('user.isDeleted = :isDelete', { isDelete: false });

        const doctor = await query.getOne();
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const doctorDto = this.doctorsMapper.fromEntityToDTO(DoctorDto, doctor);
        doctorDto.status = await this.getDoctorStatus(
            doctor.id,
            doctorDto,
            doctor.onlineWithServiceProvider?.id,
        );
        doctorDto.clinics = this.clinicsMapper.fromEntitiesToDTOs(
            doctor.clinics,
            lang,
        );
        return doctorDto;
    }

    async getDoctorForAdmin(id: number, lang: string): Promise<DoctorDto> {
        const query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect(
                'doctor.doctorMemberships',
                'doctorMemberships',
                'doctor.id = doctorMemberships.doctorId',
            )
            .leftJoinAndSelect(
                'doctorMemberships.membership',
                'membership',
                'doctorMemberships.membershipId = membership.id',
            )
            .leftJoinAndSelect(
                'membership.translations',
                'membershipsTranslations',
                'membershipsTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.user', 'user')
            .leftJoinAndSelect('user.translations', 'userTranslations')
            .leftJoinAndSelect('doctor.serviceProviders', 'serviceProviders')
            .leftJoinAndSelect('doctor.schedules', 'schedules')
            .leftJoinAndSelect('schedules.workingDays', 'workingDays')
            .leftJoinAndSelect('doctor.clinics', 'clinics')
            .leftJoinAndSelect('clinics.governorate', 'governorate')
            .leftJoinAndSelect('clinics.city', 'city')
            .leftJoinAndSelect(
                'clinics.translations',
                'clinicTranslations',
                'clinicTranslations.languageCode = :lang',
                { lang },
            )
            .select([
                'doctor.id',
                'doctor.homeVisitFees',
                'doctor.urgentReservationPercentage',
                'doctor.higherPricingPercentage',
                'doctor.onlineConsultationFees',
                'doctor.refundPercentageOnCancellation',
                'doctor.secretaryEmail',
                'doctor.canForwardToSecretary',
                'doctor.status',
                'doctor.isActive',
                'doctor.isBanned',
                'doctor.website',
                'userTranslations.about',
                'doctor.nationalID',
                'doctor.nationalIdFrontURL',
                'doctor.nationalIdBackURL',
                'doctor.syndicateNumber',
                'doctor.syndicateIdURL',
                'doctor.newSyndicateIdURL',
                'doctor.taxIdURL',
                'doctor.instapayUrl',
                'doctor.link',
                'doctor.arabicName',
                'doctor.certificates',
                'doctor.languages',
                'doctor.isInCall',
                'doctor.isASAPCalls',
                'doctor.isASAPHomeVisit',
                'doctor.kashfPercentage',
                'doctor.isFreeCall',
                'doctor.isFreeVisit',
                'user.id',
                'user.name',
                'user.role',
                'user.phone',
                'user.email',
                'user.avatar',
                'user.averageRating',
                'specialities',
                'specialityTranslations',
                'userTranslations',
                'grade',
                'gradeTranslations',
                'clinics.id',
                'clinics.fees',
                'clinics.isFreeClinic',
                'clinics.location',
                'clinics.clinicLocation',
                'clinics.landlineNumber',
                'clinics.mobileNumber',
                'governorate',
                'city',
                'clinicTranslations.id',
                'clinicTranslations.name',
                'clinicTranslations.address',
                'clinicTranslations.languageCode',
                'serviceProviders',
                'schedules',
                'workingDays',
                'user.isDeleted',
                'doctorMemberships',
                'membership',
                'membershipsTranslations',
            ])
            .where('doctor.id = :id', { id });

        const doctor = await query.getOne();

        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const doctorDto = this.doctorsMapper.fromEntityToDTO(DoctorDto, doctor);

        doctorDto.status = await this.getDoctorStatus(doctor.id, doctorDto);

        doctorDto.grade = {
            id: doctorDto.grade.id,
            title: doctorDto.grade.translations[0].title,
        };
        doctorDto.specialities = doctor.specialities
            ? doctor.specialities.map((speciality) => ({
                  id: speciality.id,
                  title: speciality.translations[0].title,
              }))
            : [];
        doctorDto.doctorMemberships = this.mapToMembership(doctorDto, doctor);

        doctorDto.clinics = this.clinicsMapper.fromEntitiesToDTOs(
            doctor.clinics,
            lang,
        );
        return doctorDto;
    }

    async getDoctorRatings(
        id: number,
        user: UserDetailsDto,
        lang: string,
    ): Promise<GetRatingDto[]> {
        const query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.user', 'user')
            .select(['doctor.id', 'user.id', 'user.name'])
            .where('doctor.id = :id', { id })
            .andWhere('user.isDeleted = :isDelete', { isDelete: false });

        const doctor = await query.getOne();
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        return this.ratingsService.getDoctorReviews(doctor.user.id);
    }

    async addDoctorRatings(
        createDoctorRatingDto: CreateDoctorRatingDto,
        userId: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const query = this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.user', 'user')
            .select(['doctor.id', 'user.id', 'user.name'])
            .where('doctor.id = :id', { id: createDoctorRatingDto.doctorId })
            .andWhere('user.isDeleted = :isDelete', { isDelete: false });

        const doctor = await query.getOne();
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        return this.ratingsService.createRating(
            {
                ratedUserId: doctor.user.id,
                raterUserId: userId,
                requestId: createDoctorRatingDto.requestId,
                rating: createDoctorRatingDto.rating,
                comment: createDoctorRatingDto.comment,
                callRating: createDoctorRatingDto.callRating,
                callComment: createDoctorRatingDto.callComment,
            },
            lang,
        );
    }

    private async getIsFavouriteDoctor(
        doctor: DoctorEntity,
        currentUserId: number,
        doctorDto: DoctorDto,
    ) {
        const favouriteExsists = await this.favouriteRepository.findOne({
            where: {
                doctor: {
                    id: doctor.id,
                    user: {
                        isDeleted: false,
                    },
                },
                user: {
                    id: currentUserId,
                },
            },
        });

        if (favouriteExsists) {
            doctorDto.isFavourite = true;
        }
    }

    async findDoctor(doctorId: number): Promise<DoctorEntity> {
        return this.doctorsRepository.findOne({
            where: {
                id: doctorId,
            },
            relations: ['user', 'serviceProviders'],
        });
    }

    async setPendingShareStatusForDoctor(
        id: number,
        _reason?: DEACTIVATION_REASON,
    ) {
        await this.doctorsRepository.update(
            { id },
            {
                // isActive: false,
                hasPendingKashfShare: true,
                // deactivationReason: reason || DEACTIVATION_REASON.OTHER,
            },
        );
    }

    async deactivateDoctor(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        await this.doctorsRepository.update({ id }, { isActive: false });
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                DoctorMessagesKeys.DELETED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async getDoctorScheduleForPatient(
        doctorId: number,
        scheduleId: number,
        lang: string,
    ): Promise<DoctorScheduleForPatientDto[]> {
        const doctorSchedule =
            await this.schedulesService.getDoctorScheduleForPatient(
                doctorId,
                scheduleId,
            );
        if (!doctorSchedule) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ScheduleMessagesKeys.SCHEDULE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        return this.getScheduleDays(doctorSchedule, lang);
    }

    private async getScheduleDays(doctorSchedule: ScheduleDto, lang: string) {
        const schedule: DoctorScheduleForPatientDto[] = [];

        const workingDays = doctorSchedule.workingDays;
        for (let i = 0; i < 30; i++) {
            const date = moment().add(2, 'hour').add(i, 'days');
            // because moment's start of the week is Sunday -> 0
            const index = date.day() + 1 > 6 ? 0 : date.day() + 1;
            schedule.push({
                message: await this.i18n.translate(
                    DoctorMessagesKeys.SLOTS_RECOMMENDED_TIME,
                    {
                        lang,
                        args: {
                            from: moment(
                                doctorSchedule.workingDays[index].from,
                                'HH:mm:ss',
                            ).format('hh:mm A'),
                            to: moment(
                                doctorSchedule.workingDays[index].to,
                                'HH:mm A',
                            ).format('hh:mm A'),
                        },
                    },
                ),
                isEnabled: doctorSchedule.isEnabled,
                isActive: workingDays[index].isActive,
                id: workingDays[index].id,
                day: date.format('YYYY-MM-DD'),
                from: moment(workingDays[index].from, 'HH:mm:ss').format(
                    'hh:mm A',
                ),
                to: moment(workingDays[index].to, 'HH:mm:ss').format('hh:mm A'),
            });
        }
        return schedule;
    }

    async getDoctorDayScheduleForPatient(
        httpQueryString: string,
        doctorId: number,
        scheduleId: number,
        dayId: number,
        lang: string,
    ): Promise<DoctorDayScheduleForPatientDto> {
        let httpQueryObject: IHttpQuery;
        let date: any = moment().add(2, 'hour').format('YYYY-MM-DD');
        if (httpQueryString) {
            try {
                httpQueryObject = JSON.parse(httpQueryString);

                date = this.helperService.extractFilterValue(
                    httpQueryObject,
                    'date',
                );
                if (!date) {
                    throw new HttpException(
                        'Invalid date filter',
                        HttpStatus.BAD_REQUEST,
                    );
                }
            } catch (error) {
                throw new BadRequestException('Invalid filter');
            }
        }

        const doctorSchedule =
            await this.schedulesService.getDoctorScheduleForPatient(
                doctorId,
                scheduleId,
                dayId,
            );

        if (!doctorSchedule) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ScheduleMessagesKeys.SCHEDULE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.getDoctorSlots(doctorId, date, doctorSchedule, lang);
    }

    async getDoctorSlots(
        doctorId: number,
        date: Date,
        doctorSchedule: ScheduleDto,
        lang: string,
    ): Promise<DoctorDayScheduleForPatientDto> {
        const doctorRequests =
            await this.consultationRequestsService.getDoctorRequestsByDate(
                doctorId,
                moment(date, 'YYYY-MM-DD').toDate(),
            );

        const doctorRequestsSlots = doctorRequests.map((docRequest) =>
            docRequest.from
                ? docRequest.from.toString()
                : moment().format('HH:mm:ss'),
        );
        const slots: DoctorScheduleSlotsDto[] = [];
        const slotDuration =
            this.configService.DOCTORS_CONSULTATION_DURATION_IN_MINUTES;

        const startTime = moment(
            doctorSchedule.workingDays[0].from,
            'HH:mm:ss',
        );
        const isToday =
            doctorSchedule.workingDays[0].day === moment().format('dddd');
        // TODO exclude breaks in serviceprovider schedule
        const endTime = moment(doctorSchedule.workingDays[0].to, 'HH:mm:ss');
        while (startTime < endTime) {
            let isTimeNotPassed = false;
            let isDoctorHasOtherRequest = false;
            if (isToday) {
                isTimeNotPassed = startTime.isAfter(moment().add(2, 'hour'));
            } else {
                isTimeNotPassed = true;
            }
            isDoctorHasOtherRequest = doctorRequestsSlots.includes(
                startTime.format('HH:mm:ss'),
            );

            const isAvailable = isTimeNotPassed && !isDoctorHasOtherRequest;
            slots.push({
                isAvailable,
                time: startTime.format('hh:mm A'),
            });
            startTime.add(slotDuration, 'minutes');
        }

        // doctor is not working according to his schedule
        if (!doctorSchedule.isEnabled) {
            return {
                slots,
                isEnabled: false,
                message: await this.i18n.translate(
                    DoctorMessagesKeys.SLOTS_RECOMMENDED_TIME,
                    {
                        lang,
                        args: {
                            from: moment(
                                doctorSchedule.workingDays[0].from,
                                'HH:mm:ss',
                            ).format('hh:mm A'),
                            to: moment(
                                doctorSchedule.workingDays[0].to,
                                'HH:mm A',
                            ).format('hh:mm A'),
                        },
                    },
                ),
            };
        }

        return {
            slots,
            isEnabled: true,
        };
    }

    /**
     * @deprecated since 10/2/2021
     */
    async getDoctorSchedulesForPatient(
        httpQueryString: string,
        doctorId: number,
        lang: string,
    ): Promise<DoctorScheduleForPatientDto[]> {
        let httpQueryObject: IHttpQuery;
        if (httpQueryString) {
            try {
                httpQueryObject = JSON.parse(httpQueryString);
            } catch (error) {
                console.error(httpQueryString, error);
                throw new BadRequestException('Invalid filter');
            }
        }

        const doctorSchedule =
            await this.schedulesService.getDoctorSchedulesForPatient(
                httpQueryObject,
                doctorId,
            );
        if (!doctorSchedule) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ScheduleMessagesKeys.SCHEDULE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        return this.getScheduleDays(doctorSchedule, lang);
    }

    /**
     * @deprecated since 10/2/2021
     */
    async getDoctorDaysSchedulesForPatient(
        httpQueryString: string,
        doctorId: number,
        lang: string,
    ): Promise<DoctorDayScheduleForPatientDto> {
        let httpQueryObject: IHttpQuery;
        let date: any = moment().add(2, 'hour').format('YYYY-MM-DD');
        if (httpQueryString) {
            try {
                httpQueryObject = JSON.parse(httpQueryString);

                date = this.helperService.extractFilterValue(
                    httpQueryObject,
                    'date',
                );
                if (!date) {
                    throw new HttpException(
                        'Invalid date filter',
                        HttpStatus.BAD_REQUEST,
                    );
                }
            } catch (error) {
                throw new BadRequestException('Invalid filter');
            }
        }

        const doctorSchedule =
            await this.schedulesService.getDoctorSchedulesForPatient(
                httpQueryObject,
                doctorId,
            );

        if (!doctorSchedule) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ScheduleMessagesKeys.SCHEDULE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.getDoctorSlots(doctorId, date, doctorSchedule, lang);
    }

    async getDoctorOnlinePackages(
        doctorId: number,
        lang: string,
    ): Promise<GetOnlineConsulationPackages[]> {
        const doctor = await this.findDoctor(doctorId);
        if (!doctor) {
            throw new HttpException(
                {
                    message: 'doctor is not exist',
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        const onlineConsultationsDuration = [5, 10, 20, 30];

        return Promise.all(
            onlineConsultationsDuration.map(
                async (
                    duration: number,
                ): Promise<GetOnlineConsulationPackages> => ({
                    duration: await this.i18n.translate(
                        duration > 10
                            ? DoctorMessagesKeys.MINUITS_SESSION_SINGULAR
                            : DoctorMessagesKeys.MINUITS_SESSION_PLURAL,
                        {
                            lang,
                            args: {
                                min: duration,
                            },
                        },
                    ),
                    value: duration,
                    fees: duration * doctor.onlineConsultationFees,
                }),
            ),
        );
    }

    getDoctorSpeekingLanguages(
        lang: string,
    ): { label: string; value: string }[] {
        let speakingLanguages = [
            {
                label: 'عربي',
                value: Languages.AR,
            },
            {
                label: 'الانجليزى',
                value: Languages.EN,
            },
            {
                label: 'الفرنسيه',
                value: Languages.FR,
            },
            {
                label: 'الاسبانيه',
                value: Languages.ES,
            },
            {
                label: 'الايطاليه',
                value: Languages.IT,
            },
            {
                label: 'الألمانيه',
                value: Languages.DE,
            },
        ];
        if (lang === AvailableLanguageCodes.en) {
            speakingLanguages = [
                {
                    label: 'Arabic',
                    value: Languages.AR,
                },
                {
                    label: 'English',
                    value: Languages.EN,
                },
                {
                    label: 'Français',
                    value: Languages.FR,
                },
                {
                    label: 'Español',
                    value: Languages.ES,
                },
                {
                    label: 'Italiano',
                    value: Languages.IT,
                },
                {
                    label: 'German',
                    value: Languages.DE,
                },
            ];
        }
        return speakingLanguages;
    }

    async updateDoctorInaCall(
        doctorId: number,
        isInCall: boolean,
    ): Promise<void> {
        await this.doctorsRepository.update({ id: doctorId }, { isInCall });
    }

    async getDoctorServiceProviders(id: number, lang: string): Promise<any> {
        const doctor = await this.doctorsRepository
            .createQueryBuilder('doctor')
            .leftJoinAndSelect('doctor.serviceProviders', 'serviceProviders')
            .leftJoinAndSelect('serviceProviders.user', 'user')
            .leftJoinAndSelect(
                'user.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .where('doctor.id = :id', { id })
            .select([
                'doctor.id',
                'serviceProviders.id',
                'user.id',
                'user.avatar',
                'translations.name',
            ])
            .getOne();
        doctor.serviceProviders.map((serviceProvider) => {
            serviceProvider.user.name =
                serviceProvider.user.translations[0].name;
            delete serviceProvider.user.translations;
        });
        return doctor;
    }

    async veirfyDoctorServiceProviders(
        id: number,
        serviceProviderId: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { id: serviceProviderId },
            relations: ['user', 'user.translations'],
        });
        await this.addDoctorToServiceProvider(serviceProvider, id, lang);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.ADD_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async verifyDoctorServiceProvidersWithSignupCode(
        signupCode: string,
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { signupCode },
            relations: ['user', 'user.translations'],
        });
        await this.addDoctorToServiceProvider(serviceProvider, id, lang);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.ADD_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async addDoctorToServiceProvider(
        serviceProvider: ServiceProvider,
        id: number,
        lang: string,
    ): Promise<void> {
        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const doctor = await this.findDoctor(id);
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        DoctorMessagesKeys.DOCTOR_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        try {
            await this.serviceProvidersRepository
                .createQueryBuilder()
                .relation('doctors')
                .of(serviceProvider)
                .add(id);
        } catch (error) {
            if (error.code === '23505') {
                throw new HttpException(
                    {
                        message: await this.i18n.translate(
                            ServiceProviderMessagesKeys.SERVICE_PROVIDER_DOCTOR_ALREADY_EXISTS,
                            {
                                lang,
                            },
                        ),
                    },
                    HttpStatus.BAD_REQUEST,
                );
            }
        }
        await this.createScheduleDoctorServiceProvider(
            id,
            serviceProvider.id,
            {
                homeVisitFees: 1,
                onlineConsultationFees: 1,
            },
            lang,
        );
        await this.serviceProvidersService.notifyDoctorToVerifySchedule(
            id,
            doctor,
            serviceProvider,
        );
    }

    async isDoctorServiceProviderRelationExists(
        serviceProviderId: number,
        doctorId: number,
    ): Promise<boolean> {
        const result = await this.serviceProvidersRepository.query(`
            SELECT COUNT(*)
            FROM service_provider_doctors
            WHERE service_providers_id = ${serviceProviderId}
              AND doctors_id = ${doctorId}`);
        return result[0].count > 0;
    }

    async removeDoctorFromServiceProvider(
        serviceProviderId: number,
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { id: serviceProviderId },
            relations: ['user', 'user.translations'],
        });
        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const doctor = await this.findDoctor(id);
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        DoctorMessagesKeys.DOCTOR_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const isExists = await this.isDoctorServiceProviderRelationExists(
            serviceProviderId,
            id,
        );

        if (!isExists) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_DOCTOR_NOT_EXISTS,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        try {
            await this.serviceProvidersRepository
                .createQueryBuilder()
                .relation('doctors')
                .of(serviceProvider)
                .remove(id);
        } catch (error) {
            if (error.code === '23505') {
                throw new HttpException(
                    {
                        message: await this.i18n.translate(
                            ServiceProviderMessagesKeys.SERVICE_PROVIDER_DOCTOR_ALREADY_EXISTS,
                            {
                                lang,
                            },
                        ),
                    },
                    HttpStatus.BAD_REQUEST,
                );
            }
        }
        // await this.serviceProvidersService.notifyDoctorToVerifySchedule(
        //     id,
        //     doctor,
        //     serviceProvider,
        // );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.DELETE_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async checkinDoctorServiceProviders(
        id: number,
        serviceProviderId: number,
        updateDoctorDto: UpdateDoctorDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { id: serviceProviderId },
            relations: ['user', 'user.translations'],
        });
        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const doctor = await this.findDoctor(id);
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        if (updateDoctorDto.isCheckedIn === true) {
            const checkinLog =
                this.doctorServiceProviderCheckInLogsRepository.create({
                    doctor,
                    serviceProvider,
                    isCheckedIn: true,
                });
            await this.doctorServiceProviderCheckInLogsRepository.save(
                checkinLog,
            );
            doctor.onlineWithServiceProvider = serviceProvider;

            await this.doctorsRepository.save(doctor);
        }

        if (updateDoctorDto.isCheckedIn === false) {
            const checkoutLog =
                this.doctorServiceProviderCheckInLogsRepository.create({
                    doctor,
                    serviceProvider,
                    isCheckedIn: false,
                });
            await this.doctorServiceProviderCheckInLogsRepository.save(
                checkoutLog,
            );
            doctor.onlineWithServiceProvider = null;

            await this.doctorsRepository.save(doctor);
        }
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.SERVICE_PROVIDER_DOCTOR_CHECK_IN_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async setDoctorAsOnlineOnServiceProviders(
        id: number,
        lang: string,
        serviceProviderId: number = null,
    ) {
        const doctor = await this.findDoctor(id);
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        if (serviceProviderId) {
            const serviceProvider =
                await this.serviceProvidersRepository.findOne({
                    where: { id: serviceProviderId },
                });

            if (!serviceProvider) {
                throw new HttpException(
                    {
                        message: await this.i18n.translate(
                            ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                            {
                                lang,
                            },
                        ),
                    },
                    HttpStatus.NOT_FOUND,
                );
            }

            doctor.onlineWithServiceProvider = serviceProvider;
        } else {
            doctor.onlineWithServiceProvider = null;
        }

        await this.doctorsRepository.save(doctor);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.UPDATED_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async getLastCheckIn(
        doctorId: number,
        serviceProviderId: number,
        isCheckedIn?: boolean,
    ): Promise<DoctorServiceProviderCheckInLogs> {
        const query: any = {
            doctor: { id: doctorId },
            serviceProvider: { id: serviceProviderId },
        };
        if (isCheckedIn === true || isCheckedIn === false) {
            query.isCheckedIn = isCheckedIn;
        }
        return this.doctorServiceProviderCheckInLogsRepository.findOne({
            where: query,
            order: {
                id: 'DESC',
            },
        });
    }

    async getLastCheckInForAdmin(
        doctorId: number,
        isCheckedIn?: boolean,
    ): Promise<DoctorServiceProviderCheckInLogs> {
        const query: any = {
            doctor: { id: doctorId },
        };
        if (isCheckedIn === true || isCheckedIn === false) {
            query.isCheckedIn = isCheckedIn;
        }
        return this.doctorServiceProviderCheckInLogsRepository.findOne({
            where: query,
            order: {
                id: 'DESC',
            },
        });
    }

    async getDoctorByProductCode(productCode: string): Promise<DoctorEntity> {
        const doctor = this.doctorsRepository.findOne({
            where: {
                directLink: Like(`%${productCode}%`),
            },
            relations: ['user'],
            loadEagerRelations: true,
        });
        return doctor;
    }

    mapToMembership(doctorDto: DoctorDto, doctor: DoctorEntity) {
        return doctor.doctorMemberships
            ? doctor.doctorMemberships.map((doctorMembership) => ({
                  id: doctorMembership.membershipId,
                  title: doctorMembership.membership.translations[0]
                      ? doctorMembership.membership.translations[0].title
                      : '',
                  discountValue: doctorMembership.discountValue,
                  validUntil:
                      doctorMembership.validUntil ??
                      doctorMembership.membership.validUntil,
              }))
            : [];
    }

    async setDoctorAsOffline(id: number): Promise<void> {
        const doctor = await this.doctorsRepository.findOne({ where: { id } });
        doctor.status = 'Offline';
        doctor.isASAPCalls = false;
        doctor.isASAPHomeVisit = false;
        doctor.offlineAt = moment().toDate();
        await this.doctorsRepository.save(doctor);
        const notification: CreateNotificationDto = {
            userId: doctor.user.id,
            data: {
                type: NotificationTyps.DOCTOR_WENT_OFFLINE,
            },
            notificationTranslations: [
                {
                    title: 'الطبيب في وضع الخمول',
                    body: 'الطبيب في وضع الخمول الان ',
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'Doctor went Offline',
                    body: 'Doctor went Offline',
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        await this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                doctor.user.id,
                doctor.user.appLanguage,
            )
            .catch((err) => console.error(err));
    }

    async updateTheAvailability(): Promise<void> {
        const doctorsThatCallExpiredOrFinished =
            await this.consultationRequestsService.getDoctorsFromExpiredAndFinishedRequests();
        await this.doctorsRepository
            .createQueryBuilder('doctors')
            .whereInIds(doctorsThatCallExpiredOrFinished)
            .andWhere('doctors.is_in_call = :isInCall', { isInCall: true })
            .update(DoctorEntity)
            .set({ isInCall: false })
            .execute();
        await this.doctorsRepository
            .createQueryBuilder('doctors')
            .whereInIds(doctorsThatCallExpiredOrFinished)
            .andWhere('doctors.is_asap_calls = :isASAPCalls', {
                isASAPCalls: false,
            })
            .update(DoctorEntity)
            .set({ isASAPCalls: true })
            .execute();
    }

    async getDoctorWalletDetails(
        doctorId: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                DoctorMessagesKeys.UPDATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }
}
