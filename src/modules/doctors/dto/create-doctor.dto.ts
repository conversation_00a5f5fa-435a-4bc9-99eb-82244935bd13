'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsEmail,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsPhoneNumber,
    IsString,
    IsUrl,
    <PERSON>,
    <PERSON>,
} from 'class-validator';

import { GenderType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ToInt } from '../../../decorators/transforms.decorator';
import { AvailableLanguages } from '../../../i18n/languageCodes';
import { DoctorMembershipDto } from '../../memberships/dto/doctor-membership.dto';
import { UserTranslation } from '../../users/entities/user-translation.entity';

export class CreateDoctorDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    website?: string;

    @IsString()
    @Expose()
    @IsOptional()
    name?: string;

    @IsString()
    @Expose()
    @IsOptional()
    arabicName?: string;

    @IsOptional()
    @Expose()
    @ApiPropertyOptional()
    @IsNumber()
    @Min(0)
    @Max(100)
    refundPercentageOnCancellation?: number;

    @IsEmail({}, { message: 'invalid email' })
    @Expose()
    @IsOptional()
    email?: string;

    @ApiProperty()
    @Expose()
    @IsString()
    password: string;

    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @Expose()
    @IsOptional()
    phone?: string;

    @IsEnum(GenderType)
    @Expose()
    @IsOptional()
    gender?: GenderType;

    @IsString()
    @Expose()
    @IsOptional()
    avatar?: string;

    @Expose()
    @IsString()
    @IsOptional()
    appLanguage?: string;

    @Expose()
    @IsOptional()
    @IsString()
    firebaseUserId?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalID?: string;

    @IsString()
    @Expose()
    @IsOptional()
    taxIdURL?: string;

    @IsString()
    @Expose()
    @IsOptional()
    link?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    syndicateNumber?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalIdFrontURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalIdBackURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    syndicateIdURL?: string;

    @IsString()
    @Expose()
    @IsOptional()
    secretaryEmail?: string;

    @IsNumber()
    @Expose()
    @ApiProperty()
    @IsNotEmpty()
    gradeId: number;

    @IsArray()
    @Expose()
    @ApiProperty()
    @IsNotEmpty()
    specialityIds: number[];

    @Expose()
    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @ToInt()
    homeVisitFees?: number;

    @Expose()
    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @ToInt()
    onlineConsultationFees?: number;

    @Expose()
    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @ToInt()
    urgentReservationPercentage?: number;

    @Expose()
    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @ToInt()
    higherPricingPercentage?: number;

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    certificates?: string[];

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isActive?: boolean;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    canForwardToSecretary?: boolean;

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    languages?: AvailableLanguages[];

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    translations?: UserTranslation[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    doctorMemberships?: DoctorMembershipDto[];

    @IsString()
    @Expose()
    @IsOptional()
    instapayUrl?: string;
}
