/* eslint-disable max-classes-per-file */
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class DoctorScheduleSlotsDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsString()
    time: string;

    @ApiProperty()
    @Expose()
    @IsBoolean()
    isAvailable: boolean;
}

export class DoctorDayScheduleForPatientDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsBoolean()
    isEnabled: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    message?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => DoctorScheduleSlotsDto)
    slots?: DoctorScheduleSlotsDto[];
}
