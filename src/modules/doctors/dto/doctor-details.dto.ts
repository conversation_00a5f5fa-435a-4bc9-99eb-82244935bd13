'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ToInt } from '../../../decorators/transforms.decorator';
import { AvailableLanguages } from '../../../i18n/languageCodes';
import { ClinicDto } from '../../clinics/dto/clinic.dto';
import { GradeDto } from '../../grades/dto/grade.dto';
import { GetRatingDto } from '../../ratings/dto/get-rating.dto';
import { SpecialityDto } from '../../specialities/dto/speciality.dto';

export class DoctorDetailsDto extends AbstractDto {
    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalID?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    syndicateNumber?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalIdFrontURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalIdBackURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    syndicateIdURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    newSyndicateIdURL?: string;

    @IsNumber()
    @Expose()
    @ApiProperty()
    @IsNotEmpty()
    gradeId: number;

    @IsArray()
    @Expose()
    @ApiProperty()
    @IsNotEmpty()
    specialityIds: number[];

    @Expose()
    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @ToInt()
    homeVisitFees?: number;

    @Expose()
    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @ToInt()
    onlineConsultationFees?: number;

    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => SpecialityDto)
    specialities?: SpecialityDto[];

    @Expose()
    @IsOptional()
    @Type(() => GradeDto)
    grade?: GradeDto;

    @IsString()
    @Expose()
    @IsOptional()
    status?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    taxIdURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    link?: string;

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    certificates?: string[];

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    languages?: AvailableLanguages[];

    clinics?: ClinicDto[];
    ratings?: GetRatingDto[];
    isFavourite?: boolean;
}
