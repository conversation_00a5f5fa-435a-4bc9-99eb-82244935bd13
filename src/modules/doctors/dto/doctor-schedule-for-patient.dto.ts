import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class DoctorScheduleForPatientDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsBoolean()
    isEnabled?: boolean;

    @ApiProperty()
    @Expose()
    @IsBoolean()
    isActive?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    message?: string;

    @ApiProperty()
    @Expose()
    @IsString()
    day: string;

    @ApiProperty()
    @Expose()
    @IsString()
    from: string;

    @ApiProperty()
    @Expose()
    @IsString()
    to: string;
}
