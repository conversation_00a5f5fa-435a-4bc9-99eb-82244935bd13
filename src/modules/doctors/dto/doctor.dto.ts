import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsEnum,
    IsNumber,
    IsOptional,
    IsString,
    IsUrl,
    ValidateNested,
} from 'class-validator';

import {
    DEACTIVATION_REASON,
    SyndicateIdStatus,
} from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ToInt } from '../../../decorators/transforms.decorator';
import { AvailableLanguages } from '../../../i18n/languageCodes';
import { ClinicDto } from '../../clinics/dto/clinic.dto';
import { GradeDto } from '../../grades/dto/grade.dto';
import { DoctorMembershipDto } from '../../memberships/dto/doctor-membership.dto';
import { GetRatingDto } from '../../ratings/dto/get-rating.dto';
import { ScheduleDto } from '../../schedules/dto/schedule.dto';
import { ServiceProviderForDoctorDto } from '../../service-providers/dto/service-provider-for-doctor.dto';
import { SpecialityDto } from '../../specialities/dto/speciality.dto';
import { UserDto } from '../../users/dto/user.dto';
import { Column } from 'typeorm';

export class DoctorDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    website?: string;

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    @IsNumber()
    kashfPercentage?: number;

    @Expose()
    @ApiPropertyOptional()
    @IsNumber()
    refundPercentageOnCancellation?: number;

    @IsString()
    @Expose()
    @IsOptional()
    nationalID?: string;

    @IsString()
    @Expose()
    @IsOptional()
    nationalIdFrontURL?: string;

    @IsString()
    @Expose()
    @IsOptional()
    nationalIdBackURL?: string;

    @IsString()
    @Expose()
    @IsOptional()
    syndicateNumber?: string;

    @IsString()
    @Expose()
    @IsOptional()
    syndicateIdURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    newSyndicateIdURL?: string;

    @IsString()
    @Expose()
    @IsOptional()
    syndicateIdStatus?: SyndicateIdStatus;

    @IsString()
    @Expose()
    @IsOptional()
    taxIdURL?: string;

    @IsString()
    @Expose()
    @IsOptional()
    link?: string;

    @IsString()
    @Expose()
    @IsOptional()
    lastCheckIn?: Date;

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    certificates?: string[];

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    languages?: AvailableLanguages[];

    @IsString()
    @Expose()
    @IsOptional()
    bankAccountNumber?: string;

    @IsString()
    @Expose()
    @IsOptional()
    bankName?: string;

    @IsString()
    @Expose()
    @IsOptional()
    beneficiaryName?: string;

    @IsString()
    @Expose()
    @IsOptional()
    instapayUrl?: string;

    @IsString()
    @Expose()
    @IsOptional()
    bankSwiftCode?: string;

    @IsString()
    @Expose()
    @IsOptional()
    bankIBAN?: string;

    @IsString()
    @Expose()
    @IsOptional()
    walletNumber?: string;

    @IsString()
    @Expose()
    @IsOptional()
    walletProvider?: string;

    @IsNumber()
    @Expose()
    @IsOptional()
    walletBalance?: number;

    @Expose()
    @IsOptional()
    @ToInt()
    homeVisitFees?: number;

    @IsString()
    @Expose()
    @IsOptional()
    secretaryEmail?: string;

    @Expose()
    @IsOptional()
    @ToInt()
    urgentReservationPercentage?: number;

    @Expose()
    @IsOptional()
    @ToInt()
    higherPricingPercentage?: number;

    @Expose()
    @IsOptional()
    @ToInt()
    onlineConsultationFees?: number;

    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => SpecialityDto)
    specialities?: SpecialityDto[];

    @Expose()
    @IsOptional()
    @Type(() => GradeDto)
    grade?: GradeDto;

    @Expose()
    @IsOptional()
    @Type(() => UserDto)
    user?: UserDto;

    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => ClinicDto)
    clinics?: ClinicDto[];

    @ValidateNested()
    @Expose()
    @IsArray()
    @IsOptional()
    @Type(() => ServiceProviderForDoctorDto)
    serviceProviders?: ServiceProviderForDoctorDto[];

    @Expose()
    @IsOptional()
    @Type(() => ServiceProviderForDoctorDto)
    onlineWithServiceProvider?: ServiceProviderForDoctorDto;

    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => GetRatingDto)
    ratings?: GetRatingDto[];

    @IsBoolean()
    @Expose()
    @IsOptional()
    isFavourite?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    canForwardToSecretary?: boolean;

    @Expose()
    @IsOptional()
    status?: string;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isASAPHomeVisit?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    hasPendingKashfShare?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isASAPCalls?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isFreeCall?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isFreeVisit?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isAcceptCashOnhomeVisit?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isActive?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isBanned?: boolean;

    @IsEnum(DEACTIVATION_REASON)
    @Expose()
    @IsOptional()
    deactivation_reason?: DEACTIVATION_REASON;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isInCall?: boolean;

    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => ScheduleDto)
    schedules?: ScheduleDto[];

    consultationRequests?: any;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    doctorMemberships?: DoctorMembershipDto[];

    @IsString()
    @Expose()
    @IsOptional()
    arabicName?: string;
}
