import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { DoctorForAdminDto } from './doctor-for-admin.dto';

export class DoctorsPageDto {
    @ApiProperty({
        type: DoctorForAdminDto,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => DoctorForAdminDto)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: DoctorForAdminDto[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: DoctorForAdminDto[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
