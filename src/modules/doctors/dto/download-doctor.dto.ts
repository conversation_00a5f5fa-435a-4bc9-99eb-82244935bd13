import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class DownloadDoctorDto extends AbstractDto {
    @IsString()
    @Expose()
    name: string;

    @IsString()
    @Expose()
    syndicateId: string;

    @IsString()
    @Expose()
    specialities: string;

    @IsString()
    @Expose()
    grade: string;

    @IsString()
    @Expose()
    mobileNumber: string;

    @IsString()
    @IsOptional()
    @Expose()
    lastCheckIn?: string;
}
