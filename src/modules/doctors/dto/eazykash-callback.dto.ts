/* eslint-disable @typescript-eslint/tslint/config */
'use strict';

import { Expose } from 'class-transformer';
import { IsString } from 'class-validator';

export class EasyKashCallbackDTO {
    @IsString()
    @Expose()
    ProductCode: string;

    @IsString()
    @Expose()
    Amount: string;

    @IsString()
    @Expose()
    BuyerEmail: string;

    @IsString()
    @Expose()
    BuyerMobile: string;

    @IsString()
    @Expose()
    BuyerName: string;
}

/*
Sample body
Only needed property is ProductCode
{
  "ProductCode": "CHQ4668",
  "PaymentMethod": "Cash Through Fawry/Cash Through Aman/Credit & Debit Card/Mobile Wallet/Meeza/Qassatly/Cash Api",
  "ProductType": "Physical Product/Invoice/Event/Quick Payment/Quick Cash/Subscription/Custom Payment/Quick Qassatly/Fawry Payout/Booking",
  "Amount": "50.5",
  "BuyerEmail": "<EMAIL>",
  "BuyerMobile": "01111111111",
  "BuyerName": "<PERSON> Do<PERSON>",
  "Timestamp": "1626166791"
  "status": "success" ,
  "voucher": "32423432",
  "easykashRef":"3242143421",
  "VoucherData": "test"
}
*/
