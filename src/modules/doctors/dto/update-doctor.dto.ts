'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    Is<PERSON><PERSON>y,
    IsBoolean,
    IsN<PERSON>ber,
    IsOptional,
    IsString,
    IsUrl,
    <PERSON>,
    <PERSON>,
} from 'class-validator';

import { AvailableLanguages } from '../../../i18n/languageCodes';
import { DoctorMembershipDto } from '../../memberships/dto/doctor-membership.dto';
import { UpdateUserDto } from '../../users/dto/update-user.dto';
export class UpdateDoctorDto extends UpdateUserDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    website?: string;

    @IsOptional()
    @Expose()
    @ApiPropertyOptional()
    @IsNumber()
    @Min(0)
    @Max(100)
    kashfPercentage?: number;

    @IsOptional()
    @Expose()
    @ApiPropertyOptional()
    @IsNumber()
    @Min(0)
    @Max(100)
    refundPercentageOnCancellation?: number;

    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    gradeId: number;

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    specialityIds: number[];

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    homeVisitFees?: number;

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    onlineConsultationFees?: number;

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    urgentReservationPercentage?: number;

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    higherPricingPercentage?: number;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    canForwardToSecretary?: boolean;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isASAPCalls?: boolean;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isCheckedIn?: boolean;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isASAPHomeVisit?: boolean;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isFreeVisit?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isInCall?: boolean;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isFreeCall?: boolean;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isAcceptCashOnhomeVisit?: boolean;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    bankAccountNumber?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    bankName?: string;

    @IsString()
    @Expose()
    @IsOptional()
    beneficiaryName?: string;

    @IsString()
    @Expose()
    @IsOptional()
    instapayUrl?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    bankSwiftCode?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    bankIBAN?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    walletNumber?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    walletProvider?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalID?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalIdFrontURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    nationalIdBackURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    syndicateIdURL?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    taxIdURL?: string;

    @IsString()
    @Expose()
    @IsOptional()
    link?: string;

    @IsString()
    @Expose()
    @IsOptional()
    directLink?: string;

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    syndicateNumber?: string;

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    certificates?: string[];

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isActive?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isBanned?: boolean;

    @IsString()
    @Expose()
    @IsOptional()
    arabicName?: string;

    @IsArray()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    languages?: AvailableLanguages[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    doctorMemberships?: DoctorMembershipDto[];

    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    onlineStatusServiceProvider?: number;

    @IsString()
    @Expose()
    @IsOptional()
    secretaryEmail?: string;
}
