'use strict';

import { Expose, Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, IsObject, IsOptional, IsString } from 'class-validator';
import {
    <PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON>in<PERSON><PERSON>umn,
    <PERSON>inTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    OneToOne,
} from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserStatus } from '../../../common/constants/status';
import {
    DEACTIVATION_REASON,
    SyndicateIdStatus,
} from '../../../common/constants/types';
import { ColumnNumericTransformer } from '../../../shared/services/column.numeric.transform';
import { Clinic } from '../../clinics/entities/clinic.entity';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { FavouriteEntity } from '../../favourites/entities/favourite.entity';
import { Grade } from '../../grades/entities/grade.entity';
import { DoctorMembership } from '../../memberships/entities/doctor-membership.entity';
import { Membership } from '../../memberships/entities/membership.entity';
import { Transaction } from '../../payments/entities/transaction.entity';
import { ScheduleEntity } from '../../schedules/entities/schedule.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { Speciality } from '../../specialities/entities/speciality.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { DoctorServiceProviderCheckInLogs } from './doctor.service-provider.check-in.logs.entity';
import { SecretaryEntity } from '../../secretaries/entities/secretary.entity';
import { Package } from '../../packages/entities/package.entity';

@Entity('doctors')
export class DoctorEntity extends AbstractEntity {
    @Column({ nullable: true })
    @Expose()
    nationalID: string;

    @Column({ nullable: true })
    @Expose()
    nationalIdFrontURL: string;

    @Column({ nullable: true })
    @Expose()
    nationalIdBackURL: string;

    @Column({ nullable: true })
    @Expose()
    syndicateNumber: string;

    @Column({ nullable: true })
    @Expose()
    syndicateIdURL: string;

    @Column({ nullable: true })
    @Expose()
    newSyndicateIdURL: string;

    @Column({
        type: 'enum',
        enum: SyndicateIdStatus,
        default: SyndicateIdStatus.NOT_APPLIED,
    })
    @Expose()
    syndicateIdStatus?: SyndicateIdStatus;

    @Column({ nullable: true })
    @Expose()
    taxIdURL: string;

    @Column({ nullable: true })
    @Expose()
    link: string;

    @Column({ nullable: true })
    @Expose()
    directLink: string;

    @Column('simple-array', { nullable: true })
    @Expose()
    certificates: string[];

    @Column('simple-array', { nullable: true })
    @Expose()
    languages: string[];

    @Column({ default: 0 })
    @Expose()
    homeVisitFees: number;

    @Column({ default: 0 })
    @Expose()
    onlineConsultationFees: number;

    @Column({ default: 100 })
    @Expose()
    urgentReservationPercentage: number;

    @Column({ default: 100 })
    @Expose()
    higherPricingPercentage: number;

    @Column({ default: false })
    @Expose()
    isASAPHomeVisit: boolean;

    @Column({ nullable: true })
    @Expose()
    secretaryEmail?: string;

    @Column({ default: false })
    @Expose()
    isASAPCalls: boolean;

    @Column({ default: false })
    @Expose()
    isFreeCall: boolean;

    @Column({ default: false })
    @Expose()
    canForwardToSecretary: boolean;

    @Column({ default: false })
    @Expose()
    isFreeVisit: boolean;

    @Column({ default: false })
    @Expose()
    isAcceptCashOnhomeVisit: boolean;

    @Column({ default: false })
    @Expose()
    isActive: boolean;

    @Column({ default: false })
    @Expose()
    isBanned: boolean;

    @Column({ nullable: true, enum: DEACTIVATION_REASON, type: 'enum' })
    @Expose()
    deactivationReason?: DEACTIVATION_REASON;

    @Column({ default: false })
    @Expose()
    isInCall: boolean;

    @Column({ default: false })
    @Expose()
    hasPendingKashfShare: boolean;

    @Column({ type: 'enum', enum: UserStatus, default: UserStatus.OFFLINE })
    @Expose()
    status: string;

    @Column({ nullable: true })
    @Expose()
    bankAccountNumber: string;

    @Column({ nullable: true })
    @Expose()
    bankName: string;

    @Column({ nullable: true })
    @Expose()
    beneficiaryName: string;

    @Column({ nullable: true })
    @Expose()
    instapayUrl: string;

    @Column({ nullable: true })
    @Expose()
    bankSwiftCode: string;

    @Column({ nullable: true })
    @Expose()
    bankIBAN: string;

    @Column({ nullable: true })
    @Expose()
    walletNumber: string;

    @Column({ nullable: true })
    @Expose()
    walletProvider: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    website?: string;

    @Column({
        type: 'decimal',
        nullable: true,
        default: 0,
        precision: 16,
        scale: 2,
    })
    @Expose()
    walletBalance: number;

    @Column({ nullable: true })
    @Expose()
    arabicName: string;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    kashfPercentage: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    refundPercentageOnCancellation: number;

    @Column({
        type: 'timestamp without time zone',
        nullable: true,
        name: 'offline_at',
    })
    @Expose()
    @IsOptional()
    offlineAt!: Date;

    @ManyToOne((_type) => Grade, (grade) => grade.doctors, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => Grade)
    grade?: Grade;

    @ManyToMany(() => Speciality, (speciality) => speciality.doctors, {
        cascade: true,
        onUpdate: 'CASCADE',
    })
    @JoinTable({ name: 'doctor_specialities_speciality' })
    specialities?: Speciality[];

    @OneToOne((_type) => UserEntity, (user) => user.doctor, {
        onDelete: 'CASCADE',
        cascade: ['insert'],
    })
    @JoinColumn()
    user: UserEntity;

    @OneToMany((_type) => Clinic, (clinic) => clinic.doctor, {
        cascade: true,
    })
    clinics: Clinic[];

    @OneToMany((_type) => ScheduleEntity, (schedule) => schedule.doctor, {
        onDelete: 'CASCADE',
    })
    schedules: ScheduleEntity[];

    @OneToMany(
        (_type) => ConsultationRequestEntity,
        (consultationRequest) => consultationRequest.doctor,
        {
            cascade: true,
        },
    )
    consultationRequests: ConsultationRequestEntity[];

    @OneToMany((_type) => Transaction, (transaction) => transaction.doctor, {
        cascade: true,
    })
    transactions: Transaction[];

    @OneToMany(() => FavouriteEntity, (favourite) => favourite.doctor, {
        onDelete: 'CASCADE',
    })
    favourites: FavouriteEntity[];

    @ManyToMany(
        () => ServiceProvider,
        (serviceProvider) => serviceProvider.doctors,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => ServiceProvider)
    serviceProviders?: ServiceProvider[];

    @ManyToOne(
        () => ServiceProvider,
        (serviceProvider) => serviceProvider.onlineDoctors,
        {
            nullable: true,
        },
    )
    @Expose()
    @IsOptional()
    @Type(() => ServiceProvider)
    @JoinColumn({ name: 'online_with_service_provider_id' })
    onlineWithServiceProvider?: ServiceProvider;

    @OneToMany(
        (_type) => DoctorServiceProviderCheckInLogs,
        (doctorServiceProviderCheckInLogs) =>
            doctorServiceProviderCheckInLogs.doctor,
        {
            cascade: true,
            onDelete: 'CASCADE',
        },
    )
    doctorCheckinLogs: DoctorServiceProviderCheckInLogs[];

    @ManyToMany(() => Membership, (membership) => membership.doctors)
    memberships?: Membership[];

    @OneToMany(
        () => DoctorMembership,
        (doctorMembership) => doctorMembership.doctor,
    )
    doctorMemberships?: DoctorMembership[];

    @OneToMany(() => SecretaryEntity, (secretary) => secretary.doctor)
    secretaries: SecretaryEntity[];

    @OneToMany(() => Package, (pkg) => pkg.doctor)
    packages: Package[];
}
