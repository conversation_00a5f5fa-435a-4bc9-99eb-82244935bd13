'use strict';

import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { DoctorEntity } from './doctor.entity';

@Entity('doctors_service_providers_check_in_logs')
export class DoctorServiceProviderCheckInLogs extends AbstractEntity {
    @Column({ default: false })
    @Expose()
    isCheckedIn: boolean;

    @ManyToOne(
        (_type) => DoctorEntity,
        (doctorEntity) => doctorEntity.doctorCheckinLogs,
        {
            onDelete: 'CASCADE',
        },
    )
    doctor: DoctorEntity;

    @ManyToOne(
        (_type) => ServiceProvider,
        (serviceProvider) => serviceProvider.doctorCheckinLogs,
        {
            onDelete: 'CASCADE',
        },
    )
    serviceProvider: ServiceProvider;
}
