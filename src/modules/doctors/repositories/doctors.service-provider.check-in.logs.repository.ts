import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { DoctorServiceProviderCheckInLogs } from '../entities/doctor.service-provider.check-in.logs.entity';

@Injectable()
export class DoctorServiceProviderCheckInLogsRepository extends Repository<
    DoctorServiceProviderCheckInLogs
> {
    constructor(dataSource: DataSource) {
        super(DoctorServiceProviderCheckInLogs, dataSource.createEntityManager());
    }
}
