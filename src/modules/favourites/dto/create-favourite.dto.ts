'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreateFavouriteDto extends AbstractDto {
    @IsNumber()
    @ApiProperty()
    @Expose()
    doctorId: number;

    @ApiPropertyOptional()
    userId: number;
}
