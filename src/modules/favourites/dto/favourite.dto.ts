'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { DoctorDetailsDto } from '../../doctors/dto/doctor-details.dto';
import { UserDto } from '../../users/dto/user.dto';

export class FavouriteDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @Type(() => DoctorDetailsDto)
    doctor?: DoctorDetailsDto;

    @ApiProperty()
    @Expose()
    @Type(() => UserDto)
    user?: UserDto;
}
