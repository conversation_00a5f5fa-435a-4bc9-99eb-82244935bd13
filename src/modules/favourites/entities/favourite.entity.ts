'use strict';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { UserEntity } from '../../users/entities/user.entity';
@Entity('favourites')
export class FavouriteEntity extends AbstractEntity {
    @ManyToOne((_type) => Doctor<PERSON><PERSON><PERSON>, (doctor) => doctor.favourites, {
        onDelete: 'CASCADE',
    })
    doctor: DoctorEntity;

    @ManyToOne(() => UserEntity, {
        onDelete: 'CASCADE',
    })
    @JoinColumn()
    user: UserEntity;
}
