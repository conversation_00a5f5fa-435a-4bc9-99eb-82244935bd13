import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { DoctorEntity } from '../doctors/entities/doctor.entity';
import { UserEntity } from '../users/entities/user.entity';
import { CreateFavouriteDto } from './dto/create-favourite.dto';
import { FavouriteDto } from './dto/favourite.dto';
import { FavouriteEntity } from './entities/favourite.entity';

@Injectable()
export class FavouriteMapper extends AbstractMapper<
    FavouriteDto,
    FavouriteEntity
> {
    constructor() {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<FavouriteEntity>,
        sourceObject: CreateFavouriteDto,
    ): FavouriteEntity {
        const userEntity = super.fromDTOToEntity(destination, sourceObject);
        const favourite = new DoctorEntity();
        favourite.id = sourceObject.doctorId;
        userEntity.doctor = favourite;

        const user = new UserEntity();
        user.id = sourceObject.userId;
        userEntity.user = user;

        return userEntity;
    }

    fromEntityToDTO(
        destination: ClassType<FavouriteDto>,
        sourceObject: FavouriteEntity,
    ): FavouriteDto {
        return super.fromEntityToDTO(destination, sourceObject);
    }
}
