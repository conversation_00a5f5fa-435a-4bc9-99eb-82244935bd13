import { forwardRef, Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { AuthModule } from '../auth/auth.module';
import { ClinicsMapper } from '../clinics/mappers/clinics.mapper';
import { DoctorsMapper } from '../doctors/doctors.mapper';
import { DoctorsModule } from '../doctors/doctors.module';
import { SchedulesRepository } from '../schedules/repositories/schedules.repository';
import { SchedulesModule } from '../schedules/schedules.module';
import { UsersModule } from '../users/users.module';
import { FavouriteMapper } from './favourites.mapper';
import { FavouriteService } from './favourites.service';
import { FavouriteRepository } from './repositories/favourites.repository';

@Module({
    imports: [
        SharedModule,
        UsersModule,
        AuthModule,
        forwardRef(() => SchedulesModule),
        forwardRef(() => DoctorsModule),
    ],
    exports: [FavouriteService],
    providers: [
        FavouriteService,
        FavouriteMapper,
        DoctorsMapper,
        ClinicsMapper,
        FavouriteRepository,
        SchedulesRepository,
    ],
})
export class FavouriteModule {}
