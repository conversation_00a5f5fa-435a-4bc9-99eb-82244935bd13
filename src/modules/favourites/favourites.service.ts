import {
    forwardRef,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { DoctorsMapper } from '../doctors/doctors.mapper';
import { DoctorsService } from '../doctors/doctors.service';
import { DoctorDto } from '../doctors/dto/doctor.dto';
import { UsersMapper } from '../users/users.mapper';
import { UsersService } from '../users/users.service';
import { CreateFavouriteDto } from './dto/create-favourite.dto';
import { FavouriteDto } from './dto/favourite.dto';
import { FavouriteEntity } from './entities/favourite.entity';
import { FavouriteMapper } from './favourites.mapper';
import { FavouriteRepository } from './repositories/favourites.repository';
import { FavouriteMessagesKeys } from './translate.enum';

@Injectable()
export class FavouriteService {
    constructor(
        public readonly favouriteRepository: FavouriteRepository,
        public readonly favouriteMapper: FavouriteMapper,
        public readonly usersService: UsersService,
        public readonly usersMapper: UsersMapper,
        private readonly i18n: I18nService,
        public readonly helperService: HelperService,
        public readonly doctorsMapper: DoctorsMapper,
        @Inject(forwardRef(() => DoctorsService))
        private readonly doctorsService: DoctorsService,
    ) {}

    async addOrRemoveFavourite(
        createFavouriteDto: CreateFavouriteDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const doctor = await this.doctorsService.findDoctor(
            createFavouriteDto.doctorId,
        );
        if (!doctor) {
            throw new HttpException(
                {
                    message: 'doctor is not exist',
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        const favouriteExsists = await this.favouriteRepository.findOne({
            where: {
                doctor: {
                    id: createFavouriteDto.doctorId,
                },
                user: {
                    id: createFavouriteDto.userId,
                },
            },
        });
        if (favouriteExsists) {
            return this.removeFavourite(createFavouriteDto, lang);
        }
        const favouriteEntity = this.favouriteMapper.fromDTOToEntity(
            FavouriteEntity,
            createFavouriteDto,
        );

        const favourite = this.favouriteRepository.create(favouriteEntity);
        await this.favouriteRepository.save(favourite);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                FavouriteMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async getUserFavourite(
        userId: number,
        lang: string,
    ): Promise<FavouriteDto[]> {
        const favourites = await this.favouriteRepository
            .createQueryBuilder('favourite')
            .leftJoinAndSelect('favourite.user', 'user')
            .leftJoinAndSelect('favourite.doctor', 'doctor')
            .leftJoinAndSelect('doctor.schedules', 'schedules')
            .leftJoinAndSelect('doctor.clinics', 'clinics')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .where('user.id = :id', { id: userId })
            .select([
                'favourite.id',
                'favourite.createdAt',
                'doctor.id',
                'doctor.homeVisitFees',
                'doctor.onlineConsultationFees',
                'doctor.isInCall',
                'doctor.isActive',
                'doctor.isASAPCalls',
                'doctor.isASAPHomeVisit',
                'doctorUser.id',
                'doctorUser.name',
                'doctorUser.role',
                'doctorUser.avatar',
                'doctorUser.averageRating',
                'clinics.id',
                'schedules.id',
                'schedules.fees',
                'clinics.fees',
                'specialities',
                'grade',
                'specialityTranslations',
                'gradeTranslations',
            ])
            .getMany();

        return Promise.all(
            favourites.map(async (favourite: any) => {
                const doctor = this.doctorsMapper.fromEntityToDoctorDetailsDTO(
                    DoctorDto,
                    favourite.doctor,
                    lang,
                );
                const feesMin = Math.min(
                    ...this.doctorsService.getDoctorFees(favourite.doctor),
                );
                const feesMax = Math.max(
                    ...this.doctorsService.getDoctorFees(favourite.doctor),
                );
                const status = await this.doctorsService.getDoctorStatus(
                    favourite.doctor.id,
                    favourite.doctor,
                );
                favourite.doctor = {
                    ...doctor,
                    feesMin,
                    feesMax,
                    status,
                    isFavourite: true,
                };
                this.favouriteMapper.fromEntityToDTO(FavouriteDto, favourite);
                return favourite;
            }),
        );
    }

    async removeFavourite(
        removeFavouriteDto: CreateFavouriteDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        await this.favouriteRepository.delete({
            doctor: {
                id: removeFavouriteDto.doctorId,
            },
        });
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                FavouriteMessagesKeys.DELETED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }
}
