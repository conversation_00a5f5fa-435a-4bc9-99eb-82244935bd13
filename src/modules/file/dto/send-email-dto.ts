import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsBooleanString,
    IsEnum,
    IsNumberString,
    IsString,
} from 'class-validator';

import { AttachementType } from '../../../common/constants/types';

export class SendEmailDto {
    @ApiProperty()
    @Expose()
    @IsNumberString()
    requestId: number;

    @ApiProperty()
    @Expose()
    @IsString()
    subject: string;

    @ApiProperty()
    @Expose()
    @IsEnum(AttachementType)
    attachmentType: AttachementType;

    @ApiProperty()
    @Expose()
    @IsBooleanString()
    forwardToSecretary?: boolean;
}
