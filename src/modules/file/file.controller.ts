import {
    Body,
    Controller,
    Param,
    Post,
    Req,
    UploadedFile,
    UseInterceptors,
} from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>A<PERSON>, ApiHeader, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { File } from 'multer';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { Roles } from '../../decorators/roles.decorator';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { localFileInterceptor } from '../../interceptors/LocalFileInterceptor';
import { SendEmailDto } from './dto/send-email-dto';
import { FileService } from './file.service';

@Controller('files')
@ApiTags('files')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(HeaderInterceptor)
@ApiBearerAuth()
export class FileController {
    constructor(private fileService: FileService) {}

    @Post('/upload/doctors/:doctorId')
    @UseInterceptors(
        localFileInterceptor({
            filter: /\.(jpg|jpeg|png|gif)$/,
            destination: './upload/doctors/email',
            fieldName: 'file',
        }),
    )
    @Roles(RoleType.PATIENT)
    async uploadFile(
        @Req() req: Request,
        @Param('doctorId') doctorId: number,
        @UploadedFile() file: File,
        @I18nLang() lang: string,
    ) {
        return this.fileService.sendEmailWithAttachment(
            req.user.id,
            doctorId,
            file,
            lang,
        );
    }

    @Post('/send/email')
    @UseInterceptors(
        localFileInterceptor({
            filter: /\.(jpg|jpeg|png|gif)$/,
            destination: './upload/email',
            fieldName: 'file',
        }),
    )
    // @Roles(RoleType.DOCTOR)
    async uploadAttachment(
        @UploadedFile() file: File,
        @Body() sendEmailDto: SendEmailDto,
    ) {
        return this.fileService.sendAttachment(sendEmailDto, file);
    }
}
