import { <PERSON>du<PERSON> } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { ConsultationRequestsRepository } from '../consultation-requests/repositories/consultation-requests.repository';
import { DoctorsRepository } from '../doctors/repositories/doctors.repository';
import { PatientsRepository } from '../patients/repositories/patients.repository';
import { FileController } from './file.controller';
import { FileService } from './file.service';

@Module({
    imports: [SharedModule],
    controllers: [FileController],
    exports: [FileService],
    providers: [
        FileService,
        DoctorsRepository,
        PatientsRepository,
        ConsultationRequestsRepository,
    ],
})
export class FileModule {}
