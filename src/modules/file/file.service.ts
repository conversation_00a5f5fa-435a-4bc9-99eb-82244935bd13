import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import * as moment from 'moment/moment';
import { File } from 'multer';
import { I18nService } from 'nestjs-i18n';

import { AttachementType, GenderType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { EmailService } from '../../shared/services/email.service';
import { DebugLogger } from '../../shared/services/logger.service';
import { SimpleEmailBuilder } from '../../shared/utils/simple-email-builder';
import { AuthMessagesKeys } from '../auth/translate.enum';
import { ConsultationRequestsRepository } from '../consultation-requests/repositories/consultation-requests.repository';
import { DoctorsRepository } from '../doctors/repositories/doctors.repository';
import { PatientsRepository } from '../patients/repositories/patients.repository';
import { ServiceProviderMessagesKeys } from '../service-providers/translate.enum';
import { SendEmailDto } from './dto/send-email-dto';

@Injectable()
export class FileService {
    constructor(
        private readonly emailService: EmailService,
        private readonly doctorsRepository: DoctorsRepository,
        private readonly i18n: I18nService,
        private readonly patientsRepository: PatientsRepository,
        private readonly consultationRepository: ConsultationRequestsRepository,
        private readonly logger: DebugLogger,
    ) {}

    async sendEmailWithAttachment(
        patientUserId: number,
        doctorId: number,
        file: File,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const doctor = await this.doctorsRepository.findOne({
            where: {
                id: doctorId,
            },
            relations: ['user'],
        });

        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.DOCTOR_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const patient = await this.patientsRepository.findOne({
            where: {
                user: {
                    id: patientUserId,
                },
            },
            relations: ['user'],
        });

        if (!patient) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.PATIENT_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.emailService.sendEmailWithAttachment(
            doctor.user.email,
            'Attachment',
            new SimpleEmailBuilder()
                .addLine(`Dear ${doctor.user.name}`)
                .addBreak()
                .addLine(
                    `${
                        patient.user.gender === GenderType.MALE ? 'Mr' : 'Ms'
                    }. ${
                        patient.user.name
                    } has submitted an attachment for you .`,
                )
                .addBreak()
                .addBreak()
                .addLine('Thank you.').content,
            file,
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.DELETE_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async sendAttachment(
        sendEmailDto: SendEmailDto,
        file: File,
    ): Promise<void> {
        const request = await this.consultationRepository.findOne({
            where: {
                id: sendEmailDto.requestId,
            },
            relations: ['doctor', 'patient', 'doctor.user', 'patient.user'],
        });

        const attachmentTypeFromRequest = sendEmailDto.attachmentType;
        let attachmentType = '';

        switch (attachmentTypeFromRequest) {
            case AttachementType.PRESCRIPTION:
                attachmentType = 'Prescription';
                break;
            case AttachementType.X_RAY:
                attachmentType = 'X-Rays';
                break;
            case AttachementType.LAP_TEST:
                attachmentType = 'LAP Test';
        }

        if (
            sendEmailDto.forwardToSecretary &&
            !!request.doctor.secretaryEmail
        ) {
            await this.emailService.sendEmailWithAttachment(
                request.doctor.secretaryEmail,
                sendEmailDto.subject,
                new SimpleEmailBuilder()
                    .addLine('Dears')
                    .addBreak()
                    .addLine(
                        `${
                            request.doctor.user.gender === GenderType.MALE
                                ? 'Mr'
                                : 'Ms'
                        }. ${
                            request.doctor.user.name
                        } has submitted an ${attachmentType} as attachment.`,
                    )
                    .addLine('Patient Information:')
                    .addLine(`Name: ${request.patient.user.name}`)
                    .addLine(
                        `Request Date: ${moment(request.date).format(
                            'DD-MM-YYYY hh:mm A',
                        )}`,
                    )
                    .addLine(`KASHF ID: ${request.patient.user.id}`)
                    .addBreak()
                    .addBreak()
                    .addLine('Thank you.').content,
                file,
            );
        }

        await this.emailService.sendEmailWithAttachment(
            request.patient.user.email,
            sendEmailDto.subject,
            new SimpleEmailBuilder()
                .addLine(`Dear ${request.patient.user.name}`)
                .addBreak()
                .addLine(
                    `${
                        request.doctor.user.gender === GenderType.MALE
                            ? 'Mr'
                            : 'Ms'
                    }. ${
                        request.doctor.user.name
                    } has submitted an ${attachmentType} as attachment for you.`,
                )
                .addBreak()
                .addLine('Thank you.').content,
            file,
        );
    }
}
