'use strict';

import {
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { GovernoratesDto } from './dto/governorates.dto';
import { GovernoratesService } from './governorates.service';

@Controller('governorates')
@ApiTags('governorates')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(HeaderInterceptor)
@ApiBearerAuth()
export class CitiesController {
    constructor(private governoratesService: GovernoratesService) {}

    @Get('')
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'list governorates',
        type: [GovernoratesDto],
    })
    listGovernorates(@I18nLang() lang: string): Promise<GovernoratesDto[]> {
        return this.governoratesService.listGovernorates(lang);
    }
}
