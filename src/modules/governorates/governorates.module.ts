import { Module } from '@nestjs/common';

import { CitiesController } from './governorates.controller';
import { GovernoratesService } from './governorates.service';
import { GovernoratesRepository } from './repositories/governorates.repository';

@Module({
    controllers: [CitiesController],
    exports: [GovernoratesService],
    providers: [GovernoratesService, GovernoratesRepository],
})
export class GovernoratesModule {
}
