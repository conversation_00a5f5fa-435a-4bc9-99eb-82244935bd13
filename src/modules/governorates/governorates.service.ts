import { Injectable } from '@nestjs/common';

import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { GovernoratesDto } from './dto/governorates.dto';
import { GovernoratesRepository } from './repositories/governorates.repository';

@Injectable()
export class GovernoratesService {
    constructor(
        public readonly governoratesRepository: GovernoratesRepository,
    ) {}

    async listGovernorates(lang: string): Promise<GovernoratesDto[]> {
        const query = this.governoratesRepository
            .createQueryBuilder('governorate')
            .select([
                'governorate.id',
                'governorate.name',
                'governorate.nameEn',
            ]);
        let governorates: any = await query.getMany();
        governorates = governorates.map((governorate) => {
            if (lang === AvailableLanguageCodes.en) {
                governorate.name = governorate.nameEn;
            }
            delete governorate.nameEn;
            return governorate;
        });
        return governorates;
    }
}
