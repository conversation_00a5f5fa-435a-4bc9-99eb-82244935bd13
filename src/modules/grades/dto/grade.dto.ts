'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { DoctorDto } from '../../doctors/dto/doctor.dto';
import { GradeTranslation } from '../entities/grade-translation.entity';

export class GradeDto extends AbstractDto {
    @ApiPropertyOptional()
    @IsString()
    @Expose()
    @IsOptional()
    title?: string;

    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => DoctorDto)
    doctors?: DoctorDto[];

    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => GradeTranslation)
    translations?: GradeTranslation[];
}
