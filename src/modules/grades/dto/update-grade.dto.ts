import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { GradeTranslation } from '../entities/grade-translation.entity';

export class UpdateGradeDto extends AbstractDto {
    @ApiPropertyOptional({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @Type(() => GradeTranslation)
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: GradeTranslation[];
}
