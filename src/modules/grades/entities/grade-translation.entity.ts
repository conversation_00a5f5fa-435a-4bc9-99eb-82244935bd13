import { Expose, Type } from 'class-transformer';
import { IsObject, IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Grade } from './grade.entity';

@Entity('grades_translations')
export class GradeTranslation extends AbstractEntity {
    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    title?: string;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    languageCode?: string;

    @ManyToOne((_type) => Grade, (grade) => grade.translations, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => Grade)
    grade?: Grade;
}
