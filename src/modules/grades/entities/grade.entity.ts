import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional } from 'class-validator';
import { Entity, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { GradeTranslation } from './grade-translation.entity';

@Entity('grades')
export class Grade extends AbstractEntity {
    @OneToMany(
        (_type) => GradeTranslation,
        (gradeTranslation) => gradeTranslation.grade,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => GradeTranslation)
    translations?: GradeTranslation[];

    @OneToMany((_type) => DoctorEntity, (doctor) => doctor.grade, {
        cascade: true,
    })
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => DoctorEntity)
    doctors?: DoctorEntity[];
}
