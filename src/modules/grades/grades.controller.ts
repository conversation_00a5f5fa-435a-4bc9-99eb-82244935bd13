'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    Headers,
    Param,
    Post,
    Put,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { RoleType, SourceType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateGradeDto } from './dto/create-grade.dto';
import { GradeDto } from './dto/grade.dto';
import { UpdateGradeDto } from './dto/update-grade.dto';
import { GradesService } from './grades.service';

@Controller('grades')
@ApiTags('grades')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class GradesController {
    constructor(private gradesService: GradesService) {}

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get grade',
        type: GradeDto,
    })
    async getGrade(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<GradeDto> {
        return this.gradesService.getGrade(id, lang);
    }

    @Get()
    @ApiResponse({
        description: 'Get all grades',
        type: [GradeDto],
    })
    getAllGrades(
        @Headers('source-type') source: SourceType,
        @I18nLang() lang: string,
    ): Promise<GradeDto[]> {
        if (source && source === SourceType.ADMIN) {
            return this.gradesService.getAllGradesForAdmin();
        }
        return this.gradesService.getAllGrades(lang);
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create grade',
        type: CreateOperationsResponse,
    })
    createGrade(
        @Body() createGradeDto: CreateGradeDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.gradesService.createGrade(createGradeDto, lang);
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update grade',
        type: BasicOperationsResponse,
    })
    async updateGrade(
        @Param('id') id: number,
        @Body() updateGradeDto: UpdateGradeDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.gradesService.updateGrade(id, updateGradeDto, lang);
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete grade',
        type: BasicOperationsResponse,
    })
    async deleteGrade(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.gradesService.deleteGrade(id, lang);
    }
}
