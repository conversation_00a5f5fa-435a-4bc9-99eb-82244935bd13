import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreateGradeDto } from './dto/create-grade.dto';
import { GradeDto } from './dto/grade.dto';
import { UpdateGradeDto } from './dto/update-grade.dto';
import { Grade } from './entities/grade.entity';

type GradeDTOs = CreateGradeDto | UpdateGradeDto | GradeDto;
@Injectable()
export class GradesMapper extends AbstractMapper<GradeDTOs, Grade> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<Grade>,
        sourceObject: GradeDTOs,
    ): Grade {
        const gradeEntity = super.fromDTOToEntity(destination, sourceObject);
        this.helperService.removeEmptyKeys(gradeEntity);
        return gradeEntity;
    }

    fromEntityToDTO(
        destination: ClassType<GradeDto>,
        sourceObject: Grade,
    ): GradeDto {
        const gradeDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(gradeDto);
        return gradeDto;
    }
}
