import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { GradesController } from './grades.controller';
import { GradesMapper } from './grades.mapper';
import { GradesService } from './grades.service';
import { GradesTranslationsRepository } from './repositories/grades-translations.repository';
import { GradesRepository } from './repositories/grades.repository';

@Module({
    imports: [SharedModule],
    controllers: [GradesController],
    exports: [GradesService],
    providers: [
        GradesService,
        GradesMapper,
        GradesRepository,
        GradesTranslationsRepository,
    ],
})
export class GradesModule {}
