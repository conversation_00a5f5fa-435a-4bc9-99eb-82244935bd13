import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { CreateGradeDto } from './dto/create-grade.dto';
import { GradeDto } from './dto/grade.dto';
import { UpdateGradeDto } from './dto/update-grade.dto';
import { GradeTranslation } from './entities/grade-translation.entity';
import { Grade } from './entities/grade.entity';
import { GradesMapper } from './grades.mapper';
import { GradesTranslationsRepository } from './repositories/grades-translations.repository';
import { GradesRepository } from './repositories/grades.repository';
import { GradeMessagesKeys } from './translate.enum';

@Injectable()
export class GradesService {
    constructor(
        private readonly gradesRepository: GradesRepository,
        private readonly gradesTranslationsRepository: GradesTranslationsRepository,
        private readonly gradesMapper: GradesMapper,
        private readonly i18n: I18nService,
        private readonly helperService: HelperService,
    ) { }

    async getGrade(id: number, lang: string): Promise<GradeDto> {
        const grade = await this.gradesRepository.findOne({
            where: { id },
            relations: ['translations'],
        });
        if (!grade) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        GradeMessagesKeys.GRADE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.gradesMapper.fromEntityToDTO(GradeDto, grade);
    }

    async getAllGrades(lang: string): Promise<GradeDto[]> {
        const gradesEntities = await this.gradesRepository
            .createQueryBuilder('grade')
            .leftJoinAndSelect(
                'grade.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .getMany();

        const gradesDtos = gradesEntities.map((gradeEntity) =>
            this.gradesMapper.fromEntityToDTO(GradeDto, gradeEntity),
        );

        return gradesDtos
            .filter((grade) => grade.translations.length > 0)
            .map((grade) => ({
                id: grade.id,
                title: grade.translations[0].title,
            }));
    }

    async getAllGradesForAdmin(): Promise<GradeDto[]> {
        const grades = await this.gradesRepository.find({
            relations: ['translations'],
        });
        return grades.map((grade) =>
            this.gradesMapper.fromEntityToDTO(GradeDto, grade),
        );
    }

    async createGrade(
        createGradeDto: CreateGradeDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const gradeEntity = this.gradesMapper.fromDTOToEntity(
            Grade,
            createGradeDto,
        );
        const grade = this.gradesRepository.create(gradeEntity);
        const createdGrade = await this.gradesRepository.save(grade);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                GradeMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdGrade.id,
        };
    }

    async updateGrade(
        id: number,
        updateGradeDto: UpdateGradeDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const grade = await this.gradesRepository.findOne({ where: { id } });

        if (!grade) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        GradeMessagesKeys.GRADE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },

                HttpStatus.NOT_FOUND,
            );
        }

        const gradeEntity = this.gradesMapper.fromDTOToEntity(
            Grade,
            updateGradeDto,
        );

        if (gradeEntity.translations) {
            await this.updateTranslation(id, gradeEntity.translations);
            delete gradeEntity.translations;
        }
        await this.gradesRepository.update({ id }, gradeEntity);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                GradeMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async deleteGrade(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const grade = await this.gradesRepository.findOne({ where: { id } });

        if (!grade) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        GradeMessagesKeys.GRADE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.gradesRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                GradeMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async updateTranslation(
        parentId: number,
        updatedTranslation: GradeTranslation[],
    ): Promise<void> {
        await Promise.all(
            updatedTranslation.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);
                await this.gradesTranslationsRepository.update(
                    {
                        grade: { id: parentId },
                        languageCode: translation.languageCode,
                    },
                    translation,
                );
            }),
        );
    }
}
