import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsEmail,
    IsNotEmpty,
    IsOptional,
    IsPhoneNumber,
    IsString,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export abstract class AbstractHelpCenterCreateDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional()
    @Expose()
    @IsEmail({}, { message: 'invalid email' })
    @IsOptional()
    email?: string;

    @ApiProperty()
    @Expose()
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    mobileNumber: string;
}
