import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEmail, IsOptional, IsPhoneNumber, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export abstract class AbstractHelpCenterDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    name?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsEmail({}, { message: 'invalid email' })
    @IsOptional()
    email?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @IsOptional()
    mobileNumber?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    message?: string;
}
