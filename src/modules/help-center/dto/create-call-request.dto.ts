import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

import { AbstractHelpCenterCreateDto } from './abstract-help-center-create.dto';

export class CreateCallRequestDto extends AbstractHelpCenterCreateDto {
    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    message?: string;
}
