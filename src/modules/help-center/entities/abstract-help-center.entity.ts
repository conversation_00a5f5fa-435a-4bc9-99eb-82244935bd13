import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { Column } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';

export abstract class AbstractHelpCenterEntity extends AbstractEntity {
    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    email?: string;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    mobileNumber?: string;
}
