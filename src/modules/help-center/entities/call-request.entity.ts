import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { Column, Entity } from 'typeorm';

import { AbstractHelpCenterEntity } from './abstract-help-center.entity';

@Entity('call_requests')
export class CallRequest extends AbstractHelpCenterEntity {
    @Column({ type: 'text', nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    message?: string;
}
