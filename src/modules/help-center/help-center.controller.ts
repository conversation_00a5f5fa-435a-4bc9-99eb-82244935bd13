'use strict';

import { Body, Controller, Post, UseInterceptors } from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateCallRequestDto } from './dto/create-call-request.dto';
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { HelpCenterService } from './help-center.service';

@Controller('help-center')
@ApiTags('help-center')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(HeaderInterceptor)
@ApiBearerAuth()
export class HelpCenterController {
    constructor(private helpCenterService: HelpCenterService) {}

    /*********************** CRUD Operations *****************************/
    @Post('call-requests')
    @Roles(RoleType.SERVICE_PROVIDER)
    @ApiResponse({
        description: 'Create call request',
        type: CreateOperationsResponse,
    })
    async createCallRequest(
        @Body() createCallRequestDto: CreateCallRequestDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.helpCenterService.createCallRequest(
            createCallRequestDto,
            lang,
        );
    }

    @Post('feedbacks')
    @Roles(RoleType.SERVICE_PROVIDER)
    @ApiResponse({
        description: 'Create feedback',
        type: CreateOperationsResponse,
    })
    async createFeedback(
        @Body() createFeedbackDto: CreateFeedbackDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.helpCenterService.createFeedback(createFeedbackDto, lang);
    }
}
