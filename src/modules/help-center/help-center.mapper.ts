import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { AbstractHelpCenterDto } from './dto/abstract-help-center.dto';
import { CreateCallRequestDto } from './dto/create-call-request.dto';
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { CallRequest } from './entities/call-request.entity';
import { Feedback } from './entities/feedback.entity';

type HelpCenterEntities = CallRequest | Feedback;
type HelpCenterDtos =
    | CreateCallRequestDto
    | CreateFeedbackDto
    | AbstractHelpCenterDto;

@Injectable()
export class HelpCenterMapper extends AbstractMapper<
    HelpCenterDtos,
    HelpCenterEntities
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<HelpCenterEntities>,
        sourceObject: HelpCenterDtos,
    ): HelpCenterEntities {
        const helpCenterEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(helpCenterEntity);
        return helpCenterEntity;
    }

    fromEntityToDTO(
        destination: ClassType<AbstractHelpCenterDto>,
        sourceObject: HelpCenterEntities,
    ): AbstractHelpCenterDto {
        const helpCenterDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(helpCenterDto);
        return helpCenterDto;
    }
}
