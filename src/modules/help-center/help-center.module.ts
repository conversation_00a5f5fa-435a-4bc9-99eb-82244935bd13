import { Modu<PERSON> } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { HelpCenterController } from './help-center.controller';
import { HelpCenterMapper } from './help-center.mapper';
import { HelpCenterService } from './help-center.service';
import { CallRequestsRepository } from './repositories/call-requests.repository';
import { FeedbacksRepository } from './repositories/feedbacks.repository';

@Module({
    imports: [SharedModule],
    controllers: [HelpCenterController],
    exports: [HelpCenterService],
    providers: [
        HelpCenterService,
        HelpCenterMapper,
        CallRequestsRepository,
        FeedbacksRepository,
    ],
})
export class HelpCenterModule {}
