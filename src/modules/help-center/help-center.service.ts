import { Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { EmailService } from '../../shared/services/email.service';
import { SimpleEmailBuilder } from '../../shared/utils/simple-email-builder';
import { CreateCallRequestDto } from './dto/create-call-request.dto';
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { CallRequest } from './entities/call-request.entity';
import { Feedback } from './entities/feedback.entity';
import { HelpCenterMapper } from './help-center.mapper';
import { CallRequestsRepository } from './repositories/call-requests.repository';
import { FeedbacksRepository } from './repositories/feedbacks.repository';
import { HelpCenterMessagesKeys } from './translate.enum';

@Injectable()
export class HelpCenterService {
    constructor(
        private readonly callRequestsRepository: CallRequestsRepository,
        private readonly feedbacksRepository: FeedbacksRepository,
        private readonly helpCenterMapper: HelpCenterMapper,
        private readonly i18n: I18nService,
        private readonly emailService: EmailService,
    ) {}

    async createCallRequest(
        createCallRequestDto: CreateCallRequestDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const callRequestEntity = this.helpCenterMapper.fromDTOToEntity(
            CallRequest,
            createCallRequestDto,
        );
        const callRequest = this.callRequestsRepository.create(
            callRequestEntity,
        );
        const createdCallRequest = await this.callRequestsRepository.save(
            callRequest,
        );

        await this.emailService.sendEmail(
            '<EMAIL>',
            'A user is requesting a call',
            new SimpleEmailBuilder()
                .addLine('Dear Support Team,')
                .addBreak()
                .addLine(
                    'A user has requested a call from you, please find below the user info and message:',
                )
                .addLine(`Name: ${createCallRequestDto.name}`)
                .addLine(`Phone: ${createCallRequestDto.mobileNumber}`)
                .addLine(
                    `Email: ${createCallRequestDto.email || 'UNSPECIFIED'}`,
                )
                .addLine(
                    `Message: ${
                        createCallRequestDto.message
                            ? createCallRequestDto.message.replace(
                                  /\n/g,
                                  '<br/>',
                              )
                            : 'UNSPECIFIED'
                    }`,
                )
                .addBreak()
                .addLine('Thank you.').content,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                HelpCenterMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdCallRequest.id,
        };
    }
    async createFeedback(
        createFeedbackDto: CreateFeedbackDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const feedbackEntity = this.helpCenterMapper.fromDTOToEntity(
            Feedback,
            createFeedbackDto,
        );
        const feedback = this.feedbacksRepository.create(feedbackEntity);
        const createdFeedback = await this.feedbacksRepository.save(feedback);

        await this.emailService.sendEmail(
            '<EMAIL>',
            'A user has submitted a feedback',
            new SimpleEmailBuilder()
                .addLine('Dear Support Team,')
                .addBreak()
                .addLine(
                    'A user has submitted a feedback, please find below the user info and feedback:',
                )
                .addLine(`Name: ${createFeedbackDto.name}`)
                .addLine(`Phone: ${createFeedbackDto.mobileNumber}`)
                .addLine(`Email: ${createFeedbackDto.email || 'UNSPECIFIED'}`)
                .addLine(
                    `Message: ${
                        createFeedbackDto.message
                            ? createFeedbackDto.message.replace(/\n/g, '<br/>')
                            : 'UNSPECIFIED'
                    }`,
                )
                .addBreak()
                .addLine('Thank you.').content,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                HelpCenterMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdFeedback.id,
        };
    }
}
