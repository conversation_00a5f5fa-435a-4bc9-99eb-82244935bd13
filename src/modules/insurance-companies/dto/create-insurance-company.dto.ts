import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsUrl, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { InsuranceCompanyTranslation } from '../entities/insurance-company-translation.entity';

export class CreateInsuranceCompanyDto extends AbstractDto {
    @ApiProperty({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @IsArray()
    @ArrayMinSize(2)
    @Expose()
    @Type(() => InsuranceCompanyTranslation)
    translations: InsuranceCompanyTranslation[];

    @ApiProperty()
    @Expose()
    @IsUrl()
    logoURL: string;
}
