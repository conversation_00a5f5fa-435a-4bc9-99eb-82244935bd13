'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { InsuranceCompanyTranslation } from '../entities/insurance-company-translation.entity';

export class InsuranceCompanyDto extends AbstractDto {
    @ApiPropertyOptional()
    @IsString()
    @Expose()
    @IsOptional()
    title?: string;

    @ApiPropertyOptional()
    @IsString()
    @Expose()
    @IsOptional()
    logoURL?: string;

    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => InsuranceCompanyTranslation)
    translations?: InsuranceCompanyTranslation[];
}
