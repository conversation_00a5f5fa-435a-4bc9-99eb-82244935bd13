import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { InsuranceCompanyTranslation } from '../entities/insurance-company-translation.entity';

export class UpdateInsuranceCompanyDto extends AbstractDto {
    @ApiPropertyOptional({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @Type(() => InsuranceCompanyTranslation)
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: InsuranceCompanyTranslation[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    logoURL?: string;
}
