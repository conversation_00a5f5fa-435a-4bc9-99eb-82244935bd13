import { Expose } from 'class-transformer';
import { IsObject, IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { InsuranceCompany } from './insurance-company.entity';

@Entity('insurance_companies_translations')
export class InsuranceCompanyTranslation extends AbstractEntity {
    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    title?: string;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    languageCode?: string;

    @ManyToOne(
        (_type) => InsuranceCompany,
        (insuranceCompany) => insuranceCompany.translations,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    @Expose()
    @IsOptional()
    @IsObject()
    insuranceCompany?: InsuranceCompany;
}
