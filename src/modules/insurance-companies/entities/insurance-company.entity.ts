import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToMany, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { InsuranceCompanyTranslation } from './insurance-company-translation.entity';

@Entity('insurance_companies')
export class InsuranceCompany extends AbstractEntity {
    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    logoURL?: string;

    @OneToMany(
        (_type) => InsuranceCompanyTranslation,
        (insuranceCompanyTranslation) =>
            insuranceCompanyTranslation.insuranceCompany,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: InsuranceCompanyTranslation[];

    @ManyToMany(
        () => ServiceProvider,
        (serviceProvider) => serviceProvider.insuranceCompanies,
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => ServiceProvider)
    serviceProviders?: ServiceProvider[];
}
