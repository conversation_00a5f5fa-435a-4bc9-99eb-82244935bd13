'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateInsuranceCompanyDto } from './dto/create-insurance-company.dto';
import { InsuranceCompanyDto } from './dto/insurance-company.dto';
import { UpdateInsuranceCompanyDto } from './dto/update-insurance-company.dto';
import { InsuranceCompaniesService } from './insurance-companies.service';

@Controller('insurance-companies')
@ApiTags('insurance-companies')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class InsuranceCompaniesController {
    constructor(private insuranceCompaniesService: InsuranceCompaniesService) {}

    /*********************** CRUD Operations *****************************/
    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get insurance company',
        type: InsuranceCompanyDto,
    })
    async getInsuranceCompany(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<InsuranceCompanyDto> {
        return this.insuranceCompaniesService.getInsuranceCompany(id, lang);
    }

    @Get()
    @ApiResponse({
        description: 'Get all insurance companies',
        type: [InsuranceCompanyDto],
    })
    async getAllInsuranceCompanies(
        @I18nLang() lang: string,
    ): Promise<InsuranceCompanyDto[]> {
        return this.insuranceCompaniesService.getAllInsuranceCompanies(lang);
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create insurance company',
        type: CreateOperationsResponse,
    })
    async createInsuranceCompany(
        @Body() createInsuranceCompanyDto: CreateInsuranceCompanyDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.insuranceCompaniesService.createInsuranceCompany(
            createInsuranceCompanyDto,
            lang,
        );
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update insurance company',
        type: BasicOperationsResponse,
    })
    async updateInsuranceCompany(
        @Param('id') id: number,
        @Body() updateInsuranceCompanyDto: UpdateInsuranceCompanyDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.insuranceCompaniesService.updateInsuranceCompany(
            id,
            updateInsuranceCompanyDto,
            lang,
        );
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete insurance company',
        type: BasicOperationsResponse,
    })
    async deleteInsuranceCompany(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.insuranceCompaniesService.deleteInsuranceCompany(id, lang);
    }
}
