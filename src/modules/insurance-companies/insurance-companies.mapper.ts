import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreateInsuranceCompanyDto } from './dto/create-insurance-company.dto';
import { InsuranceCompanyDto } from './dto/insurance-company.dto';
import { UpdateInsuranceCompanyDto } from './dto/update-insurance-company.dto';
import { InsuranceCompany } from './entities/insurance-company.entity';

type InsuranceCompanyDTOs =
    | CreateInsuranceCompanyDto
    | UpdateInsuranceCompanyDto
    | InsuranceCompanyDto;

@Injectable()
export class InsuranceCompaniesMapper extends AbstractMapper<
    InsuranceCompanyDTOs,
    InsuranceCompany
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<InsuranceCompany>,
        sourceObject: InsuranceCompanyDTOs,
    ): InsuranceCompany {
        const insuranceCompanyEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(insuranceCompanyEntity);
        return insuranceCompanyEntity;
    }

    fromEntityToDTO(
        destination: ClassType<InsuranceCompanyDto>,
        sourceObject: InsuranceCompany,
    ): InsuranceCompanyDto {
        const insuranceCompanyDto: InsuranceCompanyDto = super.fromEntityToDTO(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(insuranceCompanyDto);
        insuranceCompanyDto.title = insuranceCompanyDto.translations[0].title;

        return insuranceCompanyDto;
    }
}
