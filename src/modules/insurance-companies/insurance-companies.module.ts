import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { InsuranceCompaniesController } from './insurance-companies.controller';
import { InsuranceCompaniesMapper } from './insurance-companies.mapper';
import { InsuranceCompaniesService } from './insurance-companies.service';
import { InsuranceCompaniesTranslationsRepository } from './repositories/insurance-companies-translations.repository';
import { InsuranceCompaniesRepository } from './repositories/insurance-companies.repository';

@Module({
    imports: [SharedModule],
    controllers: [InsuranceCompaniesController],
    exports: [InsuranceCompaniesService],
    providers: [
        InsuranceCompaniesService,
        InsuranceCompaniesMapper,
        InsuranceCompaniesRepository,
        InsuranceCompaniesTranslationsRepository,
    ],
})
export class InsuranceCompaniesModule {}
