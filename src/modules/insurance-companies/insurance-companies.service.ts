import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { CreateInsuranceCompanyDto } from './dto/create-insurance-company.dto';
import { InsuranceCompanyDto } from './dto/insurance-company.dto';
import { UpdateInsuranceCompanyDto } from './dto/update-insurance-company.dto';
import { InsuranceCompanyTranslation } from './entities/insurance-company-translation.entity';
import { InsuranceCompany } from './entities/insurance-company.entity';
import { InsuranceCompaniesMapper } from './insurance-companies.mapper';
import { InsuranceCompaniesTranslationsRepository } from './repositories/insurance-companies-translations.repository';
import { InsuranceCompaniesRepository } from './repositories/insurance-companies.repository';
import { InsuranceCompanyMessagesKeys } from './translate.enum';

@Injectable()
export class InsuranceCompaniesService {
    constructor(
        private readonly insuranceCompaniesRepository: InsuranceCompaniesRepository,
        private readonly insuranceCompaniesTranslationsRepository: InsuranceCompaniesTranslationsRepository,
        private readonly insuranceCompaniesMapper: InsuranceCompaniesMapper,
        private readonly i18n: I18nService,
        private readonly helperService: HelperService,
    ) { }

    async getInsuranceCompany(
        id: number,
        lang: string,
    ): Promise<InsuranceCompanyDto> {
        const insuranceCompany =
            await this.insuranceCompaniesRepository.findOne({
                where: { id },
                relations: ['translations'],
            });
        if (!insuranceCompany) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        InsuranceCompanyMessagesKeys.INSURANCE_COMPANY_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.insuranceCompaniesMapper.fromEntityToDTO(
            InsuranceCompanyDto,
            insuranceCompany,
        );
    }
    async getAllInsuranceCompanies(
        lang: string,
    ): Promise<InsuranceCompanyDto[]> {
        const insuranceCompanies = await this.insuranceCompaniesRepository
            .createQueryBuilder('insuranceCompany')
            .leftJoinAndSelect('insuranceCompany.translations', 'translations')
            .select([
                'insuranceCompany.id',
                'insuranceCompany.logoURL',
                'translations.title',
                'translations.languageCode',
            ])
            .getMany();
        return insuranceCompanies.map((insurancCompany) =>
            this.insuranceCompaniesMapper.fromEntityToDTO(
                InsuranceCompanyDto,
                insurancCompany,
            ),
        );
    }

    async createInsuranceCompany(
        createInsuranceCompanyDto: CreateInsuranceCompanyDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const insuranceCompanyEntity =
            this.insuranceCompaniesMapper.fromDTOToEntity(
                InsuranceCompany,
                createInsuranceCompanyDto,
            );
        const insuranceCompany = this.insuranceCompaniesRepository.create(
            insuranceCompanyEntity,
        );
        const createdInsuranceCompany =
            await this.insuranceCompaniesRepository.save(insuranceCompany);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                InsuranceCompanyMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdInsuranceCompany.id,
        };
    }

    async updateInsuranceCompany(
        id: number,
        updateInsuranceCompanyDto: UpdateInsuranceCompanyDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const insuranceCompany =
            await this.insuranceCompaniesRepository.findOne({
                where: { id },
            });
        if (!insuranceCompany) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        InsuranceCompanyMessagesKeys.INSURANCE_COMPANY_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const insuranceCompanyEntity =
            this.insuranceCompaniesMapper.fromDTOToEntity(
                InsuranceCompany,
                updateInsuranceCompanyDto,
            );

        if (insuranceCompanyEntity.translations) {
            await this.updateTranslation(
                id,
                insuranceCompanyEntity.translations,
            );
            delete insuranceCompanyEntity.translations;
        }
        await this.insuranceCompaniesRepository.update(
            { id },
            insuranceCompanyEntity,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                InsuranceCompanyMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async deleteInsuranceCompany(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const insuranceCompany =
            await this.insuranceCompaniesRepository.findOne({
                where: { id },
            });
        if (!insuranceCompany) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        InsuranceCompanyMessagesKeys.INSURANCE_COMPANY_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.insuranceCompaniesRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                InsuranceCompanyMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async updateTranslation(
        parentId: number,
        updatedTranslation: InsuranceCompanyTranslation[],
    ): Promise<void> {
        await Promise.all(
            updatedTranslation.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);
                await this.insuranceCompaniesTranslationsRepository.update(
                    {
                        insuranceCompany: { id: parentId },
                        languageCode: translation.languageCode,
                    },
                    translation,
                );
            }),
        );
    }
}
