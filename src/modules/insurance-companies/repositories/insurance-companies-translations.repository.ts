import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { InsuranceCompanyTranslation } from '../entities/insurance-company-translation.entity';

@Injectable()
export class InsuranceCompaniesTranslationsRepository extends Repository<
    InsuranceCompanyTranslation
> {
    constructor(dataSource: DataSource) {
        super(InsuranceCompanyTranslation, dataSource.createEntityManager());
    }
}
