'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { MedicationTranslation } from '../entities/medication-translation.entity';

export class MedicationDto extends AbstractDto {
    @ApiPropertyOptional()
    @IsString()
    @Expose()
    @IsOptional()
    title?: string;

    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => MedicationTranslation)
    translations?: MedicationTranslation[];
}
