import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { MedicationDto } from './medication.dto';

export class MedicationsPageDto {
    @ApiProperty({
        type: MedicationDto,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => MedicationDto)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: MedicationDto[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: MedicationDto[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
