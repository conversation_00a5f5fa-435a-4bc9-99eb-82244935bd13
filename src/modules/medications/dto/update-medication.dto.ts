import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { MedicationTranslation } from '../entities/medication-translation.entity';

export class UpdateMedicationDto extends AbstractDto {
    @ApiPropertyOptional({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @Type(() => MedicationTranslation)
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: MedicationTranslation[];
}
