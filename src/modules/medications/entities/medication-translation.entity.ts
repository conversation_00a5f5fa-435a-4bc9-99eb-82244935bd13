import { Expose, Type } from 'class-transformer';
import { IsObject, IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Medication } from './medication.entity';

@Entity('medications_translations')
export class MedicationTranslation extends AbstractEntity {
    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    title?: string;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    languageCode?: string;

    @ManyToOne((_type) => Medication, (medication) => medication.translations, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => Medication)
    medication?: Medication;
}
