import { Expose, Type } from 'class-transformer';
import { <PERSON>Array, IsOptional } from 'class-validator';
import { Entity, ManyToMany, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Patient } from './../../patients/entities/patient.entity';
import { MedicationTranslation } from './medication-translation.entity';

@Entity('medications')
export class Medication extends AbstractEntity {
    @OneToMany(
        (_type) => MedicationTranslation,
        (medicationTranslation) => medicationTranslation.medication,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => MedicationTranslation)
    translations?: MedicationTranslation[];

    @ManyToMany(() => Patient, (patient) => patient.medications)
    @Expose()
    @IsOptional()
    @IsArray()
    patients?: Patient[];
}
