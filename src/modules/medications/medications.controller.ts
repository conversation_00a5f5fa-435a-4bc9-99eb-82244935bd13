'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateMedicationDto } from './dto/create-medication.dto';
import { MedicationDto } from './dto/medication.dto';
import { MedicationsPageDto } from './dto/medications-page.dto';
import { UpdateMedicationDto } from './dto/update-medication.dto';
import { MedicationsService } from './medications.service';

@Controller('medications')
@ApiTags('medications')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class MedicationsController {
    constructor(private medicationsService: MedicationsService) {}

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get medication',
        type: MedicationDto,
    })
    getMedication(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<MedicationDto> {
        return this.medicationsService.getMedication(id, lang);
    }

    @Get()
    @ApiResponse({
        description: 'Get all medications',
        type: [MedicationDto],
    })
    getMedications(
        @Req() req: Request,
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @I18nLang() lang: string,
    ): Promise<MedicationDto[] | MedicationsPageDto> {
        return this.medicationsService.getMedications(
            query,
            pageOptionsDto,
            lang,
        );
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create medication',
        type: CreateOperationsResponse,
    })
    createMedication(
        @Body() createMedicationDto: CreateMedicationDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.medicationsService.createMedication(
            createMedicationDto,
            lang,
        );
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update medication',
        type: BasicOperationsResponse,
    })
    async updateMedication(
        @Param('id') id: number,
        @Body() updateMedicationDto: UpdateMedicationDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.medicationsService.updateMedication(
            id,
            updateMedicationDto,
            lang,
        );
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete medication',
        type: BasicOperationsResponse,
    })
    async deleteMedication(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.medicationsService.deleteMedication(id, lang);
    }
}
