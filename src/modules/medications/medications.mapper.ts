import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreateMedicationDto } from './dto/create-medication.dto';
import { MedicationDto } from './dto/medication.dto';
import { UpdateMedicationDto } from './dto/update-medication.dto';
import { Medication } from './entities/medication.entity';

type MedicationDTOs = CreateMedicationDto | UpdateMedicationDto | MedicationDto;
@Injectable()
export class MedicationsMapper extends AbstractMapper<
    MedicationDTOs,
    Medication
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<Medication>,
        sourceObject: MedicationDTOs,
    ): Medication {
        const medicationEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(medicationEntity);
        return medicationEntity;
    }

    fromEntityToDTO(
        destination: ClassType<MedicationDto>,
        sourceObject: Medication,
    ): MedicationDto {
        const medicationDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(medicationDto);
        return medicationDto;
    }
}
