import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { MedicationsController } from './medications.controller';
import { MedicationsMapper } from './medications.mapper';
import { MedicationsService } from './medications.service';
import { MedicationsTranslationsRepository } from './repositories/medications-translations.repository';
import { MedicationsRepository } from './repositories/medications.repository';

@Module({
    imports: [SharedModule],
    controllers: [MedicationsController],
    exports: [MedicationsService],
    providers: [
        MedicationsService,
        MedicationsMapper,
        MedicationsRepository,
        MedicationsTranslationsRepository,
    ],
})
export class MedicationsModule {}
