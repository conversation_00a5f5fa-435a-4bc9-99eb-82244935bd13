import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
} from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { SelectQueryBuilder } from 'typeorm';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { HelperService } from '../../shared/services/helper';
import { CreateMedicationDto } from './dto/create-medication.dto';
import { MedicationDto } from './dto/medication.dto';
import { MedicationsPageDto } from './dto/medications-page.dto';
import { UpdateMedicationDto } from './dto/update-medication.dto';
import { MedicationTranslation } from './entities/medication-translation.entity';
import { Medication } from './entities/medication.entity';
import { MedicationsMapper } from './medications.mapper';
import { MedicationsTranslationsRepository } from './repositories/medications-translations.repository';
import { MedicationsRepository } from './repositories/medications.repository';
import { MedicationMessagesKeys } from './translate.enum';

@Injectable()
export class MedicationsService {
    constructor(
        private readonly medicationsRepository: MedicationsRepository,
        private readonly medicationsTranslationsRepository: MedicationsTranslationsRepository,
        private readonly medicationsMapper: MedicationsMapper,
        private readonly i18n: I18nService,
        private readonly helperService: HelperService,
    ) { }

    async getMedication(id: number, lang: string): Promise<MedicationDto> {
        const medication = await this.medicationsRepository.findOne({
            where: { id },
            relations: ['translations'],
        });
        if (!medication) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        MedicationMessagesKeys.MEDICATION_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.medicationsMapper.fromEntityToDTO(
            MedicationDto,
            medication,
        );
    }
    buildGetMedicationsQuery(
        query: SelectQueryBuilder<Medication>,
        httpQueryString: string,
    ): SelectQueryBuilder<Medication> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }

        if (httpQueryObject.search) {
            query.andWhere('(title) ILIKE :searchKey', {
                searchKey: `%${httpQueryObject.search.value.trim()}%`,
            });
        }

        return query;
    }
    async getMedications(
        httpQueryString: string,
        pageOptionsDto: PageOptionsDto,
        lang: string,
    ): Promise<MedicationsPageDto> {
        let query = this.medicationsRepository
            .createQueryBuilder('medication')
            .leftJoinAndSelect(
                'medication.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .orderBy('translations.title', 'ASC')
            .select();

        if (httpQueryString) {
            query = this.buildGetMedicationsQuery(query, httpQueryString);
        }

        const [requests, pageMetaDto] = await query.paginate(pageOptionsDto);

        const mappedRequests = requests.map((request) =>
            this.medicationsMapper.fromEntityToDTO(MedicationDto, request),
        );

        return new MedicationsPageDto(mappedRequests, pageMetaDto);
    }
    async getAllMedications(lang: string): Promise<MedicationDto[]> {
        const medicationsEntities = await this.medicationsRepository
            .createQueryBuilder('medication')
            .leftJoinAndSelect(
                'medication.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .orderBy('translations.title', 'ASC')
            .getMany();

        const medicationsDtos = medicationsEntities.map((medicationEntity) =>
            this.medicationsMapper.fromEntityToDTO(
                MedicationDto,
                medicationEntity,
            ),
        );

        return medicationsDtos
            .filter((medication) => medication.translations.length > 0)
            .map((medication) => ({
                id: medication.id,
                title: medication.translations[0].title,
            }));
    }

    async getAllMedicationsForAdmin(): Promise<MedicationDto[]> {
        const medications = await this.medicationsRepository.find({
            relations: ['translations'],
        });
        return medications.map((medication) =>
            this.medicationsMapper.fromEntityToDTO(MedicationDto, medication),
        );
    }

    async createMedication(
        createMedicationDto: CreateMedicationDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const medicationEntity = this.medicationsMapper.fromDTOToEntity(
            Medication,
            createMedicationDto,
        );
        const medication = this.medicationsRepository.create(medicationEntity);
        const createdMedication = await this.medicationsRepository.save(
            medication,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                MedicationMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdMedication.id,
        };
    }

    async updateMedication(
        id: number,
        updateMedicationDto: UpdateMedicationDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const medication = await this.medicationsRepository.findOne({
            where: { id },
        });

        if (!medication) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        MedicationMessagesKeys.MEDICATION_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },

                HttpStatus.NOT_FOUND,
            );
        }

        const medicationEntity = this.medicationsMapper.fromDTOToEntity(
            Medication,
            updateMedicationDto,
        );

        if (medicationEntity.translations) {
            await this.updateTranslation(id, medicationEntity.translations);
            delete medicationEntity.translations;
        }
        await this.medicationsRepository.update({ id }, medicationEntity);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                MedicationMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async deleteMedication(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const medication = await this.medicationsRepository.findOne({
            where: { id },
        });

        if (!medication) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        MedicationMessagesKeys.MEDICATION_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.medicationsRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                MedicationMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async updateTranslation(
        parentId: number,
        updatedTranslation: MedicationTranslation[],
    ): Promise<void> {
        await Promise.all(
            updatedTranslation.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);
                await this.medicationsTranslationsRepository.update(
                    {
                        medication: { id: parentId },
                        languageCode: translation.languageCode,
                    },
                    translation,
                );
            }),
        );
    }
}
