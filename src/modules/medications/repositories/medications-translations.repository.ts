import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { MedicationTranslation } from '../entities/medication-translation.entity';

@Injectable()
export class MedicationsTranslationsRepository extends Repository<
    MedicationTranslation
> {
    constructor(dataSource: DataSource) {
        super(MedicationTranslation, dataSource.createEntityManager());
    }
}
