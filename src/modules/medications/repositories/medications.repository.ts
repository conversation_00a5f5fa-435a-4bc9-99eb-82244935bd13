import { InjectDataSource } from '@nestjs/typeorm';
import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { Medication } from '../entities/medication.entity';

@Injectable()
export class MedicationsRepository extends Repository<Medication> {
    constructor(@InjectDataSource() dataSource: DataSource) {
        super(Medication, dataSource.createEntityManager());
    }
}
