import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    ArrayMinSize,
    IsArray,
    IsNumber,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { MembershipTranslation } from '../entities/membership-translation.entity';

export class CreateMembershipDto extends AbstractDto {
    @ApiProperty({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @IsArray()
    @ArrayMinSize(2)
    @Expose()
    @Type(() => MembershipTranslation)
    translations: MembershipTranslation[];

    @IsString()
    @Expose()
    @ApiProperty()
    @IsOptional()
    validUntil?: Date;

    @IsNumber()
    @Expose()
    @ApiProperty()
    discountValue: number;
}
