import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

import { DoctorDto } from '../../doctors/dto/doctor.dto';
import { MembershipDto } from './membership.dto';

export class DoctorMembershipDto {
    @IsString()
    @Expose()
    @ApiProperty()
    @IsOptional()
    validUntil?: Date;

    @IsNumber()
    @Expose()
    @ApiProperty()
    discountValue: number;

    @Expose()
    @ApiProperty()
    membership?: MembershipDto;

    @Expose()
    @ApiProperty()
    doctor?: DoctorDto;
}
