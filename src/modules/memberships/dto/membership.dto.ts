'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNumber, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { DoctorMembership } from '../entities/doctor-membership.entity';
import { MembershipTranslation } from '../entities/membership-translation.entity';

export class MembershipDto extends AbstractDto {
    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => MembershipTranslation)
    translations?: MembershipTranslation[];

    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => DoctorMembership)
    doctorMemberships?: DoctorMembership[];

    @IsString()
    @Expose()
    @ApiProperty()
    @IsOptional()
    validUntil?: Date;

    @IsNumber()
    @Expose()
    @ApiProperty()
    discountValue: number;
}
