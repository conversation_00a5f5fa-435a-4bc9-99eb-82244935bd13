import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsNumber,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { MembershipTranslation } from '../entities/membership-translation.entity';

export class UpdateMembershipDto extends AbstractDto {
    @ApiPropertyOptional({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @Type(() => MembershipTranslation)
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: MembershipTranslation[];

    @IsString()
    @Expose()
    @ApiProperty()
    @IsOptional()
    validUntil?: Date;

    @IsNumber()
    @Expose()
    @ApiProperty()
    discountValue: number;
}
