import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';
import {
    Column,
    Entity,
    JoinTable,
    ManyToOne,
    PrimaryGeneratedColumn,
} from 'typeorm';

import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { Membership } from './membership.entity';

@Entity()
export class DoctorMembership {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    @Expose()
    discountValue: number;

    @Column()
    @Expose()
    membershipId: number;

    @Column()
    @Expose()
    doctorId: number;

    @Column({ type: 'date' })
    @Expose()
    @IsOptional()
    validUntil?: Date;

    @ManyToOne(() => Membership, (membership) => membership.doctorMemberships)
    @JoinTable({ name: 'membership_id' })
    membership: Membership;

    @ManyToOne(() => DoctorEntity, (doctor) => doctor.doctorMemberships)
    @JoinTable({ name: 'doctor_id' })
    doctor: DoctorEntity;
}
