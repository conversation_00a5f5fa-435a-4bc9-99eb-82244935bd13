import { Expose, Type } from 'class-transformer';
import { IsArray, IsObject, IsOptional } from 'class-validator';
import {
    Column,
    Entity,
    JoinTable,
    ManyToMany,
    OneToMany,
    OneToOne,
} from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { DoctorMembership } from './doctor-membership.entity';
import { MembershipTranslation } from './membership-translation.entity';

@Entity('memberships')
export class Membership extends AbstractEntity {
    @OneToMany(
        (_type) => MembershipTranslation,
        (membershipTranslation) => membershipTranslation.membership,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => MembershipTranslation)
    translations?: MembershipTranslation[];

    @Column()
    @Expose()
    discountValue: number;

    @Column({ type: 'date' })
    @Expose()
    @IsOptional()
    validUntil?: Date;

    @OneToOne(() => Patient, (patient) => patient.membership) // specify inverse side as a second parameter
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => Patient)
    patient?: Patient;

    @Expose()
    @IsOptional()
    @IsArray()
    @ManyToMany(() => DoctorEntity, (doctor) => doctor.memberships)
    @JoinTable({
        name: 'doctor_membership',
        joinColumn: { name: 'membership_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'doctor_id', referencedColumnName: 'id' },
    })
    doctors?: DoctorEntity[];

    @Expose()
    @IsOptional()
    @IsArray()
    @OneToMany(
        () => DoctorMembership,
        (doctorMembership) => doctorMembership.membership,
    )
    doctorMemberships?: DoctorMembership[];
}
