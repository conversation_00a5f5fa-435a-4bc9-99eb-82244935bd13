'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    Headers,
    Param,
    Post,
    Put,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { RoleType, SourceType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { DebugLogger } from '../../shared/services/logger.service';
import { CreateMembershipDto } from './dto/create-membership.dto';
import { DoctorMembershipDto } from './dto/doctor-membership.dto';
import { MembershipDto } from './dto/membership.dto';
import { UpdateMembershipDto } from './dto/update-membership.dto';
import { MembershipsService } from './memberships.service';

@Controller('memberships')
@ApiTags('memberships')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class MembershipsController {
    constructor(
        private membershipsService: MembershipsService,
        private logger: DebugLogger,
    ) {}

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get membership',
        type: MembershipDto,
    })
    getMembership(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<MembershipDto> {
        return this.membershipsService.getMembership(id, lang);
    }

    @Get()
    @ApiResponse({
        description: 'Get all memberships',
        type: [MembershipDto],
    })
    getMemberships(
        @Headers('source-type') source: SourceType,
        @I18nLang() lang: string,
    ): Promise<MembershipDto[]> {
        if (source && source === SourceType.ADMIN) {
            return this.membershipsService.getAllMembershipsForAdmin();
        }
        return this.membershipsService.getAllMemberships(lang);
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create membership',
        type: CreateOperationsResponse,
    })
    createMembership(
        @Body() createMembershipDto: CreateMembershipDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.membershipsService.createMembership(
            createMembershipDto,
            lang,
        );
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update membership',
        type: BasicOperationsResponse,
    })
    async updateMembership(
        @Param('id') id: number,
        @Body() updateMembershipDto: UpdateMembershipDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.membershipsService.updateMembership(
            id,
            updateMembershipDto,
            lang,
        );
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete membership',
        type: BasicOperationsResponse,
    })
    async deleteMembership(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.membershipsService.deleteMembership(id, lang);
    }

    @Post(':membershipId/doctor/:doctorId')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'link doctor membership with different discount',
        type: CreateOperationsResponse,
    })
    async saveDoctorMembership(
        @Param('membershipId') membershipId: number,
        @Param('doctorId') doctorId: number,
        @Body() doctorMembershipDto: DoctorMembershipDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        this.logger.debug(
            JSON.stringify(doctorMembershipDto),
            `${MembershipsController.name}:saveDoctorMembership`,
        );
        return this.membershipsService.addDoctorMembership(
            membershipId,
            doctorId,
            lang,
            doctorMembershipDto,
        );
    }
}
