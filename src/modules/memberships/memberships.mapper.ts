import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreateMembershipDto } from './dto/create-membership.dto';
import { MembershipDto } from './dto/membership.dto';
import { UpdateMembershipDto } from './dto/update-membership.dto';
import { Membership } from './entities/membership.entity';

type MembershipDTOs = CreateMembershipDto | UpdateMembershipDto | MembershipDto;

@Injectable()
export class MembershipMapper extends AbstractMapper<
    MembershipDTOs,
    Membership
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }

    fromDTOToEntity(
        destination: ClassType<Membership>,
        sourceObject: MembershipDTOs,
    ): Membership {
        const membershipEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(membershipEntity);
        return membershipEntity;
    }

    fromEntityToDTO(
        destination: ClassType<MembershipDto>,
        sourceObject: Membership,
    ): MembershipDto {
        const membershipDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(membershipDto);
        return membershipDto;
    }
}
