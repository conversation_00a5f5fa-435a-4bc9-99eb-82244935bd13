import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { DoctorsRepository } from '../doctors/repositories/doctors.repository';
import { MembershipsController } from './memberships.controller';
import { MembershipMapper } from './memberships.mapper';
import { MembershipsService } from './memberships.service';
import { DoctorMembershipRepository } from './repositories/doctor-membership.repository';
import { MembershipsTranslationsRepository } from './repositories/memberships-translations.respository';
import { MembershipsRepository } from './repositories/memberships.repository';

@Module({
    imports: [SharedModule],
    controllers: [MembershipsController],
    exports: [MembershipsService],
    providers: [
        MembershipsService,
        MembershipMapper,
        MembershipsRepository,
        MembershipsTranslationsRepository,
        DoctorMembershipRepository,
        DoctorsRepository],
})
export class MembershipsModule {
}
