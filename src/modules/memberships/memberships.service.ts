import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { DebugLogger } from '../../shared/services/logger.service';
import { DoctorsRepository } from '../doctors/repositories/doctors.repository';
import { DoctorMessagesKeys } from '../doctors/translate.enum';
import { CreateMembershipDto } from './dto/create-membership.dto';
import { DoctorMembershipDto } from './dto/doctor-membership.dto';
import { MembershipDto } from './dto/membership.dto';
import { UpdateMembershipDto } from './dto/update-membership.dto';
import { MembershipTranslation } from './entities/membership-translation.entity';
import { Membership } from './entities/membership.entity';
import { MembershipMapper } from './memberships.mapper';
import { DoctorMembershipRepository } from './repositories/doctor-membership.repository';
import { MembershipsTranslationsRepository } from './repositories/memberships-translations.respository';
import { MembershipsRepository } from './repositories/memberships.repository';
import { MembershipMessagesKeys } from './translate.enum';

@Injectable()
export class MembershipsService {
    constructor(
        private readonly membershipsRepository: MembershipsRepository,
        private readonly membershipsTranslationRepository: MembershipsTranslationsRepository,
        private readonly doctorRepository: DoctorsRepository,
        private readonly doctorMembershipRepository: DoctorMembershipRepository,
        private readonly membershipsMapper: MembershipMapper,
        private readonly i18n: I18nService,
        private readonly helperService: HelperService,
        private logger: DebugLogger,
    ) { }

    async createMembership(
        createMembershipDto: CreateMembershipDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const membershipEntity = this.membershipsMapper.fromDTOToEntity(
            Membership,
            createMembershipDto,
        );
        const membership = this.membershipsRepository.create(membershipEntity);
        const createMembership = await this.membershipsRepository.save(
            membership,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                MembershipMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
            createdId: createMembership.id,
        };
    }

    async updateMembership(
        id: number,
        updateMembershipDto: UpdateMembershipDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const membership = await this.membershipsRepository.findOne({
            where: { id },
        });

        if (!membership) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        MembershipMessagesKeys.MEMBERSHIP_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const membershipEntity = this.membershipsMapper.fromDTOToEntity(
            Membership,
            updateMembershipDto,
        );

        if (membershipEntity.translations) {
            await this.updateTranslation(id, membershipEntity.translations);
            delete membershipEntity.translations;
        }

        await this.membershipsRepository.update({ id }, membershipEntity);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                MembershipMessagesKeys.UPDATED_SUCCESSFULY,
                { lang },
            ),
        };
    }

    async deleteMembership(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const membership = await this.membershipsRepository.findOne({
            where: { id },
        });

        await this.ifIsNotFound(
            membership,
            MembershipMessagesKeys.MEMBERSHIP_NOT_FOUND,
            lang,
        );

        await this.membershipsRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                MembershipMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async getAllMemberships(lang: string): Promise<MembershipDto[]> {
        const membershipsEntity = await this.membershipsRepository
            .createQueryBuilder('membership')
            .leftJoinAndSelect(
                'membership.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .getMany();

        const membershipDtos = membershipsEntity.map((gradeEntity) =>
            this.membershipsMapper.fromEntityToDTO(MembershipDto, gradeEntity),
        );

        return membershipDtos
            .filter((membership) => membership.translations.length > 0)
            .map((membership) => ({
                id: membership.id,
                title: membership.translations[0].title,
                validUntil: membership.validUntil,
                discountValue: membership.discountValue,
            }));
    }

    async getAllMembershipsForAdmin(): Promise<MembershipDto[]> {
        const memberships = await this.membershipsRepository.find({
            relations: ['translations'],
        });
        return memberships.map((grade) =>
            this.membershipsMapper.fromEntityToDTO(MembershipDto, grade),
        );
    }

    async getMembership(id: number, lang: string): Promise<MembershipDto> {
        const membership = await this.membershipsRepository.findOne({
            where: { id },
            relations: ['translations'],
        });

        await this.ifIsNotFound(
            membership,
            MembershipMessagesKeys.MEMBERSHIP_NOT_FOUND,
            lang,
        );

        return this.membershipsMapper.fromDTOToEntity(
            MembershipDto,
            membership,
        );
    }

    private async updateTranslation(
        parentId: number,
        updatedTranslation: MembershipTranslation[],
    ): Promise<void> {
        await Promise.all(
            updatedTranslation.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);
                await this.membershipsTranslationRepository.update(
                    {
                        membership: { id: parentId },
                        languageCode: translation.languageCode,
                    },
                    translation,
                );
            }),
        );
    }

    async ifIsNotFound(
        object: unknown,
        message: string,
        lang: string,
    ): Promise<void> {
        if (!object) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(message, {
                        lang,
                    }),
                },
                HttpStatus.NOT_FOUND,
            );
        }
    }

    async addDoctorMembership(
        membershipId: number,
        doctorId: number,
        lang: string,
        doctorMembershipDto: DoctorMembershipDto,
    ): Promise<BasicOperationsResponse> {
        const membership = await this.membershipsRepository.findOne({
            where: { id: membershipId },
        });

        this.logger.debug(
            JSON.stringify(membership),
            `${MembershipsService.name}:addDoctorMembership`,
        );
        await this.ifIsNotFound(
            membership,
            MembershipMessagesKeys.MEMBERSHIP_NOT_FOUND,
            lang,
        );
        const doctor = await this.doctorRepository.findOne({
            where: { id: doctorId },
        });
        this.logger.debug(
            JSON.stringify(doctor),
            `${MembershipsService.name}:addDoctorMembership`,
        );
        await this.ifIsNotFound(
            doctor,
            DoctorMessagesKeys.DOCTOR_NOT_FOUND,
            lang,
        );

        await this.doctorMembershipRepository.save({
            doctor,
            membership,
            discountValue: doctorMembershipDto.discountValue,
            validUntil: doctorMembershipDto.validUntil,
        });
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                MembershipMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }
}
