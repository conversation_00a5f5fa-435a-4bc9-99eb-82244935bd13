import { Controller, Get, HttpStatus, Param } from '@nestjs/common';

import { AiMessageService } from '../services/ai-message.service';

@Controller('ai-messages')
export class AiMessageController {
    constructor(private readonly aiMessageService: AiMessageService) {}

    @Get('code/:code')
    async findByCode(@Param('code') code: string) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.aiMessageService.getFormattedWelcomeMessage(code),
        };
    }
}
