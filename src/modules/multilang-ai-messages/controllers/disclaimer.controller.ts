import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Put,
    Query,
} from '@nestjs/common';

import {
    CreateDisclaimerDto,
    UpdateDisclaimerDto,
} from '../dtos/disclaimer.dto';
import { DisclaimerService } from '../services/disclaimer.service';

@Controller('disclaimers')
export class DisclaimerController {
    constructor(private readonly disclaimerService: DisclaimerService) {}

    @Get()
    async findAll() {
        return {
            statusCode: HttpStatus.OK,
            data: await this.disclaimerService.findAll(),
        };
    }

    @Get(':id')
    async findOne(@Param('id') id: number) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.disclaimerService.findOne(id),
        };
    }

    @Get('by-language-key')
    async findByLanguageAndKey(
        @Query('languageCode') languageCode: string,
        @Query('disclaimerKey') disclaimerKey: string,
    ) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.disclaimerService.findByLanguageAndKey(
                languageCode,
                disclaimerKey,
            ),
        };
    }

    @Post()
    @HttpCode(HttpStatus.CREATED)
    async create(@Body() createDisclaimerDto: CreateDisclaimerDto) {
        return {
            statusCode: HttpStatus.CREATED,
            data: await this.disclaimerService.create(createDisclaimerDto),
        };
    }

    @Put(':id')
    async update(
        @Param('id') id: number,
        @Body() updateDisclaimerDto: UpdateDisclaimerDto,
    ) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.disclaimerService.update(id, updateDisclaimerDto),
        };
    }

    @Delete(':id')
    @HttpCode(HttpStatus.NO_CONTENT)
    async remove(@Param('id') id: number) {
        await this.disclaimerService.remove(id);
    }
}
