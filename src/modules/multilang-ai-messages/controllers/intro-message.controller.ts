import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Put,
    Query,
} from '@nestjs/common';

import {
    CreateIntroMessageTemplateDto,
    UpdateIntroMessageTemplateDto,
} from '../dtos/intro-message-template.dto';
import {
    CreateIntroMessageDto,
    UpdateIntroMessageDto,
} from '../dtos/intro-message.dto';
import { IntroMessageTemplateService } from '../services/intro-message-template.service';
import { IntroMessageService } from '../services/intro-message.service';

@Controller('intro-messages')
export class IntroMessageController {
    constructor(
        private readonly introMessageService: IntroMessageService,
        private readonly templateService: IntroMessageTemplateService,
    ) {}

    @Get()
    async findAll() {
        return {
            statusCode: HttpStatus.OK,
            data: await this.introMessageService.findAll(),
        };
    }

    @Get(':id')
    async findOne(@Param('id') id: number) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.introMessageService.findOne(id),
        };
    }

    @Get('by-language-template')
    async findByLanguageAndTemplate(
        @Query('languageCode') languageCode: string,
        @Query('templateKey') templateKey: string,
    ) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.introMessageService.findByLanguageAndTemplate(
                languageCode,
                templateKey,
            ),
        };
    }

    @Post()
    @HttpCode(HttpStatus.CREATED)
    async create(@Body() createMessageDto: CreateIntroMessageDto) {
        return {
            statusCode: HttpStatus.CREATED,
            data: await this.introMessageService.create(createMessageDto),
        };
    }

    @Put(':id')
    async update(
        @Param('id') id: number,
        @Body() updateMessageDto: UpdateIntroMessageDto,
    ) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.introMessageService.update(id, updateMessageDto),
        };
    }

    @Delete(':id')
    @HttpCode(HttpStatus.NO_CONTENT)
    async remove(@Param('id') id: number) {
        await this.introMessageService.remove(id);
    }

    // Template endpoints
    @Get('templates')
    async findAllTemplates() {
        return {
            statusCode: HttpStatus.OK,
            data: await this.templateService.findAll(),
        };
    }

    @Get('templates/:id')
    async findOneTemplate(@Param('id') id: number) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.templateService.findOne(id),
        };
    }

    @Post('templates')
    @HttpCode(HttpStatus.CREATED)
    async createTemplate(
        @Body() createTemplateDto: CreateIntroMessageTemplateDto,
    ) {
        return {
            statusCode: HttpStatus.CREATED,
            data: await this.templateService.create(createTemplateDto),
        };
    }

    @Put('templates/:id')
    async updateTemplate(
        @Param('id') id: number,
        @Body() updateTemplateDto: UpdateIntroMessageTemplateDto,
    ) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.templateService.update(id, updateTemplateDto),
        };
    }

    @Delete('templates/:id')
    @HttpCode(HttpStatus.NO_CONTENT)
    async removeTemplate(@Param('id') id: number) {
        await this.templateService.remove(id);
    }
}
