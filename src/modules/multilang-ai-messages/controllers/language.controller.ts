import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Put,
} from '@nestjs/common';

import { CreateLanguageDto, UpdateLanguageDto } from '../dtos/language.dto';
import { LanguageService } from '../services/language.service';

@Controller('languages')
export class LanguageController {
    constructor(private readonly languageService: LanguageService) {}

    @Get()
    async findAll() {
        return {
            statusCode: HttpStatus.OK,
            data: await this.languageService.findAll(),
        };
    }

    @Get(':id')
    async findOne(@Param('id') id: number) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.languageService.findOne(id),
        };
    }

    @Get('code/:code')
    async findByCode(@Param('code') code: string) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.languageService.findByCode(code),
        };
    }

    @Post()
    @HttpCode(HttpStatus.CREATED)
    async create(@Body() createLanguageDto: CreateLanguageDto) {
        return {
            statusCode: HttpStatus.CREATED,
            data: await this.languageService.create(createLanguageDto),
        };
    }

    @Put(':id')
    async update(@Param('id') id: number, @Body() updateLanguageDto: UpdateLanguageDto) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.languageService.update(id, updateLanguageDto),
        };
    }

    @Delete(':id')
    @HttpCode(HttpStatus.NO_CONTENT)
    async remove(@Param('id') id: number) {
        await this.languageService.remove(id);
    }
}
