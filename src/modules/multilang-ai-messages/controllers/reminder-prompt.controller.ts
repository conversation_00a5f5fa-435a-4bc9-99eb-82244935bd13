import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Put,
} from '@nestjs/common';

import {
    CreateReminderPromptDto,
    UpdateReminderPromptDto,
} from '../dtos/reminder-prompt.dto';
import { ReminderPromptService } from '../services/reminder-prompt.service';

@Controller('reminder-prompts')
export class ReminderPromptController {
    constructor(private readonly promptService: ReminderPromptService) {}

    @Get()
    async findAll() {
        return {
            statusCode: HttpStatus.OK,
            data: await this.promptService.findAll(),
        };
    }

    @Get(':id')
    async findOne(@Param('id') id: number) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.promptService.findOne(id),
        };
    }

    @Get('by-language/:languageCode')
    async findByLanguage(@Param('languageCode') languageCode: string) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.promptService.findByLanguage(languageCode),
        };
    }

    @Post()
    @HttpCode(HttpStatus.CREATED)
    async create(@Body() createPromptDto: CreateReminderPromptDto) {
        return {
            statusCode: HttpStatus.CREATED,
            data: await this.promptService.create(createPromptDto),
        };
    }

    @Put(':id')
    async update(
        @Param('id') id: number,
        @Body() updatePromptDto: UpdateReminderPromptDto,
    ) {
        return {
            statusCode: HttpStatus.OK,
            data: await this.promptService.update(id, updatePromptDto),
        };
    }

    @Delete(':id')
    @HttpCode(HttpStatus.NO_CONTENT)
    async remove(@Param('id') id: number) {
        await this.promptService.remove(id);
    }
}
