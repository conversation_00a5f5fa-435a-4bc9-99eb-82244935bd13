// eslint-disable-next-line max-classes-per-file
import { IsNotEmpty, IsNumber, IsString, Length } from 'class-validator';

export class CreateDisclaimerDto {
    @IsString()
    @Length(2, 50)
    disclaimerKey: string;

    @IsNumber()
    languageId: number;

    @IsString()
    @IsNotEmpty()
    disclaimerText: string;
}

export class UpdateDisclaimerDto {
    @IsString()
    @IsNotEmpty()
    disclaimerText: string;
}
