// eslint-disable-next-line max-classes-per-file
import { IsBoolean, IsOptional, IsString, Length } from 'class-validator';

export class CreateLanguageDto {
    @IsString()
    @Length(2, 10)
    languageCode: string;

    @IsString()
    @Length(2, 50)
    languageName: string;

    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}

export class UpdateLanguageDto {
    @IsString()
    @Length(2, 50)
    @IsOptional()
    languageName?: string;

    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}
