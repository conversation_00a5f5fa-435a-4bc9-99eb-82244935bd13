// eslint-disable-next-line max-classes-per-file
import {
    IsNotEmpty,
    IsN<PERSON>ber,
    IsOptional,
    IsString,
    Length,
} from 'class-validator';

export class CreateReminderPromptDto {
    @IsString()
    @Length(2, 50)
    promptKey: string;

    @IsNumber()
    displayOrder: number;

    @IsNumber()
    languageId: number;

    @IsString()
    @IsNotEmpty()
    promptText: string;
}

export class UpdateReminderPromptDto {
    @IsNumber()
    @IsOptional()
    displayOrder?: number;

    @IsString()
    @IsNotEmpty()
    @IsOptional()
    promptText?: string;
}
