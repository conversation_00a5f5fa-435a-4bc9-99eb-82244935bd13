import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, Unique } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Language } from './language.entity';

@Entity('disclaimers')
@Unique(['disclaimerKey', 'languageId'])
export class Disclaimer extends AbstractEntity {
    @Column({ length: 50 })
    disclaimerKey: string;

    @Column({ name: 'language_id' })
    languageId: number;

    @Column({ type: 'text' })
    disclaimerText: string;

    @ManyToOne(() => Language, (language) => language.disclaimers)
    @JoinColumn({ name: 'language_id' })
    language: Language;
}
