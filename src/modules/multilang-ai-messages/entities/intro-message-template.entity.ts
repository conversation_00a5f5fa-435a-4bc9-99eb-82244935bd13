import { Column, Entity, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { IntroMessage } from './intro-message.entity';

@Entity('intro_message_templates')
export class IntroMessageTemplate extends AbstractEntity {
    @Column({ name: 'template_key', length: 50, unique: true })
    templateKey: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @OneToMany(() => IntroMessage, (introMessage) => introMessage.template)
    introMessages: IntroMessage[];
}
