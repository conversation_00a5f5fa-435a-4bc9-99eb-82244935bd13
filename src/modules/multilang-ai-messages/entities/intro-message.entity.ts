import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, <PERSON>ique } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { IntroMessageTemplate } from './intro-message-template.entity';
import { Language } from './language.entity';

@Entity('intro_messages')
@Unique(['templateId', 'languageId'])
export class IntroMessage extends AbstractEntity {
    @Column()
    templateId: number;

    @Column()
    languageId: number;

    @Column({ type: 'text' })
    messageText: string;

    @ManyToOne(() => IntroMessageTemplate, (template) => template.introMessages)
    @JoinColumn({ name: 'template_id' })
    template: IntroMessageTemplate;

    @ManyToOne(() => Language, (language) => language.introMessages)
    @JoinColumn({ name: 'language_id' })
    language: Language;
}
