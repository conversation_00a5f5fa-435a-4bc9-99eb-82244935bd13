import { Column, Entity, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Disclaimer } from './disclaimer.entity';
import { IntroMessage } from './intro-message.entity';
import { ReminderPrompt } from './reminder-prompt.entity';

@Entity('languages')
export class Language extends AbstractEntity {
    @Column({ name: 'language_code', length: 10, unique: true })
    languageCode: string;

    @Column({ name: 'language_name', length: 50 })
    languageName: string;

    @Column({ name: 'is_active', default: true })
    isActive: boolean;

    @OneToMany(() => IntroMessage, (introMessage) => introMessage.language)
    introMessages: IntroMessage[];

    @OneToMany(
        () => ReminderPrompt,
        (reminderPrompt) => reminderPrompt.language,
    )
    reminderPrompts: ReminderPrompt[];

    @OneToMany(() => Disclaimer, (disclaimer) => disclaimer.language)
    disclaimers: Disclaimer[];
}
