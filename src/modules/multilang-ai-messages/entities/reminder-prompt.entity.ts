import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, Unique } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Language } from './language.entity';

@Entity('reminder_prompts')
@Unique(['promptKey', 'languageId'])
export class Reminder<PERSON>rompt extends AbstractEntity {
    @Column({ name: 'prompt_key', length: 50 })
    promptKey: string;

    @Column({ name: 'display_order' })
    displayOrder: number;

    @Column({ name: 'language_id' })
    languageId: number;

    @Column({ name: 'prompt_text', type: 'text' })
    promptText: string;

    @ManyToOne(() => Language, (language) => language.reminderPrompts)
    @JoinColumn({ name: 'languageId' })
    language: Language;
}
