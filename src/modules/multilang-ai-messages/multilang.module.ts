import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DisclaimerController } from './controllers/disclaimer.controller';
import { IntroMessageController } from './controllers/intro-message.controller';
// Controllers
import { LanguageController } from './controllers/language.controller';
import { ReminderPromptController } from './controllers/reminder-prompt.controller';
import { Disclaimer } from './entities/disclaimer.entity';
import { IntroMessageTemplate } from './entities/intro-message-template.entity';
import { IntroMessage } from './entities/intro-message.entity';
// Entities
import { Language } from './entities/language.entity';
import { ReminderPrompt } from './entities/reminder-prompt.entity';
import { AiMessageService } from './services/ai-message.service';
import { DisclaimerService } from './services/disclaimer.service';
import { IntroMessageTemplateService } from './services/intro-message-template.service';
import { IntroMessageService } from './services/intro-message.service';
// Services
import { LanguageService } from './services/language.service';
import { ReminderPromptService } from './services/reminder-prompt.service';
import { AiMessageController } from './controllers/ai-message.controller';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            Language,
            IntroMessageTemplate,
            IntroMessage,
            ReminderPrompt,
            Disclaimer,
        ]),
    ],
    controllers: [
        LanguageController,
        IntroMessageController,
        ReminderPromptController,
        DisclaimerController,
        AiMessageController,
    ],
    providers: [
        LanguageService,
        IntroMessageTemplateService,
        IntroMessageService,
        ReminderPromptService,
        DisclaimerService,
        AiMessageService,
    ],
    exports: [
        LanguageService,
        IntroMessageTemplateService,
        IntroMessageService,
        ReminderPromptService,
        DisclaimerService,
        AiMessageService,
    ],
})
export class MultiLangModule {}
