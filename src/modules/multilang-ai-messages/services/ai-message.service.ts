import { Injectable } from '@nestjs/common';

import { DisclaimerService } from './disclaimer.service';
import { IntroMessageService } from './intro-message.service';
import { LanguageService } from './language.service';
import { ReminderPromptService } from './reminder-prompt.service';

// eslint-disable-next-line @typescript-eslint/naming-convention,@typescript-eslint/tslint/config
export interface CompleteAIWelcomeMessage {
    welcomeMessage: string;
    reminders: {
        key: string;
        order: number;
        text: string;
    }[];
    disclaimer: string;
}

@Injectable()
export class AiMessageService {
    constructor(
        private readonly languageService: LanguageService,
        private readonly introMessageService: IntroMessageService,
        private readonly reminderPromptService: ReminderPromptService,
        private readonly disclaimerService: DisclaimerService,
    ) {}

    /**
     * Get complete AI welcome message with reminders and disclaimer in the specified language
     * @param languageCode Language code (e.g., 'en', 'ar')
     * @returns Complete AI welcome message structure
     */
    async getCompleteWelcomeMessage(
        languageCode: string,
    ): Promise<CompleteAIWelcomeMessage> {
        // Get welcome message
        const welcomeMessage = await this.introMessageService
            .findByLanguageAndTemplate(languageCode, 'welcome_message')
            .then((message) => message.messageText)
            .catch(() => null);

        // Get reminders
        const reminders = await this.reminderPromptService
            .findByLanguage(languageCode)
            .then((prompts) =>
                prompts.map((prompt) => ({
                    key: prompt.promptKey,
                    order: prompt.displayOrder,
                    text: prompt.promptText,
                })),
            )
            .catch(() => []);

        // Get disclaimer
        const disclaimer = await this.disclaimerService
            .findByLanguageAndKey(languageCode, 'medical_disclaimer')
            .then((disc) => disc.disclaimerText)
            .catch(() => null);

        return {
            welcomeMessage,
            reminders,
            disclaimer,
        };
    }

    /**
     * Format the complete AI welcome message as a string
     * @param languageCode Language code (e.g., 'en', 'ar')
     * @returns Formatted welcome message string with reminders and disclaimer
     */
    async getFormattedWelcomeMessage(languageCode: string): Promise<string> {
        const messageData = await this.getCompleteWelcomeMessage(languageCode);

        let formattedMessage = '';

        // Add reminders
        if (messageData.reminders && messageData.reminders.length > 0) {
            formattedMessage += 'Remember to ask:\n';
            messageData.reminders.forEach((reminder, index) => {
                formattedMessage += `${index + 1}- ${reminder.text}\n`;
            });
            formattedMessage += '\n';
        }

        // Add disclaimer
        if (messageData.disclaimer) {
            formattedMessage += `${messageData.disclaimer}\n\n`;
        }

        // Add welcome message
        if (messageData.welcomeMessage) {
            formattedMessage += messageData.welcomeMessage;
        }

        return formattedMessage;
    }
}
