import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import {
    CreateDisclaimerDto,
    UpdateDisclaimerDto,
} from '../dtos/disclaimer.dto';
import { Disclaimer } from '../entities/disclaimer.entity';
import { LanguageService } from './language.service';

@Injectable()
export class DisclaimerService {
    constructor(
        @InjectRepository(Disclaimer)
        private disclaimerRepository: Repository<Disclaimer>,
        private languageService: LanguageService,
    ) {}

    async findAll(): Promise<Disclaimer[]> {
        return this.disclaimerRepository.find({
            relations: ['language'],
        });
    }

    async findOne(id: number): Promise<Disclaimer> {
        const disclaimer = await this.disclaimerRepository.findOne({
            where: { id },
            relations: ['language'],
        });
        if (!disclaimer) {
            throw new NotFoundException(`Disclaimer with ID ${id} not found`);
        }
        return disclaimer;
    }

    async findByLanguageAndKey(
        languageCode: string,
        disclaimerKey: string,
    ): Promise<Disclaimer> {
        const language = await this.languageService.findByCode(languageCode);

        const disclaimer = await this.disclaimerRepository.findOne({
            where: { languageId: language.id, disclaimerKey },
            relations: ['language'],
        });

        if (!disclaimer) {
            throw new NotFoundException(
                `Disclaimer for language ${languageCode} and key ${disclaimerKey} not found`,
            );
        }

        return disclaimer;
    }

    async create(
        createDisclaimerDto: CreateDisclaimerDto,
    ): Promise<Disclaimer> {
        // Verify that language exists
        await this.languageService.findOne(createDisclaimerDto.languageId);

        const disclaimer = this.disclaimerRepository.create(
            createDisclaimerDto,
        );
        return this.disclaimerRepository.save(disclaimer);
    }

    async update(
        id: number,
        updateDisclaimerDto: UpdateDisclaimerDto,
    ): Promise<Disclaimer> {
        const disclaimer = await this.findOne(id);
        this.disclaimerRepository.merge(disclaimer, updateDisclaimerDto);
        return this.disclaimerRepository.save(disclaimer);
    }

    async remove(id: number): Promise<void> {
        const disclaimer = await this.findOne(id);
        await this.disclaimerRepository.remove(disclaimer);
    }
}
