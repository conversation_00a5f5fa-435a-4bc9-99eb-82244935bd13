import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import {
    CreateIntroMessageTemplateDto,
    UpdateIntroMessageTemplateDto,
} from '../dtos/intro-message-template.dto';
import { IntroMessageTemplate } from '../entities/intro-message-template.entity';

@Injectable()
export class IntroMessageTemplateService {
    constructor(
        @InjectRepository(IntroMessageTemplate)
        private templateRepository: Repository<IntroMessageTemplate>,
    ) {}

    async findAll(): Promise<IntroMessageTemplate[]> {
        return this.templateRepository.find();
    }

    async findOne(id: number): Promise<IntroMessageTemplate> {
        const template = await this.templateRepository.findOne({
            where: { id },
        });
        if (!template) {
            throw new NotFoundException(`Template with ID ${id} not found`);
        }
        return template;
    }

    async findByKey(templateKey: string): Promise<IntroMessageTemplate> {
        const template = await this.templateRepository.findOne({
            where: { templateKey },
        });
        if (!template) {
            throw new NotFoundException(
                `Template with key ${templateKey} not found`,
            );
        }
        return template;
    }

    async create(
        createTemplateDto: CreateIntroMessageTemplateDto,
    ): Promise<IntroMessageTemplate> {
        const template = this.templateRepository.create(createTemplateDto);
        return this.templateRepository.save(template);
    }

    async update(
        id: number,
        updateTemplateDto: UpdateIntroMessageTemplateDto,
    ): Promise<IntroMessageTemplate> {
        const template = await this.findOne(id);
        this.templateRepository.merge(template, updateTemplateDto);
        return this.templateRepository.save(template);
    }

    async remove(id: number): Promise<void> {
        const template = await this.findOne(id);
        await this.templateRepository.remove(template);
    }
}
