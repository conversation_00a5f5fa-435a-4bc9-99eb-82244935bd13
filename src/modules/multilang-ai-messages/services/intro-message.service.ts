import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import {
    CreateIntroMessageDto,
    UpdateIntroMessageDto,
} from '../dtos/intro-message.dto';
import { IntroMessage } from '../entities/intro-message.entity';
import { IntroMessageTemplateService } from './intro-message-template.service';
import { LanguageService } from './language.service';

@Injectable()
export class IntroMessageService {
    constructor(
        @InjectRepository(IntroMessage)
        private introMessageRepository: Repository<IntroMessage>,
        private languageService: LanguageService,
        private templateService: IntroMessageTemplateService,
    ) {}

    async findAll(): Promise<IntroMessage[]> {
        return this.introMessageRepository.find({
            relations: ['language', 'template'],
        });
    }

    async findOne(id: number): Promise<IntroMessage> {
        const message = await this.introMessageRepository.findOne({
            where: { id },
            relations: ['language', 'template'],
        });
        if (!message) {
            throw new NotFoundException(
                `Intro message with ID ${id} not found`,
            );
        }
        return message;
    }

    async findByLanguageAndTemplate(
        languageCode: string,
        templateKey: string,
    ): Promise<IntroMessage> {
        const language = await this.languageService.findByCode(languageCode);
        const template = await this.templateService.findByKey(templateKey);

        const message = await this.introMessageRepository.findOne({
            where: { languageId: language.id, templateId: template.id },
            relations: ['language', 'template'],
        });

        if (!message) {
            throw new NotFoundException(
                `Intro message for language ${languageCode} and template ${templateKey} not found`,
            );
        }

        return message;
    }

    async create(
        createMessageDto: CreateIntroMessageDto,
    ): Promise<IntroMessage> {
        // Verify that language and template exist
        await this.languageService.findOne(createMessageDto.languageId);
        await this.templateService.findOne(createMessageDto.templateId);

        const message = this.introMessageRepository.create(createMessageDto);
        return this.introMessageRepository.save(message);
    }

    async update(
        id: number,
        updateMessageDto: UpdateIntroMessageDto,
    ): Promise<IntroMessage> {
        const message = await this.findOne(id);
        this.introMessageRepository.merge(message, updateMessageDto);
        return this.introMessageRepository.save(message);
    }

    async remove(id: number): Promise<void> {
        const message = await this.findOne(id);
        await this.introMessageRepository.remove(message);
    }
}
