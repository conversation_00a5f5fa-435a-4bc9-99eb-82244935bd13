import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { CreateLanguageDto, UpdateLanguageDto } from '../dtos/language.dto';
import { Language } from '../entities/language.entity';

@Injectable()
export class LanguageService {
    constructor(
        @InjectRepository(Language)
        private languageRepository: Repository<Language>,
    ) {}

    async findAll(): Promise<Language[]> {
        return this.languageRepository.find();
    }

    async findOne(id: number): Promise<Language> {
        const language = await this.languageRepository.findOne({
            where: { id },
        });
        if (!language) {
            throw new NotFoundException(`Language with ID ${id} not found`);
        }
        return language;
    }

    async findByCode(languageCode: string): Promise<Language> {
        const language = await this.languageRepository.findOne({
            where: { languageCode },
        });
        if (!language) {
            throw new NotFoundException(
                `Language with code ${languageCode} not found`,
            );
        }
        return language;
    }

    async create(createLanguageDto: CreateLanguageDto): Promise<Language> {
        const language = this.languageRepository.create(createLanguageDto);
        return this.languageRepository.save(language);
    }

    async update(
        id: number,
        updateLanguageDto: UpdateLanguageDto,
    ): Promise<Language> {
        const language = await this.findOne(id);
        this.languageRepository.merge(language, updateLanguageDto);
        return this.languageRepository.save(language);
    }

    async remove(id: number): Promise<void> {
        const language = await this.findOne(id);
        await this.languageRepository.remove(language);
    }
}
