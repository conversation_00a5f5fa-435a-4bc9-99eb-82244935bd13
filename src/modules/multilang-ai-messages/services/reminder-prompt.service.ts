import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import {
    CreateReminderPromptDto,
    UpdateReminderPromptDto,
} from '../dtos/reminder-prompt.dto';
import { ReminderPrompt } from '../entities/reminder-prompt.entity';
import { LanguageService } from './language.service';

@Injectable()
export class ReminderPromptService {
    constructor(
        @InjectRepository(ReminderPrompt)
        private promptRepository: Repository<ReminderPrompt>,
        private languageService: LanguageService,
    ) {}

    async findAll(): Promise<ReminderPrompt[]> {
        return this.promptRepository.find({
            relations: ['language'],
            order: { displayOrder: 'ASC' },
        });
    }

    async findOne(id: number): Promise<ReminderPrompt> {
        const prompt = await this.promptRepository.findOne({
            where: { id },
            relations: ['language'],
        });
        if (!prompt) {
            throw new NotFoundException(
                `Reminder prompt with ID ${id} not found`,
            );
        }
        return prompt;
    }

    async findByLanguage(languageCode: string): Promise<ReminderPrompt[]> {
        const language = await this.languageService.findByCode(languageCode);

        return this.promptRepository.find({
            where: { languageId: language.id },
            relations: ['language'],
            order: { displayOrder: 'ASC' },
        });
    }

    async create(
        createPromptDto: CreateReminderPromptDto,
    ): Promise<ReminderPrompt> {
        // Verify that language exists
        await this.languageService.findOne(createPromptDto.languageId);

        const prompt = this.promptRepository.create(createPromptDto);
        return this.promptRepository.save(prompt);
    }

    async update(
        id: number,
        updatePromptDto: UpdateReminderPromptDto,
    ): Promise<ReminderPrompt> {
        const prompt = await this.findOne(id);
        this.promptRepository.merge(prompt, updatePromptDto);
        return this.promptRepository.save(prompt);
    }

    async remove(id: number): Promise<void> {
        const prompt = await this.findOne(id);
        await this.promptRepository.remove(prompt);
    }
}
