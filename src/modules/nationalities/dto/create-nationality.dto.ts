import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    ArrayMinSize,
    IsArray,
    IsBoolean,
    IsOptional,
    ValidateNested,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { NationalityTranslation } from '../entities/nationality-translation.entity';

export class CreateNationalityDto extends AbstractDto {
    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isWithHighPricing?: boolean;

    @ApiProperty({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @IsArray()
    @ArrayMinSize(2)
    @Expose()
    @Type(() => NationalityTranslation)
    translations: NationalityTranslation[];
}
