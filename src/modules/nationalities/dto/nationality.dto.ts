'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsOptional } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { NationalityTranslation } from '../entities/nationality-translation.entity';

export class NationalityDto extends AbstractDto {
    @IsBoolean()
    @Expose()
    @IsOptional()
    isWithHighPricing?: boolean;

    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => NationalityTranslation)
    translations?: NationalityTranslation[];
}
