import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsOptional,
    ValidateNested,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { NationalityTranslation } from '../entities/nationality-translation.entity';

export class UpdateNationalityDto extends AbstractDto {
    @IsBoolean()
    @Expose()
    @IsOptional()
    isWithHighPricing?: boolean;

    @ApiPropertyOptional({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @Type(() => NationalityTranslation)
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: NationalityTranslation[];
}
