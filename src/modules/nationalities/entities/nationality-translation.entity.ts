import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { NationalityEntity } from './nationality.entity';

@Entity('nationalities_translations')
export class NationalityTranslation extends AbstractEntity {
    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    languageCode?: string;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @ManyToOne(
        (_type) => NationalityEntity,
        (nationality) => nationality.translations,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    nationality?: NationalityEntity;
}
