import { Expose, Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { NationalityTranslation } from './nationality-translation.entity';

@Entity('nationalities')
export class NationalityEntity extends AbstractEntity {
    @Column({ default: true })
    @Expose()
    isWithHighPricing?: boolean;

    @OneToMany(
        (_type) => NationalityTranslation,
        (nationalityTranslation) => nationalityTranslation.nationality,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: NationalityTranslation[];

    @OneToMany((_type) => Patient, (patient) => patient.nationality, {
        cascade: true,
    })
    @JoinColumn()
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => Patient)
    patients?: Patient[];
}
