'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    Headers,
    Param,
    Post,
    Put,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { RoleType, SourceType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { DebugLogger } from '../../shared/services/logger.service';
import { CreateNationalityDto } from './dto/create-nationality.dto';
import { NationalityDto } from './dto/nationality.dto';
import { UpdateNationalityDto } from './dto/update-nationality.dto';
import { NationalityService } from './nationality.service';

@Controller('nationalities')
@ApiTags('nationalities')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class NationalitiesController {
    constructor(
        private nationalitiesService: NationalityService,
        private logger: DebugLogger,
    ) {}

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get nationality',
        type: NationalityDto,
    })
    getNationality(@Param('id') id: number, @I18nLang() lang: string) {
        return this.nationalitiesService.getNationality(id, lang);
    }

    @Get()
    @ApiResponse({
        description: 'Get all nationalities',
        type: [NationalityDto],
    })
    getNationalities(
        @Headers('source-type') source: SourceType,
        @I18nLang() lang: string,
    ): Promise<NationalityDto[]> {
        if (source && source === SourceType.ADMIN) {
            return this.nationalitiesService.getAllNationalitiesForAdmin();
        }
        return this.nationalitiesService.getAllNationalities(lang);
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create Nationality',
        type: CreateOperationsResponse,
    })
    createNationality(
        @Body() createNationalityDto: CreateNationalityDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.nationalitiesService.createNationality(
            createNationalityDto,
            lang,
        );
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update nationality',
        type: BasicOperationsResponse,
    })
    async updateNationality(
        @Param('id') id: number,
        @Body() updateNationalityDto: UpdateNationalityDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.nationalitiesService.updateNationality(
            id,
            updateNationalityDto,
            lang,
        );
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete nationality',
        type: BasicOperationsResponse,
    })
    async deleteNationality(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.nationalitiesService.deleteNationality(id, lang);
    }
}
