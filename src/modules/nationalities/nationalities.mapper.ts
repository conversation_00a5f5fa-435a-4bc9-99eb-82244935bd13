import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreateNationalityDto } from './dto/create-nationality.dto';
import { NationalityDto } from './dto/nationality.dto';
import { UpdateNationalityDto } from './dto/update-nationality.dto';
import { NationalityEntity } from './entities/nationality.entity';

type NationalityDTOs =
    | CreateNationalityDto
    | UpdateNationalityDto
    | NationalityDto;

@Injectable()
export class NationalityMapper extends AbstractMapper<
    NationalityDTOs,
    NationalityEntity
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }

    fromDTOToEntity(
        destination: ClassType<NationalityEntity>,
        sourceObject: NationalityDTOs,
    ): NationalityEntity {
        const nationality = super.fromDTOToEntity(destination, sourceObject);
        this.helperService.removeEmptyKeys(nationality);
        return nationality;
    }

    fromEntityToDTO(
        destination: ClassType<NationalityDto>,
        sourceObject: NationalityEntity,
    ): NationalityDto {
        const nationality = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(nationality);
        return nationality;
    }
}
