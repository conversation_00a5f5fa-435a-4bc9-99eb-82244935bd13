import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SharedModule } from '../../shared/shared.module';
import { NationalityEntity } from './entities/nationality.entity';
import { NationalitiesController } from './nationalities.controller';
import { NationalityMapper } from './nationalities.mapper';
import { NationalityService } from './nationality.service';
import { NationalityTranslation } from './entities/nationality-translation.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            NationalityEntity,
            NationalityTranslation,
        ]),
        SharedModule,
    ],
    controllers: [NationalitiesController],
    exports: [NationalityService],
    providers: [NationalityService, NationalityMapper],
})
export class NationalitiesModule {}
