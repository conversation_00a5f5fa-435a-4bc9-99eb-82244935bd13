import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { Repository } from 'typeorm';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { DebugLogger } from '../../shared/services/logger.service';
import { tryCatch } from '../../utils/helpers';
import { CreateNationalityDto } from './dto/create-nationality.dto';
import { NationalityDto } from './dto/nationality.dto';
import { UpdateNationalityDto } from './dto/update-nationality.dto';
import { NationalityTranslation } from './entities/nationality-translation.entity';
import { NationalityEntity } from './entities/nationality.entity';
import { NationalityMapper } from './nationalities.mapper';

@Injectable()
export class NationalityService {
    constructor(
        @InjectRepository(NationalityEntity)
        private readonly nationalitiesRepository: Repository<NationalityEntity>,
        @InjectRepository(NationalityTranslation)
        private readonly nationalitiesTranslationRepository: Repository<NationalityTranslation>,
        private readonly nationalityMapper: NationalityMapper,
        private readonly logger: DebugLogger,
        private readonly i18n: I18nService,
        private readonly helperService: HelperService,
    ) {}

    async createNationality(
        createNationalityDto: CreateNationalityDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const nationalityEntity = this.nationalityMapper.fromDTOToEntity(
            NationalityEntity,
            createNationalityDto,
        );
        const nationality =
            this.nationalitiesRepository.create(nationalityEntity);
        const createNationality = await this.nationalitiesRepository.save(
            nationality,
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate('', {
                lang,
            }),
            createdId: createNationality.id,
        };
    }

    async updateNationality(
        id: number,
        updateNationalityDto: UpdateNationalityDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const nationality = await this.nationalitiesRepository.findOne({
            where: { id },
        });

        await this.ifIsNotFound(nationality, '', lang);

        const nationalityEntity = this.nationalityMapper.fromDTOToEntity(
            NationalityEntity,
            updateNationalityDto,
        );

        if (nationalityEntity.translations) {
            await this.updateTranslation(id, nationalityEntity.translations);
            delete nationalityEntity.translations;
        }

        await this.nationalitiesRepository.update({ id }, nationalityEntity);

        return {
            isSuccessful: true,
            message: await this.i18n.translate('', { lang }),
        };
    }

    async getNationality(id: number, lang: string): Promise<NationalityDto> {
        const nationality = await this.nationalitiesRepository.findOne({
            where: { id },
            relations: ['translations'],
        });

        await this.ifIsNotFound(nationality, '', lang);

        return this.nationalityMapper.fromDTOToEntity(
            NationalityDto,
            nationality,
        );
    }

    async getAllNationalitiesForAdmin(): Promise<NationalityDto[]> {
        const { isSuccess, value, error } = await tryCatch(
            this.nationalitiesRepository.find({
                relations: ['translations'],
            }),
        );

        if (!isSuccess) {
            throw new HttpException(
                {
                    message: error.message,
                },
                HttpStatus.NOT_FOUND,
            );
        }

        return value.map((nationality) =>
            this.nationalityMapper.fromEntityToDTO(NationalityDto, nationality),
        );
    }

    async getAllNationalities(lang: string): Promise<NationalityDto[]> {
        const nationalities = await this.nationalitiesRepository
            .createQueryBuilder('nationality')
            .leftJoinAndSelect(
                'nationality.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .getMany();

        const nationalityDtos = nationalities.map((nationality) =>
            this.nationalityMapper.fromEntityToDTO(NationalityDto, nationality),
        );

        return nationalityDtos
            .filter((nationality) => nationality.translations.length > 0)
            .map((nationality) => ({
                ...nationality,
                name: nationality.translations[0].name,
            }));
    }

    private async updateTranslation(
        parentId: number,
        updatedTranslations: NationalityTranslation[],
    ) {
        await Promise.all(
            updatedTranslations.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);

                await this.nationalitiesTranslationRepository
                    .createQueryBuilder()
                    .update()
                    .set(translation)
                    .where('nationality_id = :parentId', { parentId })
                    .andWhere('languageCode = :languageCode', {
                        languageCode: translation.languageCode,
                    })
                    .execute();
            }),
        );
    }

    async deleteNationality(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const nationality = await this.nationalitiesRepository.findOne({
            where: { id },
        });

        await this.ifIsNotFound(nationality, '', lang);

        await this.nationalitiesRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate('', {
                lang,
            }),
        };
    }

    async ifIsNotFound(
        object: unknown,
        message: string,
        lang: string,
    ): Promise<void> {
        if (!object) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(message, {
                        lang,
                    }),
                },
                HttpStatus.NOT_FOUND,
            );
        }
    }
}
