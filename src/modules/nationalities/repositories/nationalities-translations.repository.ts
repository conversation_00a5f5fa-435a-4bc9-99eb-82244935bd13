import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { NationalityTranslation } from '../entities/nationality-translation.entity';

@Injectable()
export class NationalitiesTranslationsRepository extends Repository<
    NationalityTranslation
> {
    constructor(dataSource: DataSource) {
        super(NationalityTranslation, dataSource.createEntityManager());
    }
}
