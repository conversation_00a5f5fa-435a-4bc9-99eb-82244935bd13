'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNumber, IsOptional, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { INotificationData } from '../../../interfaces/INotifcation';
import { NotificationTranslation } from '../entities/notification-translation.entity';

export class CreateNotificationDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    data: INotificationData;

    @ApiProperty()
    @IsNumber()
    @Expose()
    userId: number;

    @ApiPropertyOptional()
    @Expose()
    @ValidateNested()
    @Type(() => NotificationTranslation)
    @IsOptional()
    notificationTranslations?: NotificationTranslation[];
}
