'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { INotificationData } from '../../../interfaces/INotifcation';
import { UserDto } from '../../users/dto/user.dto';

export class GetNotificationDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    data?: INotificationData;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    seen?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @Type(() => UserDto)
    @IsOptional()
    user?: UserDto;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    title?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    @Expose()
    body?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    createdAt?: Date;
}
