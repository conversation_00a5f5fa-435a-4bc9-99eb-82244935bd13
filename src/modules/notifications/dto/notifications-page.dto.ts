import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { Notification } from '../entities/notification.entity';

export class NotificationsPageDto {
    @ApiProperty({
        type: Notification,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => Notification)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: Notification[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: Notification[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
