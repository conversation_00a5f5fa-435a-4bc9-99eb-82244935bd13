'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class UpdateNotificationDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsString()
    title: string;

    @ApiProperty()
    @Expose()
    @IsString()
    body: string;
}
