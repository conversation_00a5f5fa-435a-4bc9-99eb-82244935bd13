import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Notification } from './notification.entity';

@Entity('notifications_translations')
export class NotificationTranslation extends AbstractEntity {
    @Column()
    @Expose()
    public title: string;

    @Column()
    @Expose()
    public body: string;

    @Column()
    @Expose()
    languageCode: string;

    @ManyToOne(
        () => Notification,
        (notification) => notification.notificationTranslations,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    @Expose()
    notification?: Notification;
}
