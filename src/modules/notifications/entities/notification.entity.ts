'use strict';

import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { INotificationData } from '../interfaces/INotifcation';
import { NotificationTranslation } from './notification-translation.entity';

@Entity('notifications')
export class Notification extends AbstractEntity {
    @Column({ type: 'json' })
    @Expose()
    data: INotificationData;

    @Column({ default: false })
    @Expose()
    seen: boolean;

    @ManyToOne(() => UserEntity, (user) => user.notifications, {
        onDelete: 'CASCADE',
    })
    @Expose()
    user: UserEntity;

    @OneToMany(
        () => NotificationTranslation,
        (notificationTranslation) => notificationTranslation.notification,
        {
            cascade: true,
        },
    )
    @Expose()
    notificationTranslations: NotificationTranslation[];
}
