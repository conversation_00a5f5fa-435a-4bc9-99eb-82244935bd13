'use strict';

import {
    Controller,
    Get,
    Param,
    Put,
    Query,
    Req,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    Api<PERSON>earerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { DoctorForPatientDto } from '../doctors/dto/doctor-for-patient.dto';
import { GetNotificationDto } from './dto/get-notification.dto';
import { NotificationsPageDto } from './dto/notifications-page.dto';
import { NotificationsService } from './notifications.service';

@Controller('notifications')
@ApiTags('notifications')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class NotificationsController {
    constructor(private notificationService: NotificationsService) {}
    /*********************** CRUD Operations *****************************/

    @Get()
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get Notifications',
        type: [NotificationsPageDto],
    })
    getPaginatedNotifications(
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<NotificationsPageDto> {
        return this.notificationService.listNotifications(
            pageOptionsDto,
            req.user.id,
            lang,
        );
    }

    @Get('unseen/count')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT, RoleType.DOCTOR)
    @ApiResponse({
        description: 'get un seen notifications count',
        type: Number,
    })
    getunSeenNotificationCount(@Req() req: Request): Promise<number> {
        return this.notificationService.getunSeenNotificationCount(req.user.id);
    }

    @Put('/seen/:id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT, RoleType.DOCTOR)
    @ApiResponse({
        description: 'mark notification as seen',
        type: BasicOperationsResponse,
    })
    markAsSeen(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.notificationService.markAsSeen(req.user.id, id, lang);
    }

    @Put('/seen')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT, RoleType.DOCTOR)
    @ApiResponse({
        description: 'mark all notification as seen',
        type: BasicOperationsResponse,
    })
    markAllAsSeen(
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.notificationService.markAllAsSeen(req.user.id, lang);
    }
}
