import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { UserEntity } from '../users/entities/user.entity';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { GetNotificationDto } from './dto/get-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { Notification } from './entities/notification.entity';

type NotificationDto =
    | CreateNotificationDto
    | GetNotificationDto
    | UpdateNotificationDto;
@Injectable()
export class NotificationsMapper extends AbstractMapper<
    NotificationDto,
    Notification
> {
    fromDTOToEntity(
        destination: ClassType<Notification>,
        sourceObject: CreateNotificationDto,
    ): Notification {
        const notification = super.fromDTOToEntity(destination, sourceObject);

        const user = new UserEntity();
        user.id = sourceObject.userId;
        notification.user = user;

        return notification;
    }

    fromEntityToDTO(
        destination: ClassType<GetNotificationDto>,
        sourceObject: Notification,
    ): GetNotificationDto {
        return super.fromEntityToDTO(destination, sourceObject);
    }
}
