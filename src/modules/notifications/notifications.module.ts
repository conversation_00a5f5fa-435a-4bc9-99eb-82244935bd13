import { Modu<PERSON> } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { UsersModule } from '../users/users.module';
import { NotificationsController } from './notifications.controller';
import { NotificationsMapper } from './notifications.mapper';
import { NotificationsService } from './notifications.service';
import { NotificationsTranslationsRepository } from './repositories/notifications-translations.repository';
import { NotificationsRepository } from './repositories/notifications.repository';

@Module({
    imports: [SharedModule, UsersModule],
    controllers: [NotificationsController],
    exports: [NotificationsService],
    providers: [
        NotificationsService,
        NotificationsMapper,
        NotificationsRepository,
        NotificationsTranslationsRepository,
    ],
})
export class NotificationsModule {}
