import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { SortingType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { FCMService } from '../../shared/services/fcm.service';
import { UsersService } from '../users/users.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { GetNotificationDto } from './dto/get-notification.dto';
import { NotificationsPageDto } from './dto/notifications-page.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { Notification } from './entities/notification.entity';
import { NotificationsMapper } from './notifications.mapper';
import { NotificationsTranslationsRepository } from './repositories/notifications-translations.repository';
import { NotificationsRepository } from './repositories/notifications.repository';
import { NotificationMessagesKeys } from './translate.enum';

@Injectable()
export class NotificationsService {
    constructor(
        private readonly notificationsRepository: NotificationsRepository,
        private readonly notificationsTranslationsRepository: NotificationsTranslationsRepository,
        private readonly notificationsMapper: NotificationsMapper,
        private readonly fcmService: FCMService,
        private readonly userService: UsersService,
        private readonly i18n: I18nService,
    ) { }

    async createNotification(
        notificationDto: CreateNotificationDto,
    ): Promise<CreateOperationsResponse> {
        const notification = this.notificationsMapper.fromDTOToEntity(
            Notification,
            notificationDto,
        );

        const createdNotification =
            this.notificationsRepository.create(notification);
        const { id } = await this.notificationsRepository.save(
            createdNotification,
        );
        return {
            createdId: id,
            isSuccessful: true,
            message: await this.i18n.translate(
                NotificationMessagesKeys.CREATED_SUCCESSFULLY,
            ),
        };
    }

    async updateNotification(
        id: number,
        notificationDto: UpdateNotificationDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const notification = await this.notificationsRepository
            .createQueryBuilder('notification')
            .leftJoinAndSelect(
                'notification.notificationTranslations',
                'notificationTranslations',
                'notificationTranslations.languageCode = :lang',
                { lang },
            )
            .where('notification.id = :id', { id })
            .getOne();
        if (!notification) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        NotificationMessagesKeys.NOTIFICATION_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },

                HttpStatus.BAD_REQUEST,
            );
        }

        if (notification.notificationTranslations.length <= 0) {
            const notitifactionTranslation =
                this.notificationsTranslationsRepository.create({
                    languageCode: lang,
                    title: notificationDto.title,
                    body: notificationDto.body,
                    notification: {
                        id,
                    },
                });
            await this.notificationsTranslationsRepository.save(
                notitifactionTranslation,
            );
        } else {
            if (notificationDto.title) {
                notification.notificationTranslations[0].title =
                    notificationDto.title;
            }
            if (notificationDto.body) {
                notification.notificationTranslations[0].body =
                    notificationDto.body;
            }
            await this.notificationsTranslationsRepository.update(
                notification.notificationTranslations[0].id,
                notification.notificationTranslations[0],
            );
        }

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                NotificationMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async sendNotification(
        notificationId: number,
        userId: number,
        lang: string,
    ): Promise<void> {
        const notification = await this.notificationsRepository
            .createQueryBuilder('notification')
            .leftJoinAndSelect(
                'notification.notificationTranslations',
                'notificationTranslations',
                'notificationTranslations.languageCode = :lang',
                { lang },
            )
            .where('notification.id = :id', { id: notificationId })
            .getOne();
        if (!notification) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        NotificationMessagesKeys.NOTIFICATION_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },

                HttpStatus.BAD_REQUEST,
            );
        }

        const token = await this.userService.getFcmTokenByUserId(userId);

        const { data, notificationTranslations } = notification;
        const { body, title } = notificationTranslations[0];

        await this.fcmService.sendNotification(title, body, data, token);
    }

    async listNotifications(
        pageOptionsDto: PageOptionsDto,
        userId: number,
        lang: string,
    ): Promise<NotificationsPageDto> {
        const query = this.notificationsRepository
            .createQueryBuilder('notification')
            .leftJoinAndSelect('notification.user', 'user')
            .leftJoinAndSelect(
                'notification.notificationTranslations',
                'notificationTranslations',
                'notificationTranslations.languageCode = :lang',
                { lang },
            )
            .where('user.id = :id', { id: userId })
            .select([
                'user.id',
                'notification.id',
                'notification.data',
                'notification.seen',
                'notification.createdAt',
                'notificationTranslations',
            ])
            .orderBy('notification.createdAt', SortingType.DESC);
        const [notifications, pageMetaDto] = await query.paginate(
            pageOptionsDto,
        );

        return new NotificationsPageDto(notifications, pageMetaDto);
    }

    async markAsSeen(
        userId: number,
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        try {
            await this.notificationsRepository.update(
                { id, user: { id: userId } },
                { seen: true },
            );

            return {
                message: await this.i18n.translate(
                    NotificationMessagesKeys.UPDATED_SUCCESSFULY,
                    {
                        lang,
                    },
                ),
                isSuccessful: true,
            };
        } catch (error) {
            throw new HttpException(
                await this.i18n.translate(
                    NotificationMessagesKeys.UPDATE_FAILED,
                    {
                        lang,
                    },
                ),
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    async markAllAsSeen(
        userId: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        try {
            await this.notificationsRepository.update(
                { user: { id: userId } },
                { seen: true },
            );

            return {
                message: await this.i18n.translate(
                    NotificationMessagesKeys.UPDATED_SUCCESSFULY,
                    {
                        lang,
                    },
                ),
                isSuccessful: true,
            };
        } catch (error) {
            throw new HttpException(
                await this.i18n.translate(
                    NotificationMessagesKeys.UPDATE_FAILED,
                    {
                        lang,
                    },
                ),
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    async getunSeenNotificationCount(userId: number): Promise<number> {
        try {
            return this.notificationsRepository.count({
                where: {
                    user: { id: userId },
                    seen: false,
                },
            });
        } catch (error) {
            return 0;
        }
    }
}
