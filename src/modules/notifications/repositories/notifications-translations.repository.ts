import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { NotificationTranslation } from '../entities/notification-translation.entity';

@Injectable()
export class NotificationsTranslationsRepository extends Repository<
    NotificationTranslation
> {
    constructor(dataSource: DataSource) {
        super(NotificationTranslation, dataSource.createEntityManager());
    }
}
