import { Type } from 'class-transformer';
import {
    IsArray,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';

import { DetailDto } from './package-detail.dto';

export class CreatePackageDto {
    @IsString()
    @IsNotEmpty()
    name: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsString()
    @IsOptional()
    diagnosis?: string;

    @IsString()
    @IsOptional()
    imagingInvestigation?: string;

    @IsString()
    @IsOptional()
    labInvestigation?: string;

    @IsString()
    @IsOptional()
    surgeryDayEssentials?: string;

    @IsNumber()
    price: number;

    @IsNumber()
    duration: number;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DetailDto)
    @IsOptional()
    details?: DetailDto[];
}
