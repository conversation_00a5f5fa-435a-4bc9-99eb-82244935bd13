import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Package } from './package.entity';

@Entity('package_details')
export class PackageDetail extends AbstractEntity {
    @ManyToOne(() => Package, (pkg) => pkg.details, {
        onDelete: 'CASCADE',
    })
    package: Package;

    @Column({ length: 255 })
    detailKey: string;

    @Column({ type: 'text' })
    detailValue: string;
}
