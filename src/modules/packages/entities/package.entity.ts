import {
    Column,
    <PERSON>tity,
    ManyToOne,
    OneToMany,
} from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Booking } from '../../booking/entities/booking.entity';
import { Review } from '../../booking/entities/review.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { PackageDetail } from './package-detail.entity';

@Entity('packages')
export class Package extends AbstractEntity {
    @ManyToOne(() => ServiceProvider, (provider) => provider.packages, {
        onDelete: 'CASCADE',
    })
    serviceProvider: ServiceProvider;

    @ManyToOne(() => DoctorEntity, (doctor) => doctor.packages, {
        onDelete: 'CASCADE',
    })
    doctor: DoctorEntity;

    @Column({ length: 255 })
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'text', nullable: true })
    diagnosis: string;

    @Column({ name: 'imaging_investigation', type: 'text', nullable: true })
    imagingInvestigation: string;

    @Column({ name: 'lap_investigation', type: 'text', nullable: true })
    lapInvestigation: string;

    @Column({ name: 'surgery_day_essentials', type: 'text', nullable: true })
    surgeryDayEssentials: string;

    @Column({ type: 'decimal', precision: 10, scale: 2 })
    price: number;

    @Column({ type: 'int' })
    duration: number; // in minutes

    @OneToMany(() => PackageDetail, (detail) => detail.package)
    details: PackageDetail[];

    @OneToMany(() => Booking, (booking) => booking.package)
    bookings: Booking[];

    @OneToMany(() => Review, (review) => review.package)
    reviews: Review[];
}
