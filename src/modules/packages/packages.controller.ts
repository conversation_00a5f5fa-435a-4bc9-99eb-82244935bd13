import {
    BadRequestException,
    Body,
    Controller,
    Delete,
    ForbiddenException,
    Get,
    HttpCode,
    HttpStatus,
    InternalServerErrorException,
    Param,
    Post,
    Put,
    Req,
    Res,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>A<PERSON>, ApiHeader, ApiTags } from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { Request, Response } from 'express';
import { File } from 'multer';
import { I18nLang } from 'nestjs-i18n';
import * as path from 'path';

import { RoleType } from '../../common/constants/types';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { localFileInterceptor } from '../../interceptors/LocalFileInterceptor';
import { QueryDto } from '../../shared/dtos/QueryDto';
import { DebugLogger } from '../../shared/services/logger.service';
import { QueryParser } from '../../shared/services/QueryParser';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { PackagesService } from './packages.service';

@Controller('packages')
@ApiTags('packages')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class PackagesController {
    constructor(
        private readonly packagesService: PackagesService,
        private readonly logger: DebugLogger,
    ) {
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    create(@Body() createPackageDto: CreatePackageDto) {
        return this.packagesService.create(createPackageDto);
    }

    @Get()
    @UseGuards(AuthGuard, RolesGuard)
    async findAll(
        @Req() req: Request,
        @I18nLang() lang: string,
    ) {
        try {
            const query = QueryParser.parseRequest(req);

            const queryDto = plainToClass(QueryDto, query);
            const errors = await validate(queryDto);

            if (errors.length > 0) {
                throw new BadRequestException('Invalid query parameters');
            }
            switch (req.user.role) {
                case RoleType.SUPER_ADMIN:
                case RoleType.ADMIN:
                    return this.packagesService.findAll(lang);
                case RoleType.DOCTOR:
                case RoleType.SERVICE_PROVIDER:
                case RoleType.PATIENT:
                    return this.packagesService.getAll(
                        req.user.role,
                        req.user.id,
                        queryDto,
                    );
                default:
                    throw new ForbiddenException();
            }
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new InternalServerErrorException('Error processing request');
        }
    }

    @Get('/portal')
    @HttpCode(HttpStatus.OK)
    portal(@Res() res: Response) {
        const htmlPath = path.join(
            process.cwd(),
            '/views/packages-uploader.hbs',
        );
        res.render(htmlPath);
    }

    @Post('send/email')
    @UseGuards(AuthGuard, RolesGuard)
    sendEmail(@Req() req: Request, @I18nLang() lang: string) {
        // eslint-disable-next-line eqeqeq
        if (req.user.role == RoleType.DOCTOR) {
            const url = `${req.protocol}://${req.get('host')}`;
            return this.packagesService.sendEmail(url, req.user.id, lang);
        }
    }

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    findOne(@Param('id') id: string) {
        return this.packagesService.findOne(+id);
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    update(
        @Param('id') id: string,
        @Body() updatePackageDto: UpdatePackageDto,
    ) {
        return this.packagesService.update(+id, updatePackageDto);
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    remove(@Param('id') id: string) {
        return this.packagesService.delete(+id);
    }

    @Post('upload/service-provider/:id')
    @UseGuards(AuthGuard, RolesGuard)
    @UseInterceptors(
        localFileInterceptor({
            filter: /\.(xlsx|xls)$/,
            destination: './upload/packages',
            fieldName: 'file',
        }),
    )
    async uploadPackageForServiceProvider(
        @Param('id') id: string,
        @UploadedFile() file: File,
    ) {
        return this.packagesService.uploadPackagesForServiceProvider(+id, file);
    }

    @Post('upload/doctors')
    @UseGuards(AuthGuard, RolesGuard)
    @UseInterceptors(
        localFileInterceptor({
            filter: /\.(xlsx|xls)$/,
            destination: './upload/packages',
            fieldName: 'file',
        }),
    )
    async uploadPackageForDoctor(
        @UploadedFile() file: File,
        @Req() req: Request,
    ) {
        return this.packagesService.uploadPackagesForDoctor(req.user.id, file);
    }

    @Get('excel/template')
    @UseGuards(AuthGuard, RolesGuard)
    downloadTemplate(@Res() res: Response) {
        const templateBuffer = this.packagesService.downloadTemplate();

        res.setHeader(
            'Content-Type',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );

        res.setHeader(
            'Content-Disposition',
            'attachment; filename="packages-template.xlsx"',
        );

        res.status(200).end(templateBuffer);
    }
}
