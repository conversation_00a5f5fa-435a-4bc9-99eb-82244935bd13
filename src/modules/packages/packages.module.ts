import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SharedModule } from '../../shared/shared.module';
import { DoctorsRepository } from '../doctors/repositories/doctors.repository';
import { ServiceProvidersRepository } from '../service-providers/repositories/service-providers.repository';
import { UserEntity } from '../users/entities/user.entity';
import { PackagesController } from './packages.controller';
import { PackagesService } from './packages.service';
import { PackageDetailsRepository } from './repositories/package-details.repository';
import { PackagesRepository } from './repositories/packages.repository';

@Module({
    imports: [
        TypeOrmModule.forFeature([UserEntity]),
        SharedModule,
        JwtModule.register({}),
    ],
    controllers: [PackagesController],
    providers: [
        PackagesService,
        PackagesRepository,
        PackageDetailsRepository,
        ServiceProvidersRepository,
        DoctorsRepository,
    ],
    exports: [PackagesService],
})
export class PackagesModule {
}
