import {
    HttpException,
    HttpStatus,
    Injectable,
    NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { readFileSync } from 'fs';
import { readFile } from 'fs/promises';
import { handlebars } from 'hbs';
import { File } from 'multer';
import { I18nService } from 'nestjs-i18n';
import * as path from 'path';
import { Repository, SelectQueryBuilder } from 'typeorm';
import * as XLSX from 'xlsx';

import {
    FilterByType,
    FilterType,
    RoleType,
    SortByType,
} from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { ConfigService } from '../../config/config.service';
import {
    IFilter,
    IHttpQuery,
    IPaginatedResponse,
} from '../../interfaces/IHttpQuery';
import { EmailService } from '../../shared/services/email.service';
import { DebugLogger } from '../../shared/services/logger.service';
import { DoctorEntity } from '../doctors/entities/doctor.entity';
import { DoctorsRepository } from '../doctors/repositories/doctors.repository';
import { DoctorMessagesKeys } from '../doctors/translate.enum';
import { ServiceProvider } from '../service-providers/entities/service-provider.entity';
import { ServiceProvidersRepository } from '../service-providers/repositories/service-providers.repository';
import { ServiceProviderMessagesKeys } from '../service-providers/translate.enum';
import { UserEntity } from '../users/entities/user.entity';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { PackageDetail } from './entities/package-detail.entity';
import { Package } from './entities/package.entity';
import { PackageDetailsRepository } from './repositories/package-details.repository';
import { PackagesRepository } from './repositories/packages.repository';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class PackagesService {
    constructor(
        private readonly packageRepository: PackagesRepository,
        private readonly detailRepository: PackageDetailsRepository,
        private readonly serviceProvidersRepository: ServiceProvidersRepository,
        private readonly doctorsRepository: DoctorsRepository,
        @InjectRepository(UserEntity)
        public readonly userRepository: Repository<UserEntity>,
        private readonly emailService: EmailService,
        private readonly i18n: I18nService,
        private readonly configService: ConfigService,
        private readonly jwtService: JwtService,
        private logger: DebugLogger,
    ) {
    }

    async findAll(lang: string): Promise<Package[]> {
        const query = this.packageRepository
            .createQueryBuilder('package')
            .leftJoinAndSelect('package.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'spUser')
            .leftJoinAndSelect('package.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'dUser')
            .leftJoinAndSelect('package.details', 'details')
            .select([
                'package.id',
                'package.name',
                'package.price',
                'package.duration',
                'package.description',
                'package.diagnosis',
                'package.imagingInvestigation',
                'package.lapInvestigation',
                'serviceProvider.id',
                'spUser.id ',
                'spUser.name',
                'dUser.id',
                'doctor.id',
                'dUser.name',
                'details.id',
                'details.detailKey',
                'details.detailValue',
            ]);
        return query.getMany();
    }

    async getAll(
        type: RoleType,
        userId: number,
        httpQueryObject: IHttpQuery,
    ): Promise<IPaginatedResponse<Package>> {
        let baseQuery = this.packageRepository
            .createQueryBuilder('package')
            .leftJoinAndSelect('package.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'spUser')
            .leftJoinAndSelect('package.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'dUser')
            .leftJoinAndSelect('package.details', 'details')
            .select([
                'package.id',
                'package.name',
                'package.price',
                'package.duration',
                'package.description',
                'package.diagnosis',
                'package.imagingInvestigation',
                'package.lapInvestigation',
                'serviceProvider.id',
                'spUser.id ',
                'spUser.name',
                'dUser.id',
                'doctor.id',
                'dUser.name',
                'details.id',
                'details.detailKey',
                'details.detailValue',
            ]);

        switch (type) {
            case RoleType.DOCTOR:
                baseQuery.where('dUser.id = :id', {
                    id: userId,
                });
                break;
            case RoleType.SERVICE_PROVIDER:
                baseQuery.where('spUser.id = :id', {
                    id: userId,
                });
                break;
            default:
                '';
        }
        baseQuery = this.buildGetPackagesQuery(baseQuery, httpQueryObject);

        const { page, perPage, total, lastPage } = await this.getPageProps(
            httpQueryObject,
            baseQuery,
        );

        const data = await baseQuery.getMany();

        return {
            data,
            meta: {
                total,
                page,
                lastPage,
                perPage,
            },
        };
    }

    private async getPageProps(
        httpQueryObject: IHttpQuery,
        baseQuery: SelectQueryBuilder<Package>,
    ) {
        // Apply pagination
        const page = httpQueryObject?.pagination?.page || 1;
        const perPage = httpQueryObject?.pagination?.perPage || 10;
        const skip = (page - 1) * perPage;

        // Get total count
        const total = await baseQuery.getCount();
        //
        // // Apply pagination to query
        baseQuery.skip(skip).take(perPage);
        //
        // // Calculate pagination metadata
        const lastPage = Math.ceil(total / perPage);
        return { page, perPage, total, lastPage };
    }

    buildGetPackagesQuery(
        query: SelectQueryBuilder<Package>,
        httpQueryObject: IHttpQuery,
    ): SelectQueryBuilder<Package> {
        if (httpQueryObject?.search) {
            const { by, value } = httpQueryObject.search;

            if (by === 'name') {
                query.andWhere(
                    `(
                    package.name ILIKE :searchTerm OR
                    dUser.name ILIKE :searchTerm OR
                    spUser.name ILIKE :searchTerm
                )`,
                    { searchTerm: `%${value}%` },
                );
            }
        }

        if (httpQueryObject?.filters?.length) {
            httpQueryObject.filters.forEach((filter, index) => {
                switch (filter.type) {
                    case FilterType.FIXED:
                        this.applyFixedFilter(query, filter, index);
                        break;
                    case FilterType.RANGE:
                        this.applyRangeFilter(query, filter, index);
                        break;
                    case FilterType.MULTIPLE:
                        this.applyMultipleFilter(query, filter, index);
                }
            });
        }

        if (httpQueryObject?.sort) {
            const { by, type } = httpQueryObject.sort;
            switch (by) {
                case SortByType.FEES:
                    query.orderBy('package.price', type);
                    break;
                case SortByType.RATING:
                    query.orderBy('doctor.rating', type);
                    break;
                case SortByType.POPULARITY:
                    query.orderBy('doctor.popularity', type);
            }
        }

        return query;
    }

    private applyFixedFilter(
        query: SelectQueryBuilder<Package>,
        filter: IFilter,
        index: number,
    ) {
        const paramKey = `fixedValue${index}`;
        switch (filter.by) {
            case FilterByType.GENDER:
                query.andWhere('dUser.gender = :' + paramKey, {
                    [paramKey]: filter.value,
                });
                break;
            case FilterByType.TYPE:
                query.andWhere('serviceProvider.type = :' + paramKey, {
                    [paramKey]: filter.value,
                });
            // case FilterByType.ONLINE:
            //     query.andWhere('package.isOnline = :' + paramKey, {
            //         [paramKey]: filter.value === 'true',
            //     });
            //     break;
            // case FilterByType.HOME:
            //     query.andWhere('package.isHomeVisit = :' + paramKey, {
            //         [paramKey]: filter.value === 'true',
            //     });
        }
    }

    private applyRangeFilter(
        query: SelectQueryBuilder<Package>,
        filter: IFilter,
        index: number,
    ) {
        const minKey = `min${index}`;
        const maxKey = `max${index}`;

        switch (filter.by) {
            case FilterByType.RATING:
                if (filter.min !== undefined) {
                    query.andWhere('doctor.rating >= :' + minKey, {
                        [minKey]: filter.min,
                    });
                }
                if (filter.max !== undefined) {
                    query.andWhere('doctor.rating <= :' + maxKey, {
                        [maxKey]: filter.max,
                    });
                }
                break;
            case FilterByType.DATE:
                if (filter.min !== undefined) {
                    query.andWhere('package.availableDate >= :' + minKey, {
                        [minKey]: filter.min,
                    });
                }
                if (filter.max !== undefined) {
                    query.andWhere('package.availableDate <= :' + maxKey, {
                        [maxKey]: filter.max,
                    });
                }
        }
    }

    private applyMultipleFilter(
        query: SelectQueryBuilder<Package>,
        filter: IFilter,
        index: number,
    ) {
        const paramKey = `multipleValues${index}`;
        switch (filter.by) {
            case FilterByType.SPECIALITY:
                query.andWhere('doctor.speciality IN (:...' + paramKey + ')', {
                    [paramKey]: filter.values,
                });
                break;
            case FilterByType.GRADE:
                query.andWhere('doctor.grade IN (:...' + paramKey + ')', {
                    [paramKey]: filter.values,
                });
        }
    }

    // Get a single package by ID
    async findOne(id: number): Promise<Package> {
        const pkg = await this.packageRepository
            .createQueryBuilder('package')
            .leftJoinAndSelect('package.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'spUser')
            .leftJoinAndSelect('package.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'dUser')
            .leftJoinAndSelect('package.details', 'details')
            .leftJoinAndSelect('package.reviews', 'reviews')
            .leftJoinAndSelect('reviews.user', 'reviewUser')
            .select([
                'package.id',
                'package.name',
                'package.price',
                'package.duration',
                'package.description',
                'package.diagnosis',
                'package.imagingInvestigation',
                'package.lapInvestigation',
                'package.surgeryDayEssentials',
                'serviceProvider.id',
                'spUser.id ',
                'spUser.name',
                'dUser.id',
                'doctor.id',
                'dUser.name',
                'details.id',
                'details.detailKey',
                'details.detailValue',
                'reviews.id',
                'reviews.rating',
                'reviews.comment',
                'reviewUser.name',
            ])
            .where({
                id,
            })
            .getOne();
        if (!pkg) {
            throw new NotFoundException(`Package with ID ${id} not found`);
        }
        return pkg;
    }

    // Create a new package with surgery details
    async create(createPackageDto: CreatePackageDto): Promise<Package> {
        const { details, ...packageData } = createPackageDto;

        const newPackage = this.packageRepository.create(packageData);
        const savedPackage = await this.packageRepository.save(newPackage);

        // Save surgery details if provided
        if (details && details.length > 0) {
            const packageDetails = details.map((detail) =>
                this.detailRepository.create({
                    ...detail,
                    package: savedPackage,
                }),
            );
            await this.detailRepository.save(packageDetails);
        }

        return this.findOne(savedPackage.id); // Return with relations
    }

    // Update a package and its surgery details
    async update(
        id: number,
        updatePackageDto: UpdatePackageDto,
    ): Promise<Package> {
        try {
            const existingPackage = await this.findOne(id);

            const { details, ...packageData } = updatePackageDto;

            this.logger.debug(JSON.stringify(updatePackageDto));

            // Update package fields
            await this.packageRepository.update(id, packageData);

            // Update surgery details
            if (details) {
                // Delete existing details
                await this.detailRepository.delete({ package: { id } });

                // Add new details
                const packageDetails = details.map((detail) =>
                    this.detailRepository.create({
                        ...detail,
                        package: existingPackage,
                    }),
                );
                await this.detailRepository.save(packageDetails);
            }
        } catch (e) {
            this.logger.error(e.message);
        }

        return this.findOne(id); // Return updated package
    }

    // Delete a package
    async delete(id: number): Promise<BasicOperationsResponse> {
        const pkg = await this.findOne(id);
        await this.packageRepository.remove(pkg);
        return {
            message: 'Package is deleted successfully',
            isSuccessful: true,
        };
    }

    downloadTemplate(): Buffer {
        const headers = [
            [
                'PackageName',
                'Description',
                'Price',
                'Duration',
                'Diagnosis',
                'ImagingInvestigation',
                'lapInvestigation',
                'SurgeryDayEssentials',
                'Key',
                'Value',
            ],
        ];

        const worksheet = XLSX.utils.aoa_to_sheet(headers);

        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');

        return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    }

    async uploadPackagesForServiceProvider(
        serviceProviderId: number,
        file: File,
        lang = 'ar',
    ): Promise<BasicOperationsResponse> {
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: {
                id: serviceProviderId,
            },
        });

        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        return this.uploadPackagesFromExcel(file, serviceProvider);
    }

    async uploadPackagesForDoctor(
        userId: number,
        file: File,
        lang = 'ar',
    ): Promise<BasicOperationsResponse> {
        const doctor = await this.doctorsRepository.findOne({
            where: {
                user: {
                    id: userId,
                },
            },
        });
        this.logger.log(JSON.stringify(doctor));

        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        DoctorMessagesKeys.DOCTOR_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        return this.uploadPackagesFromExcel(file, null, doctor);
    }

    async uploadPackagesFromExcel(
        file: File,
        serviceProvider?: ServiceProvider,
        doctor?: DoctorEntity,
    ): Promise<BasicOperationsResponse> {
        try {
            const fileBuffer = await readFile(file.path);
            if (!file || !fileBuffer) {
                throw new HttpException(
                    {
                        message: 'No file uploaded or file is empty',
                    },
                    HttpStatus.BAD_REQUEST,
                );
            }
            const workbook = XLSX.read(fileBuffer, { type: 'buffer' });

            const sheetName = workbook.SheetNames[0];
            const sheetData = XLSX.utils.sheet_to_json(
                workbook.Sheets[sheetName],
            );

            if (!Array.isArray(sheetData) || sheetData.length === 0) {
                throw new HttpException(
                    {
                        message: 'Excel file is empty or invalid',
                    },
                    HttpStatus.BAD_REQUEST,
                );
            }

            let currentPackage: Partial<Package> | null = null;
            const packagesToSave: Partial<Package>[] = [];

            for (const row of sheetData) {
                const packageName = row.PackageName as string;
                const description = row.Description as string;
                const price = row.Price as string;
                const duration = row.Duration as string;
                const diagnosis = row.Diagnosis as string;
                const imagingInvestigation = row.ImagingInvestigation as string;
                const lapInvestigation = row.lapInvestigation as string;
                const surgeryDayEssentials = row.SurgeryDayEssentials as string;
                const surgeryKey = row.Key as string;
                const surgeryValue = row.Value as string;

                // If a new package is encountered
                if (packageName) {
                    if (!price || !duration) {
                        throw new HttpException(
                            {
                                message:
                                    // eslint-disable-next-line no-template-curly-in-string
                                    'Missing required fields for package: ${packageName}',
                            },
                            HttpStatus.BAD_REQUEST,
                        );
                    }

                    // Save the previous package if exists
                    if (currentPackage) {
                        packagesToSave.push(currentPackage);
                    }

                    // Prepare a new package
                    currentPackage = {
                        name: packageName,
                        description: description || '',
                        price: parseFloat(price),
                        duration: parseInt(duration, 10),
                        diagnosis,
                        imagingInvestigation,
                        lapInvestigation,
                        surgeryDayEssentials,
                        details: [],
                    };
                }

                // Validate surgery details
                if (!currentPackage) {
                    throw new HttpException(
                        {
                            message:
                                'Surgery details provided without an associated package',
                        },
                        HttpStatus.BAD_REQUEST,
                    );
                }

                if (surgeryKey && surgeryValue) {
                    const surgeryDetail = {
                        detailKey: surgeryKey,
                        detailValue: surgeryValue,
                        package: currentPackage,
                    } as PackageDetail;
                    currentPackage.details.push(surgeryDetail);
                }
            }

            // Save the last package
            if (currentPackage) {
                packagesToSave.push(currentPackage);
            }

            // Save to database
            const savedPackages = [];
            for (const packageData of packagesToSave) {
                const newPackage = this.packageRepository.create({
                    name: packageData.name,
                    description: packageData.description,
                    price: packageData.price,
                    duration: packageData.duration,
                    diagnosis: packageData.diagnosis,
                    imagingInvestigation: packageData.imagingInvestigation,
                    lapInvestigation: packageData.lapInvestigation,
                    surgeryDayEssentials: packageData.surgeryDayEssentials,
                    serviceProvider,
                    doctor,
                });

                const savedPackage = await this.packageRepository.save(
                    newPackage,
                );

                // Save details
                if (packageData.details && packageData.details.length > 0) {
                    const details = packageData.details.map((detail) =>
                        this.detailRepository.create({
                            detailKey: detail.detailKey,
                            detailValue: detail.detailValue,
                            package: savedPackage,
                        }),
                    );

                    await this.detailRepository.save(details);
                }

                savedPackages.push(savedPackage);
            }

            return {
                message:
                    'Packages and their details have been uploaded successfully.',
                isSuccessful: true,
            };
        } catch (error) {
            throw new HttpException(
                {
                    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                    message: `Error processing Excel file: ${error.message}`,
                },
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    generateTokenForPackageUploader(user: UserEntity): string {
        return this.jwtService.sign(
            {
                id: user.id,
                role: user.role,
            },
            {
                expiresIn: this.configService.JWT_ACCESS_TOKEN_EXPIRES_IN,
                secret: this.configService.JWT_ACCESS_TOKEN_SECRET_KEY,
            },
        );
    }

    async sendEmail(
        url: string,
        userId: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        try {
            const user = await this.userRepository.findOne({
                where: {
                    id: userId,
                },
            });
            if (!user) {
                throw new HttpException(
                    {
                        message: await this.i18n.translate(
                            DoctorMessagesKeys.DOCTOR_NOT_FOUND,
                            {
                                lang,
                            },
                        ),
                    },
                    HttpStatus.BAD_REQUEST,
                );
            }

            const name = user.name;
            // generate token
            const token = this.generateTokenForPackageUploader(user);
            // generate url link
            const link = `${url}/packages/portal?endPoint=/packages/upload/doctors&token=${token}`;
            const emailSource = readFileSync(
                path.join(process.cwd(), '/views/packages-email.hbs'),
                {
                    encoding: 'utf8',
                },
            );
            const template = handlebars.compile(emailSource);

            const html = template({ name, link });

            // send mail with
            await this.emailService.sendEmailWithAttachmentAsBuffer(
                user.email,
                'Kashf247 Packages',
                html,
                [
                    {
                        filename: 'Packages.xlsx',
                        content: this.downloadTemplate().toString('base64'),
                        encoding: 'base64',
                    },
                ],
            );
            return {
                message: 'Email is sent successfully.',
                isSuccessful: true,
            };
        } catch (e) {
            this.logger.error(e.message);
            throw new HttpException(
                {
                    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                    message: `Error While sending email: ${e.message}`,
                },
                HttpStatus.BAD_REQUEST,
            );
        }
    }
}
