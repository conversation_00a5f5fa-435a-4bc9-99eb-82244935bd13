'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsArray,
    IsEmail,
    IsEnum,
    IsISO8601,
    IsOptional,
    IsPhoneNumber,
    IsString,
} from 'class-validator';

import { GenderType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { MembershipDto } from '../../memberships/dto/membership.dto';

export class CreatePatientDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    firebaseUserId?: string;

    @ApiProperty()
    @Expose()
    @IsEmail({}, { message: 'invalid email' })
    email: string;

    @ApiProperty()
    @Expose()
    @IsString()
    password: string;

    @ApiProperty()
    @Expose()
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    phone: string;

    @ApiPropertyOptional({ enum: GenderType })
    @Expose()
    @IsOptional()
    @IsEnum(GenderType)
    gender?: GenderType;

    @IsArray()
    @Expose()
    @IsOptional()
    medications?: number[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    membership?: MembershipDto;

    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    avatar?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    appLanguage?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsISO8601()
    @IsOptional()
    dateOfBirth?: Date;
}
