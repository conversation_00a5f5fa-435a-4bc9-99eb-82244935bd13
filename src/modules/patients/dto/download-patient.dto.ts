

import { GenderType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

import { IsEnum, IsNumber, IsString } from 'class-validator';
import { Expose } from 'class-transformer';

export class DownloadPatientDto extends AbstractDto {
    @IsString()
    @Expose()
    name: string;

    @IsString()
    @Expose()
    mobileNumber: string;

    @IsString()
    @Expose()
    email: string;

    @IsEnum(GenderType)
    @Expose()
    gender: GenderType;

    @IsNumber()
    @Expose()
    age: number;
}
