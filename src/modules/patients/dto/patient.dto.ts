'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsDateString,
    IsOptional,
    IsString,
} from 'class-validator';

import { MaritalStatusType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ConsultationRequestDto } from '../../consultation-requests/dto/consultation-request.dto';
import { MembershipDto } from '../../memberships/dto/membership.dto';
import { UserDto } from '../../users/dto/user.dto';
import { NationalityDto } from '../../nationalities/dto/nationality.dto';

export class PatientDto extends AbstractDto {
    @IsDateString()
    @Expose()
    @IsOptional()
    dateOfBirth?: Date;

    @IsString()
    @Expose()
    @IsOptional()
    bloodGroup?: string;

    @Expose()
    @IsOptional()
    maritalStatus?: MaritalStatusType;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isSmoking?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isActive?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isBanned?: boolean;

    @IsArray()
    @Expose()
    @IsOptional()
    allergies?: string[];

    @IsArray()
    @Expose()
    @IsOptional()
    chronicDiseases?: string[];

    @IsArray()
    @Expose()
    @IsOptional()
    disabilities?: string[];

    @IsArray()
    @Expose()
    @IsOptional()
    medications?: number[];

    @IsArray()
    @Expose()
    @IsOptional()
    injuries?: string[];

    @IsArray()
    @Expose()
    @IsOptional()
    surgeries?: string[];

    @Expose()
    @IsOptional()
    @Type(() => UserDto)
    user?: UserDto;

    @Expose()
    @IsOptional()
    @Type(() => ConsultationRequestDto)
    consultationRequests?: ConsultationRequestDto[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    membership?: MembershipDto;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    nationality?: NationalityDto;
}
