import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { PatientDto } from './patient.dto';

export class PatientsPageDto {
    @ApiProperty({
        type: PatientDto,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => PatientDto)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: PatientDto[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: PatientDto[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
