'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsEnum,
    IsISO8601,
    IsOptional,
    IsString,
} from 'class-validator';

import { MaritalStatusType } from '../../../common/constants/types';
import { MembershipDto } from '../../memberships/dto/membership.dto';
import { UpdateUserDto } from '../../users/dto/update-user.dto';
import { NationalityDto } from '../../nationalities/dto/nationality.dto';

export class UpdatePatientDto extends UpdateUserDto {
    @ApiPropertyOptional({ example: 'YYYY-MM-DD' })
    @Expose()
    @IsOptional()
    @IsISO8601()
    dateOfBirth?: Date;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    bloodGroup?: string;

    @ApiPropertyOptional({ enum: MaritalStatusType })
    @Expose()
    @IsOptional()
    @IsEnum(MaritalStatusType)
    maritalStatus?: MaritalStatusType;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isSmoking?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isBanned?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    allergies?: string[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    chronicDiseases?: string[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    disabilities?: string[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    medications?: number[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    injuries?: string[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    surgeries?: string[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    membership?: MembershipDto;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    nationality?: NationalityDto;
}
