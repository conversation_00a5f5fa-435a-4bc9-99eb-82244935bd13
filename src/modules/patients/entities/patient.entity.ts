'use strict';

import { Expose, Type } from 'class-transformer';
import {
    <PERSON>Array,
    IsEnum,
    IsObject,
    IsOptional,
    IsString,
} from 'class-validator';
import {
    Column,
    <PERSON>tity,
    JoinC<PERSON>umn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    OneToOne,
} from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { MaritalStatusType } from '../../../common/constants/types';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { Medication } from '../../medications/entities/medication.entity';
import { Membership } from '../../memberships/entities/membership.entity';
import { NationalityEntity } from '../../nationalities/entities/nationality.entity';
import { Transaction } from '../../payments/entities/transaction.entity';
import { UserEntity } from '../../users/entities/user.entity';

@Entity('patients')
export class Patient extends AbstractEntity {
    @Column({ type: 'date', nullable: true })
    @Expose()
    @IsOptional()
    dateOfBirth?: Date;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    bloodGroup?: string;

    @Column({ nullable: true, type: 'enum', enum: MaritalStatusType })
    @Expose()
    @IsOptional()
    @IsEnum(MaritalStatusType)
    maritalStatus?: MaritalStatusType;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    isSmoking?: boolean;

    @Column('text', { array: true, default: '{}' })
    @Expose()
    @IsOptional()
    @IsArray()
    allergies?: string[];

    @Column('text', { array: true, default: '{}' })
    @Expose()
    @IsOptional()
    @IsArray()
    chronicDiseases?: string[];

    @Column('text', { array: true, default: '{}' })
    @Expose()
    @IsOptional()
    @IsArray()
    disabilities?: string[];

    @Column('text', { array: true, default: '{}' })
    @Expose()
    @IsOptional()
    @IsArray()
    injuries?: string[];

    @Column('text', { array: true, default: '{}' })
    @Expose()
    @IsOptional()
    @IsArray()
    surgeries?: string[];

    @Column({ default: true })
    @Expose()
    isActive: boolean;

    @Column({ default: false })
    @Expose()
    isBanned: boolean;

    @OneToOne((_type) => UserEntity, (user) => user.patient, {
        cascade: ['insert'],
        onDelete: 'CASCADE',
    })
    @JoinColumn()
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => UserEntity)
    user?: UserEntity;

    @OneToMany(
        (_type) => ConsultationRequestEntity,
        (consultationRequest) => consultationRequest.patient,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => ConsultationRequestEntity)
    consultationRequests?: ConsultationRequestEntity[];

    @OneToMany((_type) => Transaction, (transaction) => transaction.patient, {
        cascade: true,
    })
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => Transaction)
    transactions?: Transaction[];

    @ManyToMany(() => Medication, (medication) => medication.patients, {
        cascade: true,
        onUpdate: 'CASCADE',
    })
    @JoinTable({
        name: 'patient_medications',
    })
    medications?: Medication[];

    @Column()
    @Expose()
    membershipId?: number;

    @OneToOne((_type) => Membership, (membership) => membership.patient, {
        cascade: ['insert'],
        onDelete: 'SET NULL',
    })
    @JoinColumn()
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => Membership)
    membership?: Membership;

    @ManyToOne(
        (_type) => NationalityEntity,
        (nationality) => nationality.patients,
        {
            onDelete: 'SET NULL',
        },
    )
    @JoinColumn()
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => NationalityEntity)
    nationality: NationalityEntity;
}
