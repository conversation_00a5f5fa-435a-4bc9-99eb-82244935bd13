'use strict';

import {
    Body,
    Controller,
    Delete,
    ForbiddenException,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    Api<PERSON>earerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { AbstractResponseDto } from '../../common/dto/abstract-response.dto';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateFavouriteDto } from '../favourites/dto/create-favourite.dto';
import { FavouriteDto } from '../favourites/dto/favourite.dto';
import { CreatePatientRatingDto } from './dto/create-patient-rating.dto';
import { CreatePatientResponseDto } from './dto/create-patient-response.dto';
import { CreatePatientDto } from './dto/create-patient.dto';
import { DownloadPatientDto } from './dto/download-patient.dto';
import { PatientDto } from './dto/patient.dto';
import { PatientsPageDto } from './dto/patients-page.dto';
import { UpdatePatientDto } from './dto/update-patient.dto';
import { PatientsService } from './patients.service';

@Controller('patients')
@ApiTags('patients')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class PatientsController {
    constructor(private patientsService: PatientsService) {}

    /*********************** Favourites *****************************/

    @Get('favourite-doctors')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT)
    @ApiResponse({
        description: 'list favourite Doctors',
        type: [FavouriteDto],
    })
    listFavouriteDoctors(
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<FavouriteDto[]> {
        return this.patientsService.listFavouriteDoctors(req.user.id, lang);
    }

    @Post('favourite-doctors')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT)
    @ApiResponse({
        description: 'Add or remove favourite doctor',
        type: BasicOperationsResponse,
    })
    addOrRemoveFavourite(
        @Req() req: Request,
        @Body() createFavouriteDto: CreateFavouriteDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        createFavouriteDto.userId = req.user.id;
        return this.patientsService.addOrRemoveFavouriteDoctor(
            createFavouriteDto,
            lang,
        );
    }

    /*********************** App Settings *****************************/

    @Get('app-settings')
    getAppSettings(@I18nLang() lang: string): AbstractResponseDto {
        return {
            isSuccessful: true,
            message: `app-settings: ${lang}`,
        };
    }

    /*********************** CRUD Operations *****************************/

    @Post()
    @ApiResponse({
        description: 'Create patient - Patient registration',
        type: CreatePatientResponseDto,
    })
    create(
        @Body() createPatientDto: CreatePatientDto,
        @I18nLang() lang: string,
    ): Promise<CreatePatientResponseDto> {
        return this.patientsService.createPatient(createPatientDto, lang);
    }

    @Get('/download')
    @ApiResponse({
        description: 'Download patients',
        type: [DownloadPatientDto],
    })
    async downloadPatients(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
    ): Promise<void> {
        const data = await this.patientsService.downloadPatients(
            providerId,
            query,
        );
        res.setHeader(
            'Content-disposition',
            'attachment; filename=patients.csv',
        );
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get patient',
        type: PatientDto,
    })
    getPatient(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<PatientDto> {
        if (req.user.role === RoleType.PATIENT && id !== req.user.patient.id) {
            throw new ForbiddenException();
        }
        switch (req.user.role) {
            case RoleType.ADMIN:
            case RoleType.SUPER_ADMIN:
            case RoleType.SERVICE_PROVIDER:
                return this.patientsService.getPatientForAdmin(id, lang);
            default:
                return this.patientsService.getPatientProfile(id, lang);
        }
    }

    @Get()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN, RoleType.SERVICE_PROVIDER)
    @ApiResponse({
        description: 'Get all patients',
        type: [PatientDto],
    })
    getAllPatients(
        @Req() req: Request,
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
    ): Promise<PatientsPageDto> {
        return this.patientsService.getAllPatients(
            req.user,
            query,
            pageOptionsDto,
        );
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update patient',
        type: BasicOperationsResponse,
    })
    update(
        @Req() req: Request,
        @Param('id') id: number,
        @Body() updatePatientDto: UpdatePatientDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        if (req.user.role === RoleType.PATIENT && id !== req.user.patient.id) {
            throw new ForbiddenException();
        }

        return this.patientsService.updatePatient(id, updatePatientDto, lang);
    }

    @Delete(':id')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'delete patient make patient isActive false',
        type: BasicOperationsResponse,
    })
    delete(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        if (req.user.role === RoleType.PATIENT && id !== req.user.patient.id) {
            throw new ForbiddenException();
        }
        return this.patientsService.deactivatePatient(id, lang);
    }

    @Delete(':id/delete')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT, RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'delete patient make patient isActive false',
        type: BasicOperationsResponse,
    })
    deletePatient(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        if (req.user.role === RoleType.PATIENT && id !== req.user.patient.id) {
            throw new ForbiddenException();
        }
        return this.patientsService.deletePatient(id, lang);
    }

    /*********************** Rating *****************************/

    @Post('ratings')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.DOCTOR)
    addPatientRating(
        @Req() req: Request,
        @Body() createPatientRatingDto: CreatePatientRatingDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.patientsService.addPatientRatings(
            createPatientRatingDto,
            req.user.id,
            lang,
        );
    }
}
