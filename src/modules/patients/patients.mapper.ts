import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';
import { get } from 'lodash';
import { GenderType } from '../../common/constants/types';
import * as moment from 'moment';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { Medication } from '../medications/entities/medication.entity';
import { CreatePatientDto } from './dto/create-patient.dto';
import { DownloadPatientDto } from './dto/download-patient.dto';
import { PatientDto } from './dto/patient.dto';
import { UpdatePatientDto } from './dto/update-patient.dto';
import { Patient } from './entities/patient.entity';

type PatientDTOs = CreatePatientDto | UpdatePatientDto | PatientDto;

@Injectable()
export class PatientsMapper extends AbstractMapper<PatientDTOs, Patient> {
    constructor(public readonly helperService: HelperService) {
        super();
    }

    fromDTOToEntity(
        destination: ClassType<Patient>,
        sourceObject: PatientDTOs,
    ): Patient {
        const patientEntity = super.fromDTOToEntity(destination, sourceObject);
        this.helperService.removeEmptyKeys(patientEntity);
        if (sourceObject.medications) {
            patientEntity.medications = sourceObject.medications.map((item) => {
                const medication = new Medication();
                medication.id = item;
                return medication;
            });
        }
        return patientEntity;
    }

    fromEntityToDTO(
        destination: ClassType<PatientDto>,
        sourceObject: Patient,
    ): PatientDto {
        const patientDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(patientDto);
        return patientDto;
    }

    fromEntityToDownloadData(
        destination: ClassType<DownloadPatientDto>,
        sourceObject: Patient,
    ): DownloadPatientDto {
        return {
            id: sourceObject.id,
            name: get(sourceObject, 'user.name', ''),
            mobileNumber: get(sourceObject, 'user.phone', ''),
            email: get(sourceObject, 'user.email', ''),
            gender: get(sourceObject, 'user.gender', GenderType.FEMALE) || GenderType.FEMALE,
            age: moment().diff(
                moment(sourceObject.dateOfBirth, 'YYYY-MM-DD'),
                'years',
            ),
        };
    }
}
