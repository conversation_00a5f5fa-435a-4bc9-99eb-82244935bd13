import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { AuthModule } from '../auth/auth.module';
import { FavouriteModule } from '../favourites/favourites.module';
import { MedicationsMapper } from '../medications/medications.mapper';
import { MedicationsRepository } from '../medications/repositories/medications.repository';
import { RatingsModule } from '../ratings/ratings.module';
import { UsersModule } from '../users/users.module';
import { PatientsController } from './patients.controller';
import { PatientsMapper } from './patients.mapper';
import { PatientsService } from './patients.service';
import { PatientsRepository } from './repositories/patients.repository';

@Module({
    imports: [
        UsersModule,
        RatingsModule,
        AuthModule,
        FavouriteModule,
        SharedModule,
    ],
    controllers: [PatientsController],
    exports: [PatientsService],
    providers: [
        PatientsService,
        PatientsMapper,
        MedicationsMapper,
        MedicationsRepository,
        PatientsRepository,
    ],
})
export class PatientsModule {}
