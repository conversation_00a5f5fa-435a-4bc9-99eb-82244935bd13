import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
} from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';

import { RoleType, SortByType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { DataDownloadService } from '../../shared/services/data-download.service';
import { DebugLogger } from '../../shared/services/logger.service';
import { AuthService } from '../auth/auth.service';
import { TokenPayloadDto } from '../auth/dto/token-payload.dto';
import { AuthMessagesKeys } from '../auth/translate.enum';
import { CreateFavouriteDto } from '../favourites/dto/create-favourite.dto';
import { FavouriteDto } from '../favourites/dto/favourite.dto';
import { FavouriteService } from '../favourites/favourites.service';
import { RatingsService } from '../ratings/ratings.service';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { UserEntity } from '../users/entities/user.entity';
import { UsersMapper } from '../users/users.mapper';
import { UsersService } from '../users/users.service';
import { CreatePatientRatingDto } from './dto/create-patient-rating.dto';
import { CreatePatientResponseDto } from './dto/create-patient-response.dto';
import { CreatePatientDto } from './dto/create-patient.dto';
import { DownloadPatientDto } from './dto/download-patient.dto';
import { PatientDto } from './dto/patient.dto';
import { PatientsPageDto } from './dto/patients-page.dto';
import { UpdatePatientDto } from './dto/update-patient.dto';
import { Patient } from './entities/patient.entity';
import { PatientsMapper } from './patients.mapper';
import { PatientsRepository } from './repositories/patients.repository';
import { PatientMessagesKeys } from './translate.enum';

@Injectable()
export class PatientsService {
    constructor(
        private readonly i18n: I18nService,
        private readonly patientsRepository: PatientsRepository,
        private readonly patientsMapper: PatientsMapper,
        private readonly usersMapper: UsersMapper,
        private readonly usersService: UsersService,
        private readonly ratingsService: RatingsService,
        private readonly authService: AuthService,
        private readonly favouriteService: FavouriteService,
        private readonly dataDownloadService: DataDownloadService,
        private logger: DebugLogger,
    ) {}

    async createPatient(
        createPatientDto: CreatePatientDto,
        lang: string,
    ): Promise<CreatePatientResponseDto> {
        await this.usersService.checkUserExistence(
            createPatientDto,
            lang,
            RoleType.PATIENT,
        );

        const userEntity = this.usersMapper.fromDTOToEntity(
            UserEntity,
            createPatientDto,
        );

        const patientEntity = this.patientsMapper.fromDTOToEntity(
            Patient,
            createPatientDto,
        );

        userEntity.role = RoleType.PATIENT;

        patientEntity.user = userEntity;
        const patient = this.patientsRepository.create(patientEntity);
        const createdPatient = await this.patientsRepository.save(patient);

        let tokenPayload = new TokenPayloadDto();
        tokenPayload = this.authService.generateTokens(
            createdPatient.user.id,
            createdPatient.user.role,
        );

        const header = await this.i18n.translate(
            PatientMessagesKeys.EMAIL_PATIENT_REGISTERED_HEADER,
            { lang },
        );
        const body = await this.i18n.translate(
            PatientMessagesKeys.EMAIL_PATIENT_REGISTERED_MESSAGE,
            { lang },
        );

        await Promise.all([
            this.usersService.updateRefreshToken(
                createdPatient.user.id,
                tokenPayload.refreshToken,
            ),
            this.usersService.confirmEmail(createdPatient.user.id, lang),
            this.usersService.sendEmailToAdminOnPatientRegistration(lang),
            this.usersService.sendRegistrationEmail(
                createdPatient.user.id,
                header,
                body,
                lang,
            ),
        ]);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                PatientMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
            patientId: createdPatient.id,
            userId: createdPatient.user.id,
            token: tokenPayload,
        };
    }

    async findPatient(patientId: number): Promise<Patient> {
        return this.patientsRepository.findOne({
            where: {
                id: patientId,
            },
        });
    }

    async addOrRemoveFavouriteDoctor(
        favouriteDto: CreateFavouriteDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.favouriteService.addOrRemoveFavourite(favouriteDto, lang);
    }

    async listFavouriteDoctors(
        userId: number,
        lang: string,
    ): Promise<FavouriteDto[]> {
        return this.favouriteService.getUserFavourite(userId, lang);
    }

    async addPatientRatings(
        createPatientRatingDto: CreatePatientRatingDto,
        userId: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const query = this.patientsRepository
            .createQueryBuilder('patient')
            .leftJoinAndSelect('patient.user', 'user')
            .select(['patient.id', 'user.id', 'user.name'])
            .where('patient.id = :id', {
                id: createPatientRatingDto.patientId,
            });

        const patient = await query.getOne();
        if (!patient) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        return this.ratingsService.createRating(
            {
                ratedUserId: patient.user.id,
                raterUserId: userId,
                requestId: createPatientRatingDto.requestId,
                rating: createPatientRatingDto.rating,
                comment: createPatientRatingDto.comment,
                callRating: createPatientRatingDto.callRating,
                callComment: createPatientRatingDto.callComment,
            },
            lang,
        );
    }

    async getPatientProfile(id: number, lang: string): Promise<PatientDto> {
        const query = this.patientsRepository
            .createQueryBuilder('patient')
            .leftJoinAndSelect('patient.user', 'user')
            .leftJoinAndSelect('patient.medications', 'medication')
            .leftJoinAndSelect(
                'medication.translations',
                'medicationTranslations',
                'medicationTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('patient.membership', 'membership')
            .leftJoinAndSelect(
                'membership.translations',
                'membershipTranslations',
                'membershipTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('patient.nationality', 'nationality')
            .leftJoinAndSelect(
                'nationality.translations',
                'nationalityTranslations',
                'nationalityTranslations.languageCode = :lang',
                { lang },
            )
            .select([
                'patient.id',
                'patient.isActive',
                'patient.dateOfBirth',
                'patient.bloodGroup',
                'patient.maritalStatus',
                'patient.isSmoking',
                'patient.allergies',
                'patient.chronicDiseases',
                'patient.disabilities',
                'patient.injuries',
                'patient.surgeries',
                'user.id',
                'user.name',
                'user.phone',
                'user.email',
                'user.gender',
                'user.avatar',
                'user.isEmailVerified',
                'membership',
                'membershipTranslations',
                'nationality',
                'nationalityTranslations',
            ])
            .where('patient.id = :id', {
                id,
            });

        const patient = await query.getOne();
        if (!patient) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.patientsMapper.fromEntityToDTO(PatientDto, patient);
    }

    async getPatientForAdmin(id: number, lang: string): Promise<PatientDto> {
        const query = this.patientsRepository
            .createQueryBuilder('patient')
            .leftJoinAndSelect('patient.user', 'user')
            .leftJoinAndSelect(
                'patient.consultationRequests',
                'consultationRequests',
            )
            .leftJoinAndSelect('patient.membership', 'membership')
            .leftJoinAndSelect(
                'membership.translations',
                'membershipTranslations',
                'membershipTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('patient.nationality', 'nationality')
            .leftJoinAndSelect(
                'nationality.translations',
                'nationalityTranslations',
                'nationalityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('consultationRequests.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .select([
                'patient.id',
                'patient.isActive',
                'patient.isBanned',
                'patient.dateOfBirth',
                'user.id',
                'user.name',
                'user.phone',
                'user.email',
                'user.gender',
                'user.avatar',
                'user.isEmailVerified',
                'consultationRequests.id',
                'consultationRequests.date',
                'consultationRequests.from',
                'consultationRequests.type',
                'consultationRequests.patientNotes',
                'doctor.id',
                'doctorUser.id',
                'doctorUser.name',
                'membership',
                'membershipTranslations',
                'nationality',
                'nationalityTranslations',
            ])
            .where('patient.id = :id', {
                id,
            });

        const patient = await query.getOne();
        if (!patient) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        this.logger.log(JSON.stringify(patient));
        return this.patientsMapper.fromEntityToDTO(PatientDto, patient);
    }

    async getAllPatients(
        user: UserDetailsDto,
        httpQueryString: string,
        pageOptionsDto: PageOptionsDto,
    ): Promise<PatientsPageDto> {
        let query = this.patientsRepository
            .createQueryBuilder('patient')
            .leftJoin('patient.user', 'user')
            .select([
                'patient.id',
                'patient.isActive',
                'patient.dateOfBirth',
                'patient.isBanned',
                'user.id',
                'user.name',
                'user.phone',
                'user.email',
                'user.gender',
                'user.avatar',
            ]);

        if (user.role === RoleType.SERVICE_PROVIDER) {
            query
                .leftJoin('patient.consultationRequests', 'consultationRequest')
                .leftJoin(
                    'consultationRequest.serviceProvider',
                    'serviceProvider',
                )
                .where('serviceProvider.id = :providerId', {
                    providerId: user.serviceProvider.id,
                });
        }

        if (httpQueryString) {
            query = this.buildGetPatientsQuery(query, httpQueryString);
        }
        const [patients, pageMetaDto] = await query.paginate(pageOptionsDto);

        const mappedPatients = patients.map((patient) =>
            this.patientsMapper.fromEntityToDTO(PatientDto, patient),
        );

        return new PatientsPageDto(mappedPatients, pageMetaDto);
    }

    async downloadPatients(
        providerId: string,
        httpQueryString: string,
    ): Promise<string> {
        let query = this.patientsRepository
            .createQueryBuilder('patient')
            .leftJoin('patient.user', 'user')
            .select([
                'patient.id',
                'patient.dateOfBirth',
                'user.id',
                'user.name',
                'user.phone',
                'user.email',
                'user.gender',
                'user.avatar',
            ]);

        if (providerId) {
            query
                .leftJoin('patient.consultationRequests', 'consultationRequest')
                .leftJoin(
                    'consultationRequest.serviceProvider',
                    'serviceProvider',
                )
                .where('serviceProvider.id = :providerId', {
                    providerId: parseInt(providerId, 10),
                });
        }

        if (httpQueryString) {
            query = this.buildGetPatientsQuery(query, httpQueryString);
        }
        const patients = await query.getMany();

        const mappedPatients = patients.map((patient) =>
            this.patientsMapper.fromEntityToDownloadData(
                DownloadPatientDto,
                patient,
            ),
        );
        const fields = ['id', 'name', 'mobileNumber', 'email', 'gender', 'age'];
        return this.dataDownloadService.downloadCsv(fields, mappedPatients);
    }

    async updatePatient(
        id: number,
        updatePatientDto: UpdatePatientDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const query = this.patientsRepository
            .createQueryBuilder('patient')
            .leftJoinAndSelect('patient.user', 'user')
            .select(['patient.id', 'user.id'])
            .where('patient.id = :id', {
                id,
            });

        const patient = await query.getOne();
        if (!patient) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const userEntity = this.usersMapper.fromDTOToEntity(
            UserEntity,
            updatePatientDto,
        );

        const patientEntity = this.patientsMapper.fromDTOToEntity(Patient, {
            ...updatePatientDto,
            id,
        });

        if (updatePatientDto.membership) {
            patientEntity.membership = updatePatientDto.membership.id
                ? updatePatientDto.membership
                : null;
        }

        if (updatePatientDto.nationality) {
            patientEntity.nationality = updatePatientDto.nationality.id
                ? updatePatientDto.nationality
                : null;
        }

        if (userEntity.email) {
            await this.usersService.updateEmailVerified(patient.user.id, false);
        }

        delete userEntity.id;

        await Promise.all([
            this.patientsRepository.save(patientEntity),
            this.usersService.updateUser(patient.user.id, userEntity, lang),
        ]);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                PatientMessagesKeys.UPDATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async deactivatePatient(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        await this.patientsRepository.update({ id }, { isActive: false });
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                PatientMessagesKeys.DELETED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async deletePatient(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const query = this.patientsRepository
            .createQueryBuilder('patient')
            .leftJoinAndSelect('patient.user', 'user')
            .select(['patient.id', 'user.id'])
            .where('patient.id = :id', {
                id,
            });

        const patient = await query.getOne();

        await this.usersService.deletePatient(patient.user.id);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                PatientMessagesKeys.DELETED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    buildGetPatientsQuery(
        query: SelectQueryBuilder<Patient>,
        httpQueryString: string,
    ): SelectQueryBuilder<Patient> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }

        if (httpQueryObject.search) {
            query.andWhere(
                '(patient.id || user.name || user.phone || user.email || user.gender) ILIKE :searchKey',
                {
                    searchKey: `%${httpQueryObject.search.value.trim()}%`,
                },
            );
        }

        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.NAME:
                    query.orderBy('user.name', httpQueryObject.sort.type);
            }
        }

        return query;
    }
}
