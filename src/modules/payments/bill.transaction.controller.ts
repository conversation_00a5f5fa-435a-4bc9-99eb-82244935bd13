'use strict';

import {
    Body,
    Controller,
    Post,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateBillTransactionDto } from './dto/create-bill-transaction.dto';
import { BillsService } from './services/bills.service';

@Controller('bill-transaction')
@ApiTags('bill-transaction')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class BillTransactionsController {
    constructor(private billsService: BillsService) {}
    /*********************** CRUD Operations *****************************/
    @Post()
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Create Bill Transaction to settle a payment',
        type: [CreateBillTransactionDto],
    })
    createBillTransaction(
        @Body() createBillTransactionDto: CreateBillTransactionDto,
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.billsService.createBillTransaction(
            createBillTransactionDto,
            req.user,
            lang,
        );
    }
}
