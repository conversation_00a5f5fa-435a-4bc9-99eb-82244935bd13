'use strict';

import {
    Controller,
    ForbiddenException,
    Get,
    Param,
    Query,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang, I18nService } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { BillsPageDto } from './dto/bills-page.dto';
import { PaymentDto } from './dto/payment.dto';
import { Bill } from './entities/bill.entity';
import { BillsService } from './services/bills.service';
import { BillDto } from './dto/bill.dto';

@Controller('bills')
@ApiTags('bills')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class BillsController {
    constructor(
        private billsService: BillsService,
        private readonly i18n: I18nService,
    ) {}

    /*********************** CRUD Operations *****************************/

    @Get('/download')
    @ApiResponse({
        description: 'Download bills',
        type: [String],
    })
    async downloadBills(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
        @I18nLang() lang: string,
    ): Promise<void> {
        const data = await this.billsService.downloadBills(
            providerId,
            query,
            req.user,
            lang,
            false,
        );
        res.setHeader('Content-disposition', 'attachment; filename=bills.csv');
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get('/admin/download')
    @ApiResponse({
        description: 'Download bills',
        type: [String],
    })
    async downloadBillsForAdmin(
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
        @I18nLang() lang: string,
    ): Promise<void> {
        const data = await this.billsService.downloadBillsForAdmin(
            providerId,
            query,
            lang,
            false,
        );
        res.setHeader('Content-disposition', 'attachment; filename=bills.csv');
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get(':id/download')
    @ApiResponse({
        description: 'Download bill as pdf',
        // type: [DownloadConsultationRequestDto],
    })
    async downloadBillPDF(
        @Req() req: Request,
        @Res() res: Response,
        @Param('id') id: number,
    ): Promise<BasicOperationsResponse> {
        // const data = await this.billsService.downloadBill(id);
        // res.setHeader('Content-Disposition', 'attachment;filename=test.pdf');
        // res.setHeader('Content-Type', 'application/pdf');
        // res.send(Buffer.from(data));
        await this.billsService.sendBillByMail(id);

        return {
            isSuccessful: true,
            message: 'message is so successful',
        };
    }

    @Get()
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get Bills',
        type: [PaymentDto],
    })
    getBills(
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<BillsPageDto> {
        return this.billsService.getBills(
            query,
            pageOptionsDto,
            req.user,
            lang,
            false,
        );
    }

    @Get('/donations/download')
    @ApiResponse({
        description: 'Download bills',
        type: [String],
    })
    async downloadDonationBills(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
        @I18nLang() lang: string,
    ): Promise<void> {
        const data = await this.billsService.downloadBills(
            providerId,
            query,
            req.user,
            lang,
            true,
        );
        res.setHeader('Content-disposition', 'attachment; filename=bills.csv');
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get('/admin/donations/download')
    @ApiResponse({
        description: 'Download bills',
        type: [String],
    })
    async downloadDonationBillsForAdmin(
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
        @I18nLang() lang: string,
    ): Promise<void> {
        const data = await this.billsService.downloadBillsForAdmin(
            providerId,
            query,
            lang,
            true,
        );
        res.setHeader('Content-disposition', 'attachment; filename=bills.csv');
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get('/donations')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get Bills',
        type: [PaymentDto],
    })
    getDonationBills(
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<BillsPageDto> {
        return this.billsService.getBills(
            query,
            pageOptionsDto,
            req.user,
            lang,
            true,
        );
    }

    @Get('/doctors/:id')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get Doctor Wallet Balance',
        type: [PaymentDto],
    })
    getDoctorsWalletBalance(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BillDto[]> {
        if (req.user.role !== RoleType.DOCTOR || id !== req.user.doctor.id) {
            throw new ForbiddenException();
        }
        return this.billsService.getDoctorsWalletBalance(id, lang);
    }
}
