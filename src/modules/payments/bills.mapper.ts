import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { BillDto } from './dto/bill.dto';
import { Bill } from './entities/bill.entity';

@Injectable()
export class BillsMapper extends AbstractMapper<any, any> {
    fromEntityToDTO(
        destination: ClassType<BillDto>,
        sourceObject: Bill,
    ): BillDto {
        const billDto: BillDto = super.fromEntityToDTO(
            destination,
            sourceObject,
        );
        if (
            billDto.serviceProvider &&
            billDto.serviceProvider.serviceProviderType &&
            billDto.serviceProvider.serviceProviderType.translations[0].title
        ) {
            billDto.serviceProvider.serviceProviderType.title =
                billDto.serviceProvider.serviceProviderType.translations[0].title;
            delete billDto.serviceProvider.serviceProviderType.translations;
        }
        return billDto;
    }
}
