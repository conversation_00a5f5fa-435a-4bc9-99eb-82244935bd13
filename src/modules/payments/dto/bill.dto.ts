'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsBoolean,
    IsDate,
    IsDateString,
    IsEnum,
    IsNumber,
    IsOptional,
    ValidateNested,
} from 'class-validator';

import { BillStatus } from '../../../common/constants/status';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { DoctorDto } from '../../doctors/dto/doctor.dto';
import { ServiceProviderDto } from '../../service-providers/dto/service-provider.dto';

export class BillDto extends AbstractDto {
    @IsDateString()
    @ApiProperty()
    @Expose()
    start: Date;

    @IsDateString()
    @ApiProperty()
    @Expose()
    end: Date;

    @IsNumber()
    @ApiProperty()
    @Expose()
    onlineConsulationRevenue: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    homeVisitRevenue: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    clinicVisitRevenue: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    grossRevenue: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    netRevenue: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    kashfPercentage: number;

    @Expose()
    @ApiProperty()
    @IsBoolean()
    isDonation?: boolean;

    @IsEnum(BillStatus)
    @ApiProperty()
    @Expose()
    status: BillStatus;

    @ValidateNested()
    @Type(() => DoctorDto)
    @IsOptional()
    @Expose()
    doctor?: DoctorDto;

    @ValidateNested()
    @Type(() => ServiceProviderDto)
    @IsOptional()
    @Expose()
    serviceProvider?: ServiceProviderDto;

    @IsDate()
    @ApiProperty()
    @Expose()
    createdAt: Date;
}
