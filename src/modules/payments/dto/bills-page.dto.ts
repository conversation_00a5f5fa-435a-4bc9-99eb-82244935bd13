import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { BillDto } from './bill.dto';

export class BillsPageDto {
    @ApiProperty({
        type: BillDto,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => BillDto)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: BillDto[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: BillDto[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
