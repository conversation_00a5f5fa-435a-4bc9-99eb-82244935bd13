'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreateBillTransactionDto extends AbstractDto {
    @IsString()
    @ApiProperty()
    @Expose()
    date: string;

    @IsNumber()
    @ApiProperty()
    @Expose()
    @IsOptional()
    billId: number;

    @IsString()
    @ApiProperty()
    @Expose()
    transactionId: string;

    @IsString()
    @ApiProperty()
    @Expose()
    receipt: string;

    @IsString()
    @ApiPropertyOptional()
    @IsOptional()
    @Expose()
    bankName?: string;
}
