'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';

import { TransactionKind } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreateDonationUrlDto extends AbstractDto {
    @IsNumber()
    @ApiProperty()
    @Expose()
    serviceProviderId: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    amount: number;

    @IsNumber()
    @IsOptional()
    @ApiProperty()
    @Expose()
    userId?: number;

    @IsEnum(TransactionKind)
    @ApiProperty()
    @IsOptional()
    @Expose()
    type?: TransactionKind = TransactionKind.DONATION_FEE;

    @ApiProperty()
    @IsOptional()
    @Expose()
    name?: string;

    @ApiProperty()
    @IsOptional()
    @Expose()
    email?: string;

    @ApiProperty()
    @IsOptional()
    @Expose()
    phone?: string;
}
