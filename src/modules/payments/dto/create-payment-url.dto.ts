'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';

import { TransactionKind } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreatePaymentUrlDto extends AbstractDto {
    @IsNumber()
    @ApiProperty()
    @Expose()
    requestId: number;

    @IsEnum(TransactionKind)
    @ApiProperty()
    @IsOptional()
    @Expose()
    type?: TransactionKind = TransactionKind.REQUEST_FEE;
}
