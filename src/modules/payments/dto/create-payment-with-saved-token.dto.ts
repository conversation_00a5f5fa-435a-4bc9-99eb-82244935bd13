'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { TransactionKind } from '../../../common/constants/types';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreatePaymentWithSavedTokenDto extends AbstractDto {
    @IsNumber()
    @ApiProperty()
    @Expose()
    requestId: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    cardId: number;

    @IsEnum(TransactionKind)
    @ApiProperty()
    @IsOptional()
    @Expose()
    type?: TransactionKind = TransactionKind.REQUEST_FEE;
}
