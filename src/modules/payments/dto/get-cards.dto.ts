'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class GetCardsDto extends AbstractDto {
    @IsString()
    @ApiProperty()
    @Expose()
    cardNumber: string;

    @IsString()
    @ApiProperty()
    @Expose()
    cardType: string;
}
