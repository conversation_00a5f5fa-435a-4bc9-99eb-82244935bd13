'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsDateString,
    IsEnum,
    IsNumber,
    IsOptional,
    ValidateNested,
} from 'class-validator';

import { PaymentStatus } from '../../../common/constants/status';
import { PaymentMethod } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ConsultationRequestDto } from '../../consultation-requests/dto/consultation-request.dto';
import { DoctorDto } from '../../doctors/dto/doctor.dto';
import { PatientDto } from '../../patients/dto/patient.dto';
import { ServiceProviderDto } from '../../service-providers/dto/service-provider.dto';
import { UserDetailsDto } from '../../users/dto/userDetails.dto';

export class PaymentDto extends AbstractDto {
    consultationType: string;
    date: string;
    time: string;
    doctorName: string;
    requestId: number;
    doctorId: string;
    patientName: string;

    @IsDateString()
    @IsOptional()
    @Expose()
    createdAt: Date;

    @IsNumber()
    @ApiProperty()
    @Expose()
    subTotal: string;

    @IsNumber()
    @ApiProperty()
    @Expose()
    amount: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    kashfPercentage: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    refundedAmount: number;

    @IsEnum(PaymentMethod)
    @IsOptional()
    @Expose()
    type?: PaymentMethod;

    @IsEnum(PaymentStatus)
    @IsOptional()
    @Expose()
    status?: PaymentStatus;

    @ValidateNested()
    @Type(() => DoctorDto)
    @IsOptional()
    @Expose()
    doctor?: DoctorDto;

    @ValidateNested()
    @Type(() => ServiceProviderDto)
    @IsOptional()
    @Expose()
    serviceProvider?: ServiceProviderDto;

    @ValidateNested()
    @Type(() => PatientDto)
    @IsOptional()
    @Expose()
    patient?: PatientDto;

    @ValidateNested()
    @Type(() => ConsultationRequestDto)
    @IsOptional()
    @Expose()
    request?: ConsultationRequestDto;

    @ValidateNested()
    @Type(() => UserDetailsDto)
    @IsOptional()
    @Expose()
    user?: UserDetailsDto;
}
