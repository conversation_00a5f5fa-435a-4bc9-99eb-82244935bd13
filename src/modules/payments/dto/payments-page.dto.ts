import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { PaymentDto } from './payment.dto';

export class PaymentsPageDto {
    @ApiProperty({
        type: PaymentDto,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => PaymentDto)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: PaymentDto[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: PaymentDto[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
