import { Expose } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';
import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { BillStatus } from '../../../common/constants/status';
import { ColumnNumericTransformer } from '../../../shared/services/column.numeric.transform';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { BillTransaction } from './bill.transaction.entity';
import { Transaction } from './transaction.entity';

@Entity('bills')
export class Bill extends AbstractEntity {
    @Column({
        type: 'enum',
        enum: BillStatus,
        default: BillStatus.NOT_SETTLED,
    })
    @Expose()
    status: BillStatus;

    @Column({ type: 'date' })
    @Expose()
    start: Date;

    @Column({ type: 'date' })
    @Expose()
    end: Date;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    kashfPercentage: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    onlineConsulationRevenue: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    homeVisitRevenue: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    clinicVisitRevenue: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    grossRevenue: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    totalCash: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    totalCredit: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    total: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    netRevenue: number;

    @Column({ default: false })
    @Expose()
    @IsOptional()
    @IsBoolean()
    isDonation?: boolean;

    @ManyToOne((_type) => DoctorEntity, (doctor) => doctor.transactions, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    doctor?: DoctorEntity;

    @ManyToOne(
        (_type) => ServiceProvider,
        (serviceProvider) => serviceProvider.transactions,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    serviceProvider: ServiceProvider;

    @OneToMany((_type) => Transaction, (transaction) => transaction.bill, {
        cascade: true,
    })
    transactions: Transaction[];

    @OneToMany(
        (_type) => BillTransaction,
        (billTransaction) => billTransaction.bill,
        {
            cascade: true,
        },
    )
    billTransaction: BillTransaction[];
}
