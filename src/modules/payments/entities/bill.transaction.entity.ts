import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Bill } from './bill.entity';

@Entity('bill_transactions')
export class BillTransaction extends AbstractEntity {
    @Column()
    @Expose()
    transactionId: string;

    @Column({ nullable: true })
    @Expose()
    bankName: string;

    @Column({ type: 'date' })
    @Expose()
    date: Date;

    @Column()
    @Expose()
    receipt: string;

    @ManyToOne((_type) => Bill, (bill) => bill.billTransaction, {
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    })
    bill: Bill;
}
