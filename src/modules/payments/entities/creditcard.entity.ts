import { Expose, Type } from 'class-transformer';
import { IsString } from 'class-validator';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserDto } from '../../users/dto/user.dto';
import { UserEntity } from '../../users/entities/user.entity';

@Entity('creditcards')
export class CreditCard extends AbstractEntity {
    @Column()
    @IsString()
    @Expose()
    cardNumber?: string;

    @Column()
    @IsString()
    token?: string;

    @Column()
    @IsString()
    cardType?: string;

    @ManyToOne((_type) => UserEntity, (user) => user.creditcards, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    @Expose()
    @Type(() => UserDto)
    user?: UserEntity;
}
