import { Expose } from 'class-transformer';
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Transaction } from './transaction.entity';

@Entity('paymob_transactions')
export class PaymobTransactions extends AbstractEntity {
    @Column()
    @Expose()
    status: string;

    @Column()
    @Expose()
    paymobOrderId: string;

    @Column({ nullable: true })
    @Expose()
    paymobTransactionId: number;

    @Column({ type: 'json', nullable: true })
    @Expose()
    callbackData: any;

    @ManyToOne(
        () => Transaction,
        (transaction) => transaction.paymobTransactions,
        {
            cascade: ['insert'],
            onDelete: 'SET NULL',
        },
    )
    @JoinColumn()
    @Expose()
    transaction: Transaction;
}
