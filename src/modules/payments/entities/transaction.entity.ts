'use strict';

import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import {
    PaymentMethod,
    TransactionKind,
} from '../../../common/constants/types';
import { ColumnNumericTransformer } from '../../../shared/services/column.numeric.transform';
import { Booking } from '../../booking/entities/booking.entity';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { Bill } from './bill.entity';
import { PaymobTransactions } from './paymob-transactions.entity';

export type Requestable = ConsultationRequestEntity | Booking | null;

@Entity('transactions')
export class Transaction extends AbstractEntity {
    @Column()
    public status: string;

    @Column({ nullable: true, type: 'enum', enum: PaymentMethod })
    public type: PaymentMethod;

    @Column({ nullable: false, type: 'enum', enum: TransactionKind })
    public kind: TransactionKind;

    @Column({ nullable: true }) // ID of the related Booking/ConsultationRequest etc.
    @Expose()
    requestId: number

    @Column({
        type: 'decimal',
        nullable: true,
        precision: 16,
        scale: 3,
    })
    @Expose()
    amount: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    kashfPercentage: number;

    @Column({
        type: 'decimal',
        nullable: true,
        precision: 16,
        scale: 3,
    })
    @Expose()
    refundedAmount: number;

    @OneToMany(
      () => PaymobTransactions,
      (paymobTransactions) => paymobTransactions.transaction,
      {
          cascade: true,
          onDelete: 'SET NULL',
      },
    )
    public paymobTransactions: PaymobTransactions[];

    @ManyToOne((_type) => DoctorEntity, (doctor) => doctor.transactions, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    doctor: DoctorEntity;

    @ManyToOne((_type) => Patient, (patient) => patient.transactions, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    patient: Patient;

    @ManyToOne(
      (_type) => ServiceProvider,
      (serviceProvider) => serviceProvider.transactions,
      {
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
      },
    )
    serviceProvider: ServiceProvider;

    @ManyToOne((_type) => Bill, (bill) => bill.transactions, {
        onUpdate: 'CASCADE',
    })
    bill: Bill;

    @ManyToOne((_type) => UserEntity, (user) => user.transactions, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    user: UserEntity;

    // @OneToOne(
    //     () => ConsultationRequestEntity,
    //     (consultationRequestEntity) => consultationRequestEntity.transaction,
    //     {
    //         cascade: ['insert'],
    //         onDelete: 'SET NULL',
    //     },
    // )
    // @JoinColumn()
    // @Expose()
    // request: ConsultationRequestEntity;

    @Expose()
    request: Requestable;
}
