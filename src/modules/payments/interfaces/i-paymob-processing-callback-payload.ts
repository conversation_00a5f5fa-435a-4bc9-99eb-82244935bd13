import { CallBackType } from '../../../common/constants/types';
import { TransactionStatus } from '../payment.enum';

export interface IPaymobProcessingCallbackPayload {
    hmac?: string;
    obj: {
        id: number;
        order_id: string;
        masked_pan: string;
        token: string;
        card_subtype: string;
        amount_cents: number;
        pending: boolean;
        success: boolean;
        is_refunded: boolean;
        integration_id: number;
        profile_id?: number;
        status?: TransactionStatus;
        data: {
            txn_response_code: string;
        };
        order: {
            id: string;
        };
    };
    type: CallBackType;
    error_occured: boolean;
    data: {
        json: {
            error: {
                explanation: string;
            };
        };
    };
}
