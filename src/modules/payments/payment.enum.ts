export enum PaymentMessagesKeys {
    PAYMENTS_NOT_FOUND = 'payment.PAYMENTS_NOT_FOUND',
    PAYMENT_NOT_FOUND = 'payment.PAYMENT_NOT_FOUND',
    CREATED_SUCCESSFULLY = 'payment.CREATED_SUCCESSFULLY',
    UPDATED_SUCCESSFULY = 'payment.UPDATED_SUCCESSFULY',
    DELETED_SUCCESSFULY = 'payment.DELETED_SUCCESSFULY',
    REQUEST_NOT_ACCEPTED = 'payment.REQUEST_NOT_ACCEPTED',
    REQUEST_AMOUNT_SHOULD_BE_GREATER_THAN_ZERO = 'payment.REQUEST_AMOUNT_SHOULD_BE_GREATER_THAN_ZERO',
    NO_CARDS_FOUND_FOR_THIS_USER = 'payment.NO_CARDS_FOUND_FOR_THIS_USER',
    PAYMENT_CREATED_AND_PAID_SUCCESSFULLY = 'payment.PAYMENT_CREATED_AND_PAID_SUCCESSFULLY',
    PAYMENT_SUCCESS_SCREEN_MESSAGE = 'payment.PAYMENT_SUCCESS_SCREEN_MESSAGE',
    PAYMENT_SUCCESS_SCREEN_DESCRIPTION = 'payment.PAYMENT_SUCCESS_SCREEN_DESCRIPTION',
    PAYMENT_FAILUR_SCREEN_MESSAGE = 'payment.PAYMENT_FAILUR_SCREEN_MESSAGE',
    PAYMENT_FAILUR_SCREEN_DESCRIPTION = 'payment.PAYMENT_FAILUR_SCREEN_DESCRIPTION',
    PAYMENT_DECLINED_SCREEN_MESSAGE = 'payment.PAYMENT_DECLINED_SCREEN_MESSAGE',
    PAYMENT_DECLINED_SCREEN_DESCRIPTION = 'payment.PAYMENT_DECLINED_SCREEN_DESCRIPTION',
    PAYMENT_ALREADY_SUCCEDED = 'payment.PAYMENT_ALREADY_SUCCEDED',
}

export enum BillTransactionMessagesKeys {
    CREATED_SUCCESSFULLY = 'bill.CREATED_SUCCESSFULLY',
}

export enum CreditCardMessagesKeys {
    DELETED_SUCCESSFULY = 'creditcard.DELETED_SUCCESSFULY',
    CARD_NOT_FOUND = 'creditcard.CARD_NOT_FOUND',
}

export enum TransactionStatus {
    REGISTERED = 'registered',
    INITIATED = 'initiated',
    PROCESSING = 'processing',
    SUCCEEDED = 'succeeded',
    FAILED = 'failed',
    PENDING = 'pending',
    REFUNDED = 'refunded',
}

export interface IPaymobTransactionCallBack {
    id: number;
    amountInCents: number;
    paymobOrderId: string;
    generatedOrderId?: string;
    isPending: boolean;
    isSucceeded: boolean;
    isRefunded: boolean;
    integrationId: number;
    paymobUserId?: number;
    status?: TransactionStatus;
    transactionResponseCode?: string;
    data: any;
}

export interface IPayWithSavedTokenResponse {
    id: number;
    pending: boolean;
    amount_cents: number;
    success: boolean;
    is_auth: boolean;
    is_capture: boolean;
    is_standalone_payment: boolean;
    is_voided: boolean;
    is_refunded: boolean;
    is_3d_secure: boolean;
    integration_id: number;
    profile_id: number;
    has_parent_transaction: boolean;
    order: number;
    created_at: Date;
    currency: string;
    terminal_id: string;
    is_void: boolean;
    is_refund: boolean;
    error_occured: boolean;
    refunded_amount_cents: number;
    captured_amount: number;
    merchant_staff_tag: string;
    owner: number;
    parent_transaction: string;
    merchant_order_id: null;
    'data.message': string;
    'source_data.type': string;
    'source_data.pan': string;
    'source_data.sub_type': string;
    acq_response_code: string;
    txn_response_code: string;
    hmac: string;
    merchant_txn_ref: string;
    use_redirection: boolean;
    redirection_url: string;
    merchant_response: string;
    bypass_step_six: boolean;
}
