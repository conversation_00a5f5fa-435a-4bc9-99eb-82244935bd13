'use strict';

import {
    Body,
    Controller,
    Get,
    Param,
    Post,
    Query,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang, I18nService } from 'nestjs-i18n';

import { RoleType, TransactionKind } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { PageOptionsDto } from '../../common/dto/pageOptionsDto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateDonationUrlDto } from './dto/create-donation-url.dto';
import { CreatePaymentUrlDto } from './dto/create-payment-url.dto';
import { CreatePaymentWithSavedTokenDto } from './dto/create-payment-with-saved-token.dto';
import { GetCardsDto } from './dto/get-cards.dto';
import { PaymentDto } from './dto/payment.dto';
import { PaymentsPageDto } from './dto/payments-page.dto';
import { PaymentMessagesKeys } from './payment.enum';
import { CreditCardsService } from './services/creditcards.service';
import { PaymentsService } from './services/payments.service';
import { IPaymobProcessingCallbackPayload } from './interfaces/i-paymob-processing-callback-payload';
import { VerifyPaymobHmac } from '../../verify-paymob-hmac/verify-paymob-hmac.decorator';

@Controller('payments')
@ApiTags('payments')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class PaymentsController {
    constructor(
        private paymentsService: PaymentsService,
        private creditCardsService: CreditCardsService,
        private readonly i18n: I18nService,
    ) {}

    /** ********************* CRUD Operations *****************************/

    @Post('create-payment-url')
    @Roles(RoleType.PATIENT, RoleType.DOCTOR)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'create payment url',
        type: String,
    })
    createPaymentURL(
        @Req() req: Request,
        @Body() createPaymentDto: CreatePaymentUrlDto,
        @I18nLang() lang: string,
    ): Promise<string> {
        const type =
            createPaymentDto.type === undefined
                ? TransactionKind.REQUEST_FEE
                : createPaymentDto.type;
        return this.paymentsService.createPaymentURL(
            req.user.id,
            createPaymentDto.requestId,
            type,
            lang,
        );
    }

    @Get('cards')
    @Roles(RoleType.PATIENT, RoleType.DOCTOR)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'get all cards',
        type: [GetCardsDto],
    })
    getAllCards(@Req() req: Request): Promise<GetCardsDto[]> {
        return this.creditCardsService.getAllCard(req.user.id);
    }

    @Post('create-payment-with-saved-token')
    @Roles(RoleType.PATIENT, RoleType.DOCTOR)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'create payment with saved token',
        type: BasicOperationsResponse,
    })
    payWithSavedToken(
        @Req() req: Request,
        @Body() createPaymentDto: CreatePaymentWithSavedTokenDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.paymentsService.payWithSavedToken(
            req.user.id,
            createPaymentDto.cardId,
            createPaymentDto.requestId,
            createPaymentDto.type,
            lang,
        );
    }

    @Post('processing/callback')
    @ApiResponse({
        description: 'verify payment',
        type: String,
    })
    @VerifyPaymobHmac()
    processingCallback(
        @Body() data: IPaymobProcessingCallbackPayload,
    ): Promise<string> {
        return this.paymentsService.processingCallback(data);
    }

    @Get('response/callback')
    @ApiResponse({
        description: 'verify payment',
        type: String,
    })
    async responseCallback(
        @Res() res: Response,
        @Req() req: Request,
        @Param('lang') lang: string,
    ): Promise<void> {
        try {
            const response = await this.paymentsService.responseCallback(
                req.query,
            );
            switch (response.status) {
                case 'failed':
                    return res.render('failed-payment', {
                        message: await this.i18n.translate(
                            PaymentMessagesKeys.PAYMENT_FAILUR_SCREEN_MESSAGE,
                            {
                                lang: response.lang,
                            },
                        ),
                        description: await this.i18n.translate(
                            PaymentMessagesKeys.PAYMENT_FAILUR_SCREEN_DESCRIPTION,
                            {
                                lang: response.lang,
                            },
                        ),
                    });
                case 'declined':
                    return res.render('failed-payment', {
                        message: await this.i18n.translate(
                            PaymentMessagesKeys.PAYMENT_DECLINED_SCREEN_MESSAGE,
                            {
                                lang: response.lang,
                            },
                        ),
                        description: await this.i18n.translate(
                            PaymentMessagesKeys.PAYMENT_DECLINED_SCREEN_DESCRIPTION,
                            {
                                lang: response.lang,
                            },
                        ),
                    });
                case 'success':
                    return res.render('sucess-payment', {
                        message: await this.i18n.translate(
                            PaymentMessagesKeys.PAYMENT_SUCCESS_SCREEN_MESSAGE,
                            {
                                lang: response.lang,
                            },
                        ),
                        description:
                            response.kind === TransactionKind.REQUEST_FEE
                                ? await this.i18n.translate(
                                      PaymentMessagesKeys.PAYMENT_SUCCESS_SCREEN_DESCRIPTION,
                                      {
                                          lang: response.lang,
                                      },
                                  )
                                : '',
                    });
            }
        } catch (err) {
            console.error(err);
            return res.render('failed-payment', {
                message: await this.i18n.translate(
                    PaymentMessagesKeys.PAYMENT_FAILUR_SCREEN_MESSAGE,
                    {
                        lang,
                    },
                ),
                description: await this.i18n.translate(
                    PaymentMessagesKeys.PAYMENT_FAILUR_SCREEN_DESCRIPTION,
                    {
                        lang,
                    },
                ),
            });
        }
    }

    @Get('/download')
    @ApiResponse({
        description: 'Download payments',
        type: [String],
    })
    async downloadBills(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
        @I18nLang() lang: string,
    ): Promise<void> {
        const data = await this.paymentsService.downloadPayments(
            providerId,
            query,
            req.user,
            lang,
            TransactionKind.REQUEST_FEE,
        );
        res.setHeader(
            'Content-disposition',
            'attachment; filename=payments.csv',
        );
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get('/donations/download')
    @ApiResponse({
        description: 'Download payments',
        type: [String],
    })
    async downloadDonationBills(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
        @Query('providerId') providerId: string,
        @I18nLang() lang: string,
    ): Promise<void> {
        const data = await this.paymentsService.downloadPayments(
            providerId,
            query,
            req.user,
            lang,
            TransactionKind.DONATION_FEE,
        );
        res.setHeader(
            'Content-disposition',
            'attachment; filename=payments.csv',
        );
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Get()
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get Payments',
        type: [PaymentDto],
    })
    getPayments(
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<PaymentsPageDto> {
        return this.paymentsService.getPayments(
            query,
            pageOptionsDto,
            req.user,
            lang,
            TransactionKind.REQUEST_FEE,
        );
    }

    @Get('donations')
    @UseInterceptors(AuthUserInterceptor)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get Payments',
        type: [PaymentDto],
    })
    getDonationPayments(
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: PageOptionsDto,
        @Req() req: Request,
        @I18nLang() lang: string,
    ): Promise<PaymentsPageDto> {
        return this.paymentsService.getPayments(
            query,
            pageOptionsDto,
            req.user,
            lang,
            TransactionKind.DONATION_FEE,
        );
    }

    @Post('create-donation-url')
    @ApiResponse({
        description: 'create donation url',
        type: String,
    })
    createDonationUrl(
        @Body() createDonationUrlDto: CreateDonationUrlDto,
        @I18nLang() lang: string,
    ): Promise<string> {
        return this.paymentsService.createDonationPaymentUrl(
            createDonationUrlDto,
            lang,
        );
    }

    @Post('bookings/:id/create-booking-url')
    @ApiResponse({
        description: 'create booking url',
        type: String,
    })
    createBookingUrl(
        @Param('id') id: string,
        @I18nLang() lang: string,
    ): Promise<string> {
        return this.paymentsService.createBookingPaymentUrl(+id, lang);
    }
}
