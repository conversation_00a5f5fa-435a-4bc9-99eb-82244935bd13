import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { PaymentDto } from './dto/payment.dto';
import { Transaction } from './entities/transaction.entity';

@Injectable()
export class PaymentsMapper extends AbstractMapper<any, any> {
    fromEntityToDTO(
        destination: ClassType<PaymentDto>,
        sourceObject: Transaction,
    ): PaymentDto {
        return super.fromEntityToDTO(destination, sourceObject);
    }
}
