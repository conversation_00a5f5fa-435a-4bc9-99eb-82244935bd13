import { forwardRef, Module } from '@nestjs/common';

import { ConfigModule } from '../../config/config.module';
import { PaymobHmacGuardGuard } from '../../guards/paymob-hmac-guard/paymob-hmac-guard.guard';
import { SharedModule } from '../../shared/shared.module';
import { BookingsModule } from '../booking/bookings.module';
import { ConsultationRequestsModule } from '../consultation-requests/consultation-requests.module';
import { DoctorsModule } from '../doctors/doctors.module';
import { DoctorsRepository } from '../doctors/repositories/doctors.repository';
import { NotificationsModule } from '../notifications/notifications.module';
import { ServiceProvidersRepository } from '../service-providers/repositories/service-providers.repository';
import { ServiceProvidersModule } from '../service-providers/service-providers.module';
import { UsersModule } from '../users/users.module';
import { BillTransactionsController } from './bill.transaction.controller';
import { BillsController } from './bills.controller';
import { BillsMapper } from './bills.mapper';
import { PaymentsController } from './payments.controller';
import { PaymentsMapper } from './payments.mapper';
import { BillTransactionRepository } from './repositories/bill.transaction.repository';
import { BillsRepository } from './repositories/bills.repository';
import { CreditCardsRepository } from './repositories/creditcards.repository';
import { PaymobTransactionsRepository } from './repositories/payment.logs.repository';
import { TransactionsRepository } from './repositories/payments.repository';
import { CronBills } from './services/bills.cron';
import { BillsService } from './services/bills.service';
import { CreditCardsService } from './services/creditcards.service';
import { PaymentsService } from './services/payments.service';
import { PaymobTransactionsService } from './services/paymob.transactions.service';
import { TransactionsService } from './services/transactions.service';
import { ConfigService } from '../../config/config.service';

@Module({
    imports: [
        SharedModule,
        ConfigModule,
        UsersModule,
        ServiceProvidersModule,
        NotificationsModule,
        BookingsModule,
        forwardRef(() => ConsultationRequestsModule),
        forwardRef(() => DoctorsModule),
    ],
    controllers: [
        PaymentsController,
        BillsController,
        BillTransactionsController,
    ],
    exports: [TransactionsService, PaymentsService, CreditCardsService],
    providers: [
        PaymentsService,
        TransactionsService,
        PaymobTransactionsService,
        PaymentsMapper,
        BillsMapper,
        BillsService,
        CronBills,
        CreditCardsService,
        TransactionsRepository,
        PaymobTransactionsRepository,
        CreditCardsRepository,
        BillsRepository,
        ServiceProvidersRepository,
        DoctorsRepository,
        BillTransactionRepository,
        {
            provide: PaymobHmacGuardGuard,
            useFactory: (configService: ConfigService) =>
                new PaymobHmacGuardGuard(configService),
            inject: [ConfigService],
        },
    ],
})
export class PaymentsModule {}
