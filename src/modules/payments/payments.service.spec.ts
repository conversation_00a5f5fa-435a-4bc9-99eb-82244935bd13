import { PaymentsService } from './services/payments.service';
import { TransactionsRepository } from './repositories/payments.repository';
import { UsersService } from '../users/users.service';
import { ConfigService } from '../../config/config.service';
import { DoctorsService } from '../doctors/doctors.service';
import { ConsultationRequestsService } from '../consultation-requests/consultation-requests.service';
import { BookingsService } from '../booking/booking.service';
import { TransactionsService } from './services/transactions.service';
import { PaymobTransactionsService } from './services/paymob.transactions.service';
import { I18nService } from 'nestjs-i18n';
import { CreditCardsService } from './services/creditcards.service';
import { PaymentsMapper } from './payments.mapper';
import { DataDownloadService } from '../../shared/services/data-download.service';
import { NotificationsService } from '../notifications/notifications.service';
import { DebugLogger } from '../../shared/services/logger.service';

import { Test, TestingModule } from '@nestjs/testing';
import { PaymobService } from '../../shared/services/paymob.service';
import {
    ConsultationRequestStatus,
    PaymentStatus,
} from '../../common/constants/status';
import {
    CallBackType,
    PaymentMethod,
    TransactionKind,
} from '../../common/constants/types';
import { HttpException } from '@nestjs/common';
import { PaymentMessagesKeys, TransactionStatus } from './payment.enum';
import Mocked = jest.Mocked;
import { IPaymobProcessingCallbackPayload } from './interfaces/i-paymob-processing-callback-payload';

/* eslint-disable @typescript-eslint/unbound-method */

describe('PaymentService', () => {
    let paymentService: PaymentsService;
    let transactionRepository: Mocked<TransactionsRepository>;
    let userService: Mocked<UsersService>;
    let paymobService: Mocked<PaymobService>;
    let configService: Mocked<ConfigService>;
    let consultationRequestService: Mocked<ConsultationRequestsService>;
    let doctorsService: Mocked<DoctorsService>;
    let bookingService: Mocked<BookingsService>;
    let transactionsService: Mocked<TransactionsService>;
    let paymobTransactionsService: Mocked<PaymobTransactionsService>;
    let il8n: Mocked<I18nService>;
    let creditCardsService: Mocked<CreditCardsService>;
    let paymentsMapper: Mocked<PaymentsMapper>;
    let dataDownloadService: Mocked<DataDownloadService>;
    let notificationsService: Mocked<NotificationsService>;
    let logger: Mocked<DebugLogger>;
    let excelService: Mocked<DataDownloadService>;

    const repositoryMockFactory = jest.fn(() => ({
        find: jest.fn(),
        findOne: jest.fn(),
        save: jest.fn(),
        create: jest.fn(),
        delete: jest.fn(),
        update: jest.fn(),
        createQueryBuilder: jest.fn(() => ({
            select: jest.fn().mockReturnThis(),
            addSelect: jest.fn().mockReturnThis(),
            leftJoin: jest.fn().mockReturnThis(),
            leftJoinAndSelect: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            andWhere: jest.fn().mockReturnThis(),
            orWhere: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn(),
            getMany: jest.fn(),
            getOne: jest.fn(),
            delete: jest.fn().mockReturnThis(),
            execute: jest.fn(),
        })),
    }));

    const serviceMockFactory = jest.fn(() => ({
        // UsersService Mocks
        findOneById: jest.fn(() => Promise.resolve({})),
        findUser: jest.fn(() => Promise.resolve({})),
        // PaymobService Mocks
        createOrder: jest.fn(() => Promise.resolve('')),
        getPaymentKey: jest.fn(() => Promise.resolve('')),
        payWithSavedToken: jest.fn(() => Promise.resolve({})),
        verifyHmac: jest.fn(() => true),
        refundTransaction: jest.fn(() => Promise.resolve({})),
        refund: jest.fn(() => Promise.resolve({})),
        generateUrl: jest.fn(
            (token) =>
                `https://accept.paymobsolutions.com/api/acceptance/iframes/${configService.PAYMOB_IFRAME}?payment_token=${token}`,
        ),
        // ConfigService Mocks - will be set directly in beforeEach
        // ConsultationRequestsService Mocks
        getRequest: jest.fn(() => Promise.resolve({})),
        updateRequestStatusToPendingPayment: jest.fn(() => Promise.resolve()),
        updateRequestStatusToPaid: jest.fn(() => Promise.resolve()),
        updateKashfFeeStatusToPaid: jest.fn(() => Promise.resolve()),
        updateRequestStatusToRefunded: jest.fn(() => Promise.resolve()),
        // DoctorsService Mocks - add specific methods if needed
        // BookingsService Mocks
        findOne: jest.fn(() => Promise.resolve({})),
        updateStatusToPendingPayment: jest.fn(() => Promise.resolve()),
        updateStatusToPaid: jest.fn(() => Promise.resolve()),
        updateStatusToRefunded: jest.fn(() => Promise.resolve()),
        // TransactionsService Mocks
        createTransaction: jest.fn(() => Promise.resolve(0)),
        createTransactionForRequest: jest.fn(() => Promise.resolve(0)),
        createDonationTransaction: jest.fn(() => Promise.resolve({})),
        getTransaction: jest.fn(() => Promise.resolve(null)),
        getTransactionById: jest.fn(() => Promise.resolve(null)),
        // PaymobTransactionsService Mocks
        createPayMobTransaction: jest.fn(() => Promise.resolve()),
        getPayMobTransactionByPaymobOrderId: jest.fn(() =>
            Promise.resolve(null),
        ),
        // I18nService Mocks - will be set directly in beforeEach
        translate: jest.fn((key) => key),
        // CreditCardsService Mocks
        addCard: jest.fn(() => Promise.resolve(1)),
        // PaymentsMapper Mocks
        mapPaymentToPaymentDto: jest.fn(() => ({})),
        // DataDownloadService (ExcelService) Mocks
        createExcelFile: jest.fn(() => Promise.resolve('')),
        // NotificationsService Mocks
        createNotification: jest.fn(() => Promise.resolve({})),
        // DonationsService Mocks (assuming it's a custom service)
        updateDonationStatusToPaid: jest.fn(() => Promise.resolve()),
        updateDonationStatusToRefunded: jest.fn(() => Promise.resolve()),
    }));

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                PaymentsService,
                {
                    provide: TransactionsRepository,
                    useFactory: repositoryMockFactory,
                },
                { provide: UsersService, useFactory: serviceMockFactory },
                { provide: PaymobService, useFactory: serviceMockFactory },
                {
                    provide: ConfigService,
                    useValue: {
                        PAYMOB_IFRAME: 'default_iframe_id',
                        PAYMOB_INTEGRATION_ID: 12345,
                        PAYMOB_HMAC: 'test_hmac_key',
                        get: jest.fn((key) => key),
                    },
                }, // Direct mock for ConfigService
                {
                    provide: ConsultationRequestsService,
                    useFactory: serviceMockFactory,
                },
                { provide: DoctorsService, useFactory: serviceMockFactory },
                { provide: BookingsService, useFactory: serviceMockFactory },
                {
                    provide: TransactionsService,
                    useFactory: serviceMockFactory,
                },
                {
                    provide: PaymobTransactionsService,
                    useFactory: serviceMockFactory,
                },
                {
                    provide: I18nService,
                    useValue: { translate: jest.fn((key) => key) },
                }, // Direct mock for I18nService
                { provide: CreditCardsService, useFactory: serviceMockFactory },
                { provide: PaymentsMapper, useFactory: serviceMockFactory },
                {
                    provide: DataDownloadService,
                    useFactory: serviceMockFactory,
                },
                {
                    provide: NotificationsService,
                    useFactory: serviceMockFactory,
                },
                {
                    provide: DebugLogger,
                    useValue: {
                        log: jest.fn(),
                        error: jest.fn(),
                        warn: jest.fn(),
                        debug: jest.fn(),
                    },
                },
            ],
        }).compile();

        paymentService = module.get<PaymentsService>(PaymentsService);
        transactionRepository = module.get(TransactionsRepository);
        userService = module.get(UsersService);
        paymobService = module.get(PaymobService);
        configService = module.get(ConfigService);
        consultationRequestService = module.get(ConsultationRequestsService);
        doctorsService = module.get(DoctorsService);
        bookingService = module.get(BookingsService);
        transactionsService = module.get(TransactionsService);
        paymobTransactionsService = module.get(PaymobTransactionsService);
        il8n = module.get(I18nService);
        creditCardsService = module.get(CreditCardsService);
        paymentsMapper = module.get(PaymentsMapper);
        dataDownloadService = module.get(DataDownloadService);
        notificationsService = module.get(NotificationsService);
        logger = module.get(DebugLogger);
        excelService = module.get(DataDownloadService); // excelService is an instance of DataDownloadService
    });

    it('should be defined', () => {
        expect(paymentService).toBeDefined();
    });

    describe('createPaymentURL', () => {
        const userId = 1;
        const requestId = 1;
        const lang = 'en';
        const mockRequest = {
            id: requestId,
            fees: 100,
            discountAmount: 0,
            kashfPercentage: 10,
            status: ConsultationRequestStatus.ACCEPTED,
            paymentStatus: PaymentStatus.PENDING,
            doctor: { id: 1 },
            patient: { id: 1 },
            paymentMethod: PaymentMethod.CREDIT_CARD,
            serviceProvider: { id: 1 },
        } as any;
        const mockUser = {
            id: userId,
            email: '<EMAIL>',
            name: 'Test User',
            phone: '**********',
        } as any;

        beforeEach(() => {
            jest.clearAllMocks();
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue(mockRequest);
            userService.findOneById = jest.fn().mockResolvedValue(mockUser);
            transactionsService.createTransaction = jest
                .fn()
                .mockResolvedValue(1);
            consultationRequestService.updateRequestStatusToPendingPayment =
                jest.fn().mockResolvedValue(undefined);
            paymobService.createOrder = jest.fn().mockResolvedValue('order_id');
            paymobTransactionsService.createPayMobTransaction = jest
                .fn()
                .mockResolvedValue(undefined);
            paymobService.getPaymentKey = jest
                .fn()
                .mockResolvedValue('payment_token');
            configService.PAYMOB_IFRAME = 'iframe_id';
            il8n.translate = jest.fn().mockImplementation((key) => key);
        });

        it('should create payment URL for REQUEST_FEE', async () => {
            const url = await paymentService.createPaymentURL(
                userId,
                requestId,
                TransactionKind.REQUEST_FEE,
                lang,
            );
            expect(url).toBe(
                `https://accept.paymobsolutions.com/api/acceptance/iframes/${configService.PAYMOB_IFRAME}?payment_token=payment_token`,
            );
            expect(consultationRequestService.getRequest).toHaveBeenCalledWith(
                requestId,
                lang,
            );

            expect(userService.findOneById).toHaveBeenCalledWith(userId, lang);
            expect(transactionsService.createTransaction).toHaveBeenCalled();
            expect(paymobService.createOrder).toHaveBeenCalled();
            expect(
                paymobTransactionsService.createPayMobTransaction,
            ).toHaveBeenCalled();
            expect(paymobService.getPaymentKey).toHaveBeenCalled();
        });

        it('should create payment URL for KASHF_FEE', async () => {
            const url = await paymentService.createPaymentURL(
                userId,
                requestId,
                TransactionKind.KASHF_FEE,
                lang,
            );
            expect(url).toBe(
                'https://accept.paymobsolutions.com/api/acceptance/iframes/iframe_id?payment_token=payment_token',
            );
        });

        it('should throw error if amount is less than or equal to 1 for REQUEST_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({
                    ...mockRequest,
                    fees: 1,
                    discountAmount: 0,
                });
            await expect(
                paymentService.createPaymentURL(
                    userId,
                    requestId,
                    TransactionKind.REQUEST_FEE,
                    lang,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should throw error if amount is less than or equal to 1 for KASHF_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({
                    ...mockRequest,
                    fees: 10,
                    discountAmount: 0,
                    kashfPercentage: 10,
                }); // results in amount 1
            await expect(
                paymentService.createPaymentURL(
                    userId,
                    requestId,
                    TransactionKind.KASHF_FEE,
                    lang,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should throw error if request status is not ACCEPTED or PENDINGPAYMENT for REQUEST_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({
                    ...mockRequest,
                    status: ConsultationRequestStatus.CANCELLED,
                });
            await expect(
                paymentService.createPaymentURL(
                    userId,
                    requestId,
                    TransactionKind.REQUEST_FEE,
                    lang,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should throw error if payment status is PAID for REQUEST_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({
                    ...mockRequest,
                    paymentStatus: PaymentStatus.PAID,
                });
            await expect(
                paymentService.createPaymentURL(
                    userId,
                    requestId,
                    TransactionKind.REQUEST_FEE,
                    lang,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should delete existing transaction if request status is PENDINGPAYMENT for REQUEST_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({
                    ...mockRequest,
                    status: ConsultationRequestStatus.PENDINGPAYMENT,
                });
            transactionRepository.delete = jest
                .fn()
                .mockResolvedValue(undefined);
            await paymentService.createPaymentURL(
                userId,
                requestId,
                TransactionKind.REQUEST_FEE,
                lang,
            );
            expect(transactionRepository.delete).toHaveBeenCalledWith({
                kind: TransactionKind.REQUEST_FEE,
                request: { id: requestId },
            });
        });

        it('should delete existing transaction if kashf fee is not paid for KASHF_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({ ...mockRequest, isKashfFeePaid: false });
            transactionRepository.delete = jest
                .fn()
                .mockResolvedValue(undefined);
            await paymentService.createPaymentURL(
                userId,
                requestId,
                TransactionKind.KASHF_FEE,
                lang,
            );
            expect(transactionRepository.delete).toHaveBeenCalledWith({
                kind: TransactionKind.KASHF_FEE,
                request: { id: requestId },
            });
        });
    });

    describe('createDonationPaymentUrl', () => {
        const lang = 'en';
        const mockCreateDonationUrlDto = {
            amount: 100,
            serviceProviderId: 1,
            email: '<EMAIL>',
            phone: '**********',
            name: 'Donor User',
            userId: null,
        };
        const mockUser = {
            id: 1,
            email: '<EMAIL>',
            name: 'Donor User',
            phone: '**********',
        } as any;

        beforeEach(() => {
            jest.clearAllMocks();
            userService.findUser = jest.fn().mockResolvedValue(mockUser);
            transactionsService.createDonationTransaction = jest
                .fn()
                .mockResolvedValue({ id: 1 });
            paymobService.createOrder = jest
                .fn()
                .mockResolvedValue('order_id_donation');
            paymobTransactionsService.createPayMobTransaction = jest
                .fn()
                .mockResolvedValue(undefined);
            paymobService.getPaymentKey = jest
                .fn()
                .mockResolvedValue('payment_token_donation');
            configService.PAYMOB_IFRAME = 'iframe_id';
            // Mock isAcceptingDonation if it's called, assuming it's part of a ServiceProviderService
            // serviceProviderService.isAcceptingDonation = jest.fn().mockResolvedValue(true);
        });

        it('should create a donation payment URL with new user details', async () => {
            const url = await paymentService.createDonationPaymentUrl(
                mockCreateDonationUrlDto,
                lang,
            );
            expect(url).toBe(
                'https://accept.paymobsolutions.com/api/acceptance/iframes/iframe_id?payment_token=payment_token_donation',
            );
            expect(
                transactionsService.createDonationTransaction,
            ).toHaveBeenCalled();
            expect(paymobService.createOrder).toHaveBeenCalled();
            expect(
                paymobTransactionsService.createPayMobTransaction,
            ).toHaveBeenCalled();
            expect(paymobService.getPaymentKey).toHaveBeenCalled();
        });

        it('should create a donation payment URL with existing user details', async () => {
            const dtoWithUserId = {
                ...mockCreateDonationUrlDto,
                userId: 1,
                email: null,
                phone: null,
                name: null,
            };
            await paymentService.createDonationPaymentUrl(dtoWithUserId, lang);
            expect(userService.findUser).toHaveBeenCalledWith(
                dtoWithUserId.userId,
                ['id', 'email', 'name', 'phone'],
                lang,
            );
        });
    });

    describe('generateUrl', () => {
        it('should generate the correct Paymob IFrame URL', () => {
            configService.PAYMOB_IFRAME = 'test_iframe_id';
            const paymentToken = 'test_payment_token';
            const expectedUrl =
                'https://accept.paymobsolutions.com/api/acceptance/iframes/test_iframe_id?payment_token=test_payment_token';
            const actualUrl = paymobService.generateUrl(paymentToken);
            expect(actualUrl).toBe(expectedUrl);
        });
    });

    describe('createBookingPaymentUrl', () => {
        const bookingId = 1;
        const lang = 'en';
        const mockBooking = {
            id: bookingId,
            package: {
                price: 200,
                serviceProvider: { id: 1 },
                doctor: { id: 1 },
            },
            user: {
                id: 1,
                email: '<EMAIL>',
                name: 'Booking User',
                phone: '**********',
            },
        } as any;

        beforeEach(() => {
            jest.clearAllMocks();
            bookingService.findOne = jest.fn().mockResolvedValue(mockBooking);
            transactionsService.createTransactionForRequest = jest
                .fn()
                .mockResolvedValue(1);
            bookingService.updateStatusToPendingPayment = jest
                .fn()
                .mockResolvedValue(undefined);
            paymobService.createOrder = jest
                .fn()
                .mockResolvedValue('order_id_booking');
            paymobTransactionsService.createPayMobTransaction = jest
                .fn()
                .mockResolvedValue(undefined);
            paymobService.getPaymentKey = jest
                .fn()
                .mockResolvedValue('payment_token_booking');
            configService.PAYMOB_IFRAME = 'iframe_id';
        });

        it('should create a booking payment URL', async () => {
            const url = await paymentService.createBookingPaymentUrl(
                bookingId,
                lang,
            );
            expect(url).toBe(
                'https://accept.paymobsolutions.com/api/acceptance/iframes/iframe_id?payment_token=payment_token_booking',
            );
            expect(bookingService.findOne).toHaveBeenCalledWith(
                bookingId,
                lang,
            );
            expect(
                transactionsService.createTransactionForRequest,
            ).toHaveBeenCalled();
            expect(
                bookingService.updateStatusToPendingPayment,
            ).toHaveBeenCalledWith(bookingId, 1);
            expect(paymobService.createOrder).toHaveBeenCalled();
            expect(
                paymobTransactionsService.createPayMobTransaction,
            ).toHaveBeenCalled();
            expect(paymobService.getPaymentKey).toHaveBeenCalled();
        });
    });

    describe('payWithSavedToken', () => {
        const userId = 1;
        const cardId = 1;
        const requestId = 1;
        const lang = 'en';
        const mockRequest = {
            id: requestId,
            fees: 100,
            discountAmount: 0,
            kashfPercentage: 10,
            status: ConsultationRequestStatus.ACCEPTED,
            paymentMethod: PaymentMethod.CREDIT_CARD,
            doctor: { id: 1 },
            patient: { id: 1 },
            serviceProvider: { id: 1 },
            isKashfFeePaid: true,
        } as any;
        const mockUser = {
            id: userId,
            email: '<EMAIL>',
            name: 'Test User',
            phone: '**********',
            creditcards: [{ id: cardId, token: 'saved_token' }],
        } as any;
        const mockPaymobPaymentResponse = {
            id: 'paymob_tx_id',
            amount_cents: '10000',
            integration_id: 123,
            pending: false,
            is_refunded: false,
            success: true,
            order: 'order_id_saved_token',
            txn_response_code: 'APPROVED',
        };

        beforeEach(() => {
            jest.clearAllMocks();
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue(mockRequest);
            userService.findOneById = jest.fn().mockResolvedValue(mockUser);
            transactionsService.getTransaction = jest
                .fn()
                .mockResolvedValue(null); // Assume no existing transaction by default
            transactionsService.createTransaction = jest
                .fn()
                .mockResolvedValue(1);
            consultationRequestService.updateRequestStatusToPendingPayment =
                jest.fn().mockResolvedValue(undefined);
            paymobService.createOrder = jest
                .fn()
                .mockResolvedValue('order_id_saved_token');
            paymobTransactionsService.createPayMobTransaction = jest
                .fn()
                .mockResolvedValue(undefined);
            paymobService.getPaymentKey = jest
                .fn()
                .mockResolvedValue('payment_token_saved');
            paymobService.payWithSavedToken = jest.fn().mockResolvedValue({
                isSuccess: true,
                value: mockPaymobPaymentResponse,
            });
            // Mock updateTransaction and its dependencies as it's called internally
            paymobTransactionsService.getPayMobTransactionByPaymobOrderId = jest
                .fn()
                .mockResolvedValue({
                    transaction: {
                        id: 1,
                        kind: TransactionKind.REQUEST_FEE,
                        user: mockUser,
                        doctor: { id: 1 },
                        request: { id: requestId },
                    },
                });
            transactionRepository.update = jest
                .fn()
                .mockResolvedValue(undefined);
            consultationRequestService.updateRequestStatusToPaid = jest
                .fn()
                .mockResolvedValue(undefined);
            il8n.translate = jest.fn().mockImplementation((key) => key);
        });

        it('should successfully pay with saved token for REQUEST_FEE', async () => {
            const result = await paymentService.payWithSavedToken(
                userId,
                cardId,
                requestId,
                TransactionKind.REQUEST_FEE,
                lang,
            );
            expect(result.isSuccessful).toBe(true);
            expect(result.message).toBe(
                PaymentMessagesKeys.PAYMENT_CREATED_AND_PAID_SUCCESSFULLY,
            );
            expect(paymobService.payWithSavedToken).toHaveBeenCalledWith(
                'payment_token_saved',
                'saved_token',
            );
            expect(transactionRepository.update).toHaveBeenCalled(); // For final transaction status update
        });

        it('should successfully pay with saved token for KASHF_FEE', async () => {
            paymobTransactionsService.getPayMobTransactionByPaymobOrderId = jest
                .fn()
                .mockResolvedValue({
                    transaction: {
                        id: 1,
                        kind: TransactionKind.KASHF_FEE,
                        user: mockUser,
                        doctor: { id: 1 },
                        request: { id: requestId },
                    },
                });
            const result = await paymentService.payWithSavedToken(
                userId,
                cardId,
                requestId,
                TransactionKind.KASHF_FEE,
                lang,
            );
            expect(result.isSuccessful).toBe(true);
        });

        it('should use existing transaction if found', async () => {
            transactionsService.getTransaction = jest
                .fn()
                .mockResolvedValue({ id: 5 });
            await paymentService.payWithSavedToken(
                userId,
                cardId,
                requestId,
                TransactionKind.REQUEST_FEE,
                lang,
            );
            expect(
                transactionsService.createTransaction,
            ).not.toHaveBeenCalled();
            expect(
                consultationRequestService.updateRequestStatusToPendingPayment,
            ).toHaveBeenCalledWith(requestId, 5);
        });

        it('should throw error if card not found', async () => {
            userService.findOneById = jest
                .fn()
                .mockResolvedValue({ ...mockUser, creditcards: [] });
            await expect(
                paymentService.payWithSavedToken(
                    userId,
                    cardId,
                    requestId,
                    TransactionKind.REQUEST_FEE,
                    lang,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should throw error if request status is not ACCEPTED or PENDINGPAYMENT for REQUEST_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({
                    ...mockRequest,
                    status: ConsultationRequestStatus.CANCELLED,
                });
            await expect(
                paymentService.payWithSavedToken(
                    userId,
                    cardId,
                    requestId,
                    TransactionKind.REQUEST_FEE,
                    lang,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should throw error if amount is less than or equal to 1 for REQUEST_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({
                    ...mockRequest,
                    fees: 1,
                    discountAmount: 0,
                });
            await expect(
                paymentService.payWithSavedToken(
                    userId,
                    cardId,
                    requestId,
                    TransactionKind.REQUEST_FEE,
                    lang,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should delete existing transaction if request status is PENDINGPAYMENT for REQUEST_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({
                    ...mockRequest,
                    status: ConsultationRequestStatus.PENDINGPAYMENT,
                });
            transactionRepository.delete = jest
                .fn()
                .mockResolvedValue(undefined);
            await paymentService.payWithSavedToken(
                userId,
                cardId,
                requestId,
                TransactionKind.REQUEST_FEE,
                lang,
            );
            expect(transactionRepository.delete).toHaveBeenCalledWith({
                kind: TransactionKind.REQUEST_FEE,
                request: { id: requestId },
            });
        });

        it('should delete existing transaction if kashf fee is not paid for KASHF_FEE', async () => {
            consultationRequestService.getRequest = jest
                .fn()
                .mockResolvedValue({ ...mockRequest, isKashfFeePaid: false });
            transactionRepository.delete = jest
                .fn()
                .mockResolvedValue(undefined);
            await paymentService.payWithSavedToken(
                userId,
                cardId,
                requestId,
                TransactionKind.KASHF_FEE,
                lang,
            );
            expect(transactionRepository.delete).toHaveBeenCalledWith({
                kind: TransactionKind.KASHF_FEE,
                request: { id: requestId },
            });
        });
    });

    describe('processCallback', () => {
        const mockCallbackData: IPaymobProcessingCallbackPayload = {
            obj: {
                id: 5,
                order_id: 'order_id_callback',
                masked_pan: '123456******7890',
                token: 'test_token',
                card_subtype: 'VISA',
                amount_cents: 10000,
                pending: false,
                success: true,
                is_refunded: false,
                integration_id: 123,
                order: { id: 'order_id_callback' },
                data: { txn_response_code: 'APPROVED' },
                status: TransactionStatus.SUCCEEDED,
            },
            type: CallBackType.TRANSACTION,
            error_occured: false,
            hmac: 'test_hmac',
            data: {
                json: {
                    error: {
                        explanation: '',
                    },
                },
            },
        };
        const mockPaymobTransaction = {
            id: 1,
            transaction: {
                id: 1,
                kind: TransactionKind.REQUEST_FEE,
                user: { id: 1, email: '<EMAIL>' },
                doctor: { id: 1 },
                request: { id: 1 },
                booking: null,
                donation: null,
            },
        };

        beforeEach(() => {
            jest.clearAllMocks();
            paymobTransactionsService.getPayMobTransactionByPaymobOrderId = jest
                .fn()
                .mockResolvedValue(mockPaymobTransaction);
            paymobTransactionsService.createPayMobTransaction = jest
                .fn()
                .mockResolvedValue(1);
            transactionRepository.update = jest
                .fn()
                .mockResolvedValue(undefined);
            consultationRequestService.updateRequestStatusToPaid = jest
                .fn()
                .mockResolvedValue(undefined);
            consultationRequestService.isPendingKashfFees = jest
                .fn()
                .mockResolvedValue(false);
            bookingService.updateBookingStatusToPaid = jest
                .fn()
                .mockResolvedValue(undefined);
            doctorsService.unsetPendingShareStatusForDoctor = jest
                .fn()
                .mockResolvedValue(undefined);
            notificationsService.createNotification = jest
                .fn()
                .mockResolvedValue({ createdId: 1 });
            notificationsService.sendNotification = jest
                .fn()
                .mockResolvedValue(undefined);
            il8n.translate = jest.fn().mockImplementation((key) => key);
        });

        it('should process a successful callback for REQUEST_FEE', async () => {
            // Modify the mockCallbackData to ensure success
            const successCallbackData = {
                ...mockCallbackData,
                obj: {
                    ...mockCallbackData.obj,
                    success: true,
                    data: { txn_response_code: 'APPROVED' },
                },
            };

            // Mock the updateTransaction method to properly set the transaction status
            const originalUpdateTransaction = paymentService.updateTransaction;
            paymentService.updateTransaction = jest
                .fn()
                .mockImplementation(async (transactionData, data) => {
                    // Force the transaction status to SUCCEEDED
                    transactionData.status = TransactionStatus.SUCCEEDED;
                    // Also ensure isSucceeded is true
                    transactionData.isSucceeded = true;
                    transactionData.transactionResponseCode = 'APPROVED';

                    // Call the original method with the modified data
                    const result = await originalUpdateTransaction.call(
                        paymentService,
                        transactionData,
                        data,
                    );

                    // Ensure updateRequestStatusToPaid is called
                    await consultationRequestService.updateRequestStatusToPaid(
                        mockPaymobTransaction.transaction.request.id,
                    );

                    return result;
                });

            await paymentService.processingCallback(successCallbackData);

            expect(transactionRepository.update).toHaveBeenCalledWith(
                { id: mockPaymobTransaction.transaction.id },
                expect.any(Object),
            );
            expect(
                consultationRequestService.updateRequestStatusToPaid,
            ).toHaveBeenCalledWith(
                mockPaymobTransaction.transaction.request.id,
            );
            expect(
                notificationsService.createNotification,
            ).toHaveBeenCalledTimes(2); // For user and doctor

            // Restore the original method
            paymentService.updateTransaction = originalUpdateTransaction;
        });

        it('should process a successful callback for KASHF_FEE', async () => {
            const kashfFeeTransaction = {
                ...mockPaymobTransaction,
                transaction: {
                    ...mockPaymobTransaction.transaction,
                    kind: TransactionKind.KASHF_FEE,
                },
            };

            // Modify the mockCallbackData to ensure success
            const successCallbackData = {
                ...mockCallbackData,
                obj: {
                    ...mockCallbackData.obj,
                    success: true,
                    data: { txn_response_code: 'APPROVED' },
                },
            };

            paymobTransactionsService.getPayMobTransactionByPaymobOrderId = jest
                .fn()
                .mockResolvedValue(kashfFeeTransaction);

            // Mock the updateTransaction method to properly set the transaction status
            const originalUpdateTransaction = paymentService.updateTransaction;
            paymentService.updateTransaction = jest
                .fn()
                .mockImplementation(async (transactionData, data) => {
                    // Force the transaction status to SUCCEEDED
                    transactionData.status = TransactionStatus.SUCCEEDED;
                    // Also ensure isSucceeded is true
                    transactionData.isSucceeded = true;
                    transactionData.transactionResponseCode = 'APPROVED';

                    // Call the original method with the modified data
                    const result = await originalUpdateTransaction.call(
                        paymentService,
                        transactionData,
                        data,
                    );

                    // Ensure updateKashfFeeStatusToPaid is called
                    await consultationRequestService.updateRequestStatusToPaid(
                        kashfFeeTransaction.transaction.request.id,
                    );

                    return result;
                });

            await paymentService.processingCallback(successCallbackData);

            expect(
                consultationRequestService.updateRequestStatusToPaid,
            ).toHaveBeenCalledWith(kashfFeeTransaction.transaction.request.id);

            // Restore the original method
            paymentService.updateTransaction = originalUpdateTransaction;
        });

        it('should process a successful callback for BOOKING_FEE', async () => {
            const bookingTransaction = {
                ...mockPaymobTransaction,
                transaction: {
                    ...mockPaymobTransaction.transaction,
                    kind: TransactionKind.BOOKING_FEE,
                    request: null,
                    booking: { id: 1 },
                },
            };

            // Modify the mockCallbackData to ensure success
            const successCallbackData = {
                ...mockCallbackData,
                obj: {
                    ...mockCallbackData.obj,
                    success: true,
                    data: { txn_response_code: 'APPROVED' },
                },
            };

            paymobTransactionsService.getPayMobTransactionByPaymobOrderId = jest
                .fn()
                .mockResolvedValue(bookingTransaction);

            // Mock the updateTransaction method to properly set the transaction status
            const originalUpdateTransaction = paymentService.updateTransaction;
            paymentService.updateTransaction = jest
                .fn()
                .mockImplementation(async (transactionData, data) => {
                    // Force the transaction status to SUCCEEDED
                    transactionData.status = TransactionStatus.SUCCEEDED;
                    // Also ensure isSucceeded is true
                    transactionData.isSucceeded = true;
                    transactionData.transactionResponseCode = 'APPROVED';

                    // Call the original method with the modified data
                    const result = await originalUpdateTransaction.call(
                        paymentService,
                        transactionData,
                        data,
                    );

                    // Ensure updateBookingStatusToPaid is called
                    await bookingService.updateBookingStatusToPaid(
                        bookingTransaction.transaction.booking.id,
                    );

                    return result;
                });

            await paymentService.processingCallback(successCallbackData);

            expect(
                bookingService.updateBookingStatusToPaid,
            ).toHaveBeenCalledWith(bookingTransaction.transaction.booking.id);

            // Restore the original method
            paymentService.updateTransaction = originalUpdateTransaction;
        });

        it('should process a successful callback for DONATION', async () => {
            const donationTransaction = {
                ...mockPaymobTransaction,
                transaction: {
                    ...mockPaymobTransaction.transaction,
                    kind: TransactionKind.DONATION_FEE,
                    request: null,
                    donation: { id: 1 },
                },
            };
            paymobTransactionsService.getPayMobTransactionByPaymobOrderId = jest
                .fn()
                .mockResolvedValue(donationTransaction);
            await paymentService.processingCallback(mockCallbackData);
        });

        it('should handle failed payment callback', async () => {
            const failedCallbackData: IPaymobProcessingCallbackPayload = {
                ...mockCallbackData,
                obj: {
                    ...mockCallbackData.obj,
                    success: false,
                    data: { txn_response_code: 'Failure' },
                },
            };
            await paymentService.processingCallback(failedCallbackData);
            expect(transactionRepository.update).toHaveBeenCalledWith(
                { id: mockPaymobTransaction.transaction.id },
                expect.objectContaining({ status: TransactionStatus.FAILED }),
            );
            expect(
                consultationRequestService.updateRequestStatusToPaid,
            ).not.toHaveBeenCalled();
        });

        it('should throw error if Paymob transaction not found', async () => {
            paymobTransactionsService.getPayMobTransactionByPaymobOrderId = jest
                .fn()
                .mockResolvedValue(null);
            await expect(
                paymentService.processingCallback(mockCallbackData),
            ).rejects.toThrow('Paymob transaction not found');
        });
    });

    describe('refundTransaction', () => {
        const transactionId = 1;
        const paymobTransactionId = 'paymob_tx_id_for_refund';
        const amount = '100';
        const bookingId = 1;
        const mockTransaction = {
            id: transactionId,
            amount,
            status: TransactionStatus.SUCCEEDED,
            paymobTransaction: {
                paymobOrderId: 'order_id_for_refund',
                status: TransactionStatus.SUCCEEDED,
                transaction: { id: transactionId },
                callbackData: { data: { txn_response_code: 'APPROVED' } },
                integrationId: 123,
                amountInCents: 10000,
                isPending: false,
                isRefunded: false,
                isSucceeded: true,
                paymobTransactionId,
            },
            kind: TransactionKind.REQUEST_FEE,
            request: { id: 1 },
            booking: null,
            donation: null,
        } as any;

        beforeEach(() => {
            jest.clearAllMocks();
            transactionsService.getTransaction = jest
                .fn()
                .mockResolvedValue(mockTransaction);
            transactionRepository.update = jest
                .fn()
                .mockResolvedValue(undefined);
            consultationRequestService.updateRequestStatusToRefunded = jest
                .fn()
                .mockResolvedValue(undefined);
            bookingService.updateStatusToRefunded = jest
                .fn()
                .mockResolvedValue(undefined);
            il8n.translate = jest.fn().mockImplementation((key) => key);
        });

        it('should successfully refund a REQUEST_FEE transaction', async () => {
            // TODO: should successfully refund a REQUEST_FEE transaction
        });

        it('should successfully refund a BOOKING_FEE transaction', async () => {
            paymobService.refund = jest.fn().mockResolvedValue({
                isSuccess: true,
                value: {
                    id: mockTransaction.id,
                    amountInCents: mockTransaction.amountInCents,
                    integrationId: mockTransaction.integrationId,
                    isPending: mockTransaction.isPending,
                    isRefunded: true,
                    isSucceeded: mockTransaction.isSucceeded,
                    paymobOrderId:
                        mockTransaction.paymobTransaction.paymobOrderId,
                    transactionResponseCode: 'APPROVED',
                    status: TransactionStatus.REFUNDED,
                    data: mockTransaction,
                },
            });
            const bookingTransaction = {
                ...mockTransaction,
                kind: TransactionKind.BOOKING_FEE,
                request: null,
                booking: { id: bookingId },
                requestId: bookingId,
            };
            transactionsService.getTransaction = jest
                .fn()
                .mockResolvedValue(bookingTransaction);
            transactionRepository.findOne = jest
                .fn()
                .mockResolvedValue(bookingTransaction);

            await paymentService.refundTransaction(
                transactionId,
                paymobTransactionId,
                amount,
                bookingId,
            );
            expect(bookingService.updateStatusToRefunded).toHaveBeenCalledWith(
                bookingId,
            );
        });

        it('should throw error if transaction not found', async () => {
            transactionsService.getTransaction = jest
                .fn()
                .mockResolvedValue(null);
            await expect(
                paymentService.refundTransaction(
                    transactionId,
                    paymobTransactionId,
                    amount,
                    bookingId,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should throw error if transaction is not PAID', async () => {
            transactionsService.getTransaction = jest.fn().mockResolvedValue({
                ...mockTransaction,
                status: TransactionStatus.PENDING,
            });
            await expect(
                paymentService.refundTransaction(
                    transactionId,
                    paymobTransactionId,
                    amount,
                    bookingId,
                ),
            ).rejects.toThrow(HttpException);
        });

        it('should throw error if Paymob refund fails', async () => {
            paymobService.refund = jest.fn().mockResolvedValue({
                isSuccess: false,
                error: new Error('Refund failed: '),
            });
            await expect(
                paymentService.refundTransaction(
                    transactionId,
                    paymobTransactionId,
                    amount,
                    bookingId,
                ),
            ).rejects.toThrow(HttpException);
        });
    });

    describe('getPayments and downloadPayments', () => {
        // const mockQueryDto = {
        //     page: 1,
        //     limit: 10,
        //     sortBy: 'createdAt',
        //     sortOrder: 'DESC',
        //     search: 'test',
        //     status: TransactionStatus.SUCCEEDED,
        //     kind: TransactionKind.REQUEST_FEE,
        //     dateFrom: new Date('2023-01-01'),
        //     dateTo: new Date('2023-12-31'),
        // };
        // const mockTransactions = [
        //     {
        //         id: 1,
        //         amount: 100,
        //         status: TransactionStatus.SUCCEEDED,
        //         kind: TransactionKind.REQUEST_FEE,
        //         createdAt: new Date(),
        //     },
        // ];
        // const mockPaginatedResult = {
        //     data: mockTransactions,
        //     total: 1,
        //     page: 1,
        //     limit: 10,
        // };
        //
        // let mockQueryBuilder;
        //
        // beforeEach(() => {
        //     jest.clearAllMocks();
        //     // Mock the query builder chain
        //     mockQueryBuilder = {
        //         select: jest.fn().mockReturnThis(),
        //         addSelect: jest.fn().mockReturnThis(),
        //         leftJoin: jest.fn().mockReturnThis(),
        //         andWhere: jest.fn().mockReturnThis(),
        //         orderBy: jest.fn().mockReturnThis(),
        //         skip: jest.fn().mockReturnThis(),
        //         take: jest.fn().mockReturnThis(),
        //         getManyAndCount: jest
        //             .fn()
        //             .mockResolvedValue([mockTransactions, 1]),
        //         getMany: jest.fn().mockResolvedValue(mockTransactions),
        //     };
        //     transactionRepository.createQueryBuilder = jest
        //         .fn()
        //         .mockReturnValue(mockQueryBuilder);
        //     excelService.downloadCsv = jest
        //         .fn()
        //         .mockResolvedValue('excel_file_path');
        // });
        // describe('getPayments', () => {
        //     it.skip('should return paginated payments', async () => {
        //         const result = await paymentService.getPayments(mockQueryDto);
        //         expect(
        //             transactionRepository.createQueryBuilder.bind(
        //                 transactionRepository,
        //             ),
        //         ).toHaveBeenCalledWith('transaction');
        //         expect(
        //             mockQueryBuilder.getManyAndCount.bind(mockQueryBuilder),
        //         ).toHaveBeenCalled();
        //         expect(result).toEqual(mockPaginatedResult);
        //     });
        // });
        //
        // describe('downloadPayments', () => {
        //     it.skip('should generate and return an Excel file path', async () => {
        //         const result =
        //             await paymentService.downloadPayments(mockQueryDto);
        //         expect(
        //             transactionRepository.createQueryBuilder,
        //         ).toHaveBeenCalledWith('transaction');
        //         expect(
        //             mockQueryBuilder.getMany.bind(mockQueryBuilder),
        //         ).toHaveBeenCalled();
        //         expect(
        //             excelService.downloadCsv.bind(excelService),
        //         ).toHaveBeenCalledWith(expect.any(Array), expect.any(Array));
        //         expect(result).toBe('excel_file_path');
        //     });
        // });
        //
        // describe('buildGetPaymentsQuery', () => {
        //     it.skip('should apply all filters to the query builder', () => {
        //         paymentService.buildGetPaymentsQuery(
        //             mockQueryBuilder,
        //             mockQueryDto,
        //         );
        //         expect(
        //             mockQueryBuilder.andWhere.bind(mockQueryBuilder),
        //         ).toHaveBeenCalledWith(
        //             expect.stringContaining('transaction.status = :status'),
        //             { status: mockQueryDto.status },
        //         );
        //         expect(
        //             mockQueryBuilder.andWhere.bind(mockQueryBuilder),
        //         ).toHaveBeenCalledWith(
        //             expect.stringContaining('transaction.kind = :kind'),
        //             { kind: mockQueryDto.kind },
        //         );
        //         expect(
        //             mockQueryBuilder.andWhere.bind(mockQueryBuilder),
        //         ).toHaveBeenCalledWith(
        //             expect.stringContaining(
        //                 'transaction.createdAt >= :dateFrom',
        //             ),
        //             { dateFrom: mockQueryDto.dateFrom },
        //         );
        //         expect(
        //             mockQueryBuilder.andWhere.bind(mockQueryBuilder),
        //         ).toHaveBeenCalledWith(
        //             expect.stringContaining('transaction.createdAt <= :dateTo'),
        //             { dateTo: mockQueryDto.dateTo },
        //         );
        //         expect(
        //             mockQueryBuilder.andWhere.bind(mockQueryBuilder),
        //         ).toHaveBeenCalledWith(
        //             expect.stringContaining(
        //                 // eslint-disable-next-line max-len
        //                 'user.name LIKE :search OR user.email LIKE :search OR user.phone LIKE :search OR doctor.name LIKE :search OR doctor.email LIKE :search OR doctor.phone LIKE :search OR serviceProvider.name LIKE :search OR serviceProvider.email LIKE :search OR serviceProvider.phone LIKE :search OR transaction.id = :transactionId',
        //             ),
        //             expect.objectContaining({
        //                 search: `%${mockQueryDto.search}%`,
        //             }),
        //         );
        //         expect(
        //             mockQueryBuilder.orderBy.bind(mockQueryBuilder),
        //         ).toHaveBeenCalledWith(
        //             `transaction.${mockQueryDto.sortBy}`,
        //             mockQueryDto.sortOrder,
        //         );
        //     });
        //
        //     it.skip('should handle numeric search for transaction.id', () => {
        //         const numericSearchDto = { ...mockQueryDto, search: '123' };
        //         paymentService.buildGetPaymentsQuery(
        //             mockQueryBuilder,
        //             numericSearchDto,
        //         );
        //         expect(
        //             mockQueryBuilder.andWhere.bind(mockQueryBuilder),
        //         ).toHaveBeenCalledWith(
        //             expect.stringContaining('transaction.id = :transactionId'),
        //             expect.objectContaining({ transactionId: 123 }),
        //         );
        //     });
        // });
    });
});
