import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { BillsService } from './bills.service';

@Injectable()
export class CronBills {
    constructor(private billsService: BillsService) {}

    // @Cron(CronExpression.EVERY_MINUTE)
    @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
    async createBillForServiceProviders(): Promise<void> {
        await this.billsService.createBillForServiceProviders();
    }

    @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
    async createDonationBillForServiceProviders(): Promise<void> {
        await this.billsService.createDonationBillForServiceProviders();
    }

    // @Cron(CronExpression.EVERY_MINUTE)
    @Cron(CronExpression.EVERY_WEEK)
    async createBillForDoctors(): Promise<void> {
        await this.billsService.createBillForDoctors();
    }
}
