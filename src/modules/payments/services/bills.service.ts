/* eslint-disable complexity */
import {
    BadRequestException,
    forwardRef,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import * as moment from 'moment';
import { I18nService } from 'nestjs-i18n';
import { In, SelectQueryBuilder } from 'typeorm';

import { BillStatus } from '../../../common/constants/status';
import {
    ConsultationType,
    FilterByType,
    FilterType,
    PaymentMethod,
    RoleType,
    SortByType,
    TransactionKind,
} from '../../../common/constants/types';
import { CreateOperationsResponse } from '../../../common/dto/createOperationsResponse.dto';
import { PageOptionsDto } from '../../../common/dto/pageOptionsDto';
import { IHttpQuery } from '../../../interfaces/IHttpQuery';
import { DataDownloadService } from '../../../shared/services/data-download.service';
import { EmailService } from '../../../shared/services/email.service';
import { DebugLogger } from '../../../shared/services/logger.service';
import {
    jsPDFInvoiceTemplate,
    OutputType,
} from '../../../shared/templates/invoice.template';
import { SimpleEmailBuilder } from '../../../shared/utils/simple-email-builder';
import { AuthMessagesKeys } from '../../auth/translate.enum';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { DoctorsRepository } from '../../doctors/repositories/doctors.repository';
import { ServiceProvidersRepository } from '../../service-providers/repositories/service-providers.repository';
import { UserDetailsDto } from '../../users/dto/userDetails.dto';
import { BillsMapper } from '../bills.mapper';
import { BillDto } from '../dto/bill.dto';
import { BillsPageDto } from '../dto/bills-page.dto';
import { CreateBillTransactionDto } from '../dto/create-bill-transaction.dto';
import { Bill } from '../entities/bill.entity';
import { Transaction } from '../entities/transaction.entity';
import {
    BillTransactionMessagesKeys,
    PaymentMessagesKeys,
    TransactionStatus,
} from '../payment.enum';
import { BillTransactionRepository } from '../repositories/bill.transaction.repository';
import { BillsRepository } from '../repositories/bills.repository';
import { TransactionsRepository } from '../repositories/payments.repository';

@Injectable()
export class BillsService {
    constructor(
        private readonly billsRepository: BillsRepository,
        private readonly transactionsRepository: TransactionsRepository,
        private readonly billTransactionRepository: BillTransactionRepository,
        private readonly serviceProvidersRepository: ServiceProvidersRepository,
        private readonly doctorsRepository: DoctorsRepository,
        private readonly billsMapper: BillsMapper,
        private readonly i18n: I18nService,
        private readonly dataDownloadService: DataDownloadService,
        private readonly logger: DebugLogger,
        @Inject(forwardRef(() => EmailService))
        private readonly emailService: EmailService,
    ) { }

    async downloadBill(billId: number): Promise<{
        arBuffer: ArrayBuffer;
        enBuffer: ArrayBuffer;
        transaction: Transaction;
    }> {
        const transaction = await this.transactionsRepository.findOne({
            where: {
                bill: {
                    id: billId,
                },
            },
            relations: [
                'patient',
                'patient.user',
                'doctor',
                'doctor.user',
                'request',
                'bill',
            ],
        });

        if (!transaction) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang: 'en',
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        const subTotal =
            Number(transaction.amount) + Number(transaction.kashfPercentage);
        const total = subTotal + (10 / 100) * subTotal;

        const generatedEnPDF = await jsPDFInvoiceTemplate({
            outputType: OutputType.ARRAY_BUFFER,
            returnJsPDFDocObject: true,
            fileName: 'Invoice',
            orientationLandscape: false,
            compress: true,
            direction: 'ltr',
            logo: {
                src: 'https://admin.kashf.online/favicon.png',
                type: 'jpeg',
                width: 24,
                height: 24,
                margin: {
                    top: 0,
                    left: 0,
                },
            },
            business: {
                name: 'Kashf 247 for Medical Services LLC',
                // address:
                //     'Giza , 6 october, District 8, 6october library street unit 107 royal tours project',
                phone: 'WhatsApp +201020020087',
                email: '<EMAIL>',
                website: 'www.kashf247.com',
                // taxId: '***********',
            },
            contact: {
                label: 'Invoice issued for:',
                name: transaction.patient.user?.name ?? '',
                address: 'Cairo ,Egypt',
                phone: transaction.patient.user?.phone ?? '',
                email: transaction.patient.user?.email ?? '',
                otherInfo: '',
            },
            invoice: {
                label: 'Invoice #: ',
                num: transaction.bill.id,
                invDate: `Payment Date: ${moment(transaction.bill.end).format(
                    'DD-MM-YYYY HH:mm:ss',
                )}`,
                invGenDate: `Invoice Date: ${moment(
                    transaction.bill.start,
                ).format('DD-MM-YYYY HH:mm:ss')}`,
                headerBorder: true,
                tableBodyBorder: true,
                header: [
                    {
                        title: 'Doctor',
                        style: {
                            width: 80,
                        },
                    },
                    {
                        title: 'Patient',
                    },
                    { title: 'Request Type' },
                    { title: 'Price' },
                ],
                table: Array.from(Array(1), () => [
                    transaction.doctor.user.name ?? '',
                    transaction.patient.user?.name ?? '',
                    `${transaction.request instanceof ConsultationRequestEntity
                        ? transaction.request.type
                        : ''
                    }`,
                    String(transaction.amount),
                ]),
                additionalRows: [
                    {
                        col1: 'Total:',
                        col2: String(total),
                        col3: 'ALL',
                        style: {
                            fontSize: 14, // optional, default 12
                        },
                    },
                    // {
                    //     col1: 'VAT:',
                    //     col2: '10',
                    //     col3: '%',
                    //     style: {
                    //         fontSize: 10, // optional, default 12
                    //     },
                    // },
                    {
                        col1: 'SubTotal:',
                        col2: String(subTotal),
                        col3: 'ALL',
                        style: {
                            fontSize: 10, // optional, default 12
                        },
                    },
                ],

                invDescLabel: 'Invoice Note',
                invDesc:
                    '- We appreciate the opportunity to serve you.\n' +
                    '- Remember to quote the invoice number in all correspondences.\n' +
                    "- To have this invoice authorized, it is necessary for you to visit your doctor's office.",
            },
            footer: {
                text: 'The invoice is created on a computer and is valid without the signature and stamp.',
                address:
                    'Giza , 6 october, District 8, 6october library street unit 107 royal tours project',
                taxId: 'Tax Id :***********',
            },
            pageEnable: true,
            pageLabel: 'Page ',
        });

        const generatedArPDF = await jsPDFInvoiceTemplate({
            outputType: OutputType.ARRAY_BUFFER,
            returnJsPDFDocObject: true,
            fileName: 'Invoice',
            orientationLandscape: false,
            fontPath: './fonts/Amiri-Regular.ttf',
            compress: true,
            direction: 'rtl',
            logo: {
                src: 'https://admin.kashf.online/favicon.png',
                type: 'jpeg',
                width: 24,
                height: 24,
                margin: {
                    top: 0,
                    left: 0,
                },
            },
            business: {
                name: 'كشف 247 للخدمات الطبية ش ذ م م ',
                phone: 'WhatsApp +201020020087',
                email: '<EMAIL>',
                website: 'www.kashf247.com',
            },
            contact: {
                label: 'الفاتورة الصادرة ل :',
                name: transaction.patient.user?.name ?? '',
                address: 'القاهره , مصر',
                phone: transaction.patient.user?.phone ?? '',
                email: transaction.patient.user?.email ?? '',
                otherInfo: '',
            },
            invoice: {
                label: 'الفاتورة #: ',
                num: transaction.bill.id,
                invDate: `${moment(transaction.bill.end).format(
                    'DD-MM-YYYY HH:mm:ss',
                )}تاريخ الدفع:`,
                invGenDate: `${moment(transaction.bill.start).format(
                    'DD-MM-YYYY HH:mm:ss',
                )} تاريخ الفاتورة:`,
                headerBorder: true,
                tableBodyBorder: true,
                header: [
                    {
                        title: 'دكتور',
                        style: {
                            width: 80,
                        },
                    },
                    {
                        title: 'مريض',
                    },
                    { title: 'نوع الطلب' },
                    { title: 'السعر' },
                ],
                table: Array.from(Array(1), () => [
                    transaction.doctor.user.name ?? '',
                    transaction.patient.user?.name ?? '',
                    `${transaction.request instanceof ConsultationRequestEntity
                        ? transaction.request.type
                        : ''
                    }`,
                    String(transaction.amount),
                ]),
                additionalRows: [
                    {
                        col1: 'القيمه الكليه:',
                        col2: String(total),
                        col3: 'ALL',
                        style: {
                            fontSize: 14, // optional, default 12
                        },
                    },
                    // {
                    //     col1: 'ضرائب:',
                    //     col2: '10',
                    //     col3: '%',
                    //     style: {
                    //         fontSize: 10, // optional, default 12
                    //     },
                    // },
                    {
                        col1: 'قبل الضرائب:',
                        col2: String(subTotal),
                        col3: 'ALL',
                        style: {
                            fontSize: 10, // optional, default 12
                        },
                    },
                ],

                invDescLabel: ':ملاحظات الفاتورة',
                invDesc: `
            .نحن نقدر تلك الفرصة لكي نحقق اعلي جوده تنتظرها -
            .قد تخضع المدفوعات المتأخرة لرسوم إضافية وفقا للشروط والأحكام الخاصة بنا -
            .للحصول على ختم على هذه الفاتورة، يجب عليك زيارة مكتب طبيبك -
            `,
            },
            footer: {
                text: 'يتم إنشاء الفاتورة على جهاز كمبيوتر وهي صالحة بدون التوقيع والختم.',
                address:
                    'العنوان: الجيزة مدينة 6 اكتوبر,الحي 8 مشروع رويال تاورز شارع مكتبة جامعة 6 اكتوبر',
                taxId: 'السجل الضريبي: ***********',
            },
            pageEnable: true,
            pageLabel: 'Page ',
        });
        return {
            enBuffer: generatedEnPDF.arrayBuffer,
            arBuffer: generatedArPDF.arrayBuffer,
            transaction,
        };
    }

    async sendBillByMail(billId: number): Promise<void> {
        const { arBuffer, enBuffer, transaction } = await this.downloadBill(
            billId,
        );

        await this.emailService.sendEmailWithAttachmentAsBuffer(
            '<EMAIL>', // transaction.patient.user?.email,
            // '<EMAIL>',
            'Attachments',
            // .addLine('Thank you.')
            new SimpleEmailBuilder()
                .addLine(`Dear ${transaction.patient.user.name}`)
                .addBreak()
                // .addLine('a')
                .addBreak()
                .addBreak().content,

            [
                {
                    filename: 'فاتورة.pdf',
                    content: Buffer.from(arBuffer).toString('base64'),
                    encoding: 'base64',
                },
                {
                    filename: 'Invoice.pdf',
                    content: Buffer.from(enBuffer).toString('base64'),
                    encoding: 'base64',
                },
            ],
        );
    }

    async createBillForServiceProviders(): Promise<void> {
        const serviceProviders = await this.serviceProvidersRepository.find({
            where: {
                isActive: true,
            },
        });
        const serviceProvidersIds = serviceProviders.map(
            (serviceProvider) => serviceProvider.id,
        );
        const transactions = await this.transactionsRepository.find({
            where: {
                serviceProvider: {
                    id: In(serviceProvidersIds),
                },
                bill: null,
                status: TransactionStatus.SUCCEEDED,
                kind: TransactionKind.REQUEST_FEE,
            },
            relations: ['serviceProvider', 'request'],
        });
        serviceProviders.map(async (serviceProvider) => {
            const serviceProviderTransactions = transactions.filter(
                (transaction) =>
                    transaction.serviceProvider.id === serviceProvider.id,
            );
            const serviceProviderTransactionsIds =
                serviceProviderTransactions.map(
                    (serviceProviderTransaction) =>
                        serviceProviderTransaction.id,
                );

            const serviceProviderCashTransactions =
                serviceProviderTransactions.filter(
                    (doctorTransaction) =>
                        doctorTransaction.type === PaymentMethod.CASH,
                );

            const serviceProviderCreditTransactions =
                serviceProviderTransactions.filter(
                    (doctorTransaction) =>
                        doctorTransaction.type === PaymentMethod.CREDIT_CARD,
                );
            const serviceProviderClinicTransactions =
                serviceProviderTransactions.filter(
                    (serviceProviderTransaction) =>
                        serviceProviderTransaction.request instanceof
                        ConsultationRequestEntity &&
                        serviceProviderTransaction.request.type ===
                        ConsultationType.CLINIC,
                );
            const serviceProviderHomeVisitTransactions =
                serviceProviderTransactions.filter(
                    (serviceProviderTransaction) =>
                        serviceProviderTransaction.request instanceof
                        ConsultationRequestEntity &&
                        serviceProviderTransaction.request.type ===
                        ConsultationType.HOME_VISIT,
                );
            const serviceProviderOnlineConsulationTransactions =
                serviceProviderTransactions.filter(
                    (serviceProviderTransaction) =>
                        serviceProviderTransaction.request instanceof
                        ConsultationRequestEntity &&
                        serviceProviderTransaction.request.type ===
                        ConsultationType.ONLINE_CONSULTATION,
                );
            const grossRevenue = serviceProviderTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const onlineConsulationRevenue =
                serviceProviderOnlineConsulationTransactions.reduce(
                    (sum: number, transaction) =>
                        Number(sum) + Number(transaction.amount),
                    0,
                );
            const homeVisitRevenue =
                serviceProviderHomeVisitTransactions.reduce(
                    (sum: number, transaction) =>
                        Number(sum) + Number(transaction.amount),
                    0,
                );
            const clinicVisitRevenue = serviceProviderClinicTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );

            const totalCash = serviceProviderCashTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const totalCredit = serviceProviderCreditTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const netRevenue =
                grossRevenue -
                (grossRevenue * serviceProvider.kashfPercentage) / 100;
            if (serviceProviderTransactions.length) {
                const billToSave = this.billsRepository.create({
                    clinicVisitRevenue,
                    grossRevenue,
                    totalCash,
                    totalCredit,
                    homeVisitRevenue,
                    onlineConsulationRevenue,
                    netRevenue,
                    serviceProvider,
                    transactions: serviceProviderTransactions,
                    kashfPercentage: serviceProvider.kashfPercentage,
                    status: BillStatus.NOT_SETTLED,
                    start: moment()
                        .subtract(1, 'months')
                        .startOf('month')
                        .toDate(),
                    end: moment().subtract(1, 'months').endOf('month').toDate(),
                    total: grossRevenue - totalCash,
                });
                const createdBill = await this.billsRepository.save(billToSave);
                await this.transactionsRepository.update(
                    { id: In(serviceProviderTransactionsIds) },
                    { bill: createdBill },
                );
            }
        });
    }

    async createDonationBillForServiceProviders(): Promise<void> {
        const serviceProviders = await this.serviceProvidersRepository.find({
            where: {
                isActive: true,
            },
        });
        const serviceProvidersIds = serviceProviders.map(
            (serviceProvider) => serviceProvider.id,
        );
        this.logger.log(JSON.stringify(serviceProvidersIds));
        const transactions = await this.transactionsRepository.find({
            where: {
                serviceProvider: {
                    id: In(serviceProvidersIds),
                },
                bill: null,
                status: TransactionStatus.INITIATED,
                kind: TransactionKind.DONATION_FEE,
            },
            relations: ['serviceProvider', 'request'],
        });
        serviceProviders.map(async (serviceProvider) => {
            const serviceProviderTransactions = transactions.filter(
                (transaction) =>
                    transaction.serviceProvider.id === serviceProvider.id,
            );
            const serviceProviderTransactionsIds =
                serviceProviderTransactions.map(
                    (serviceProviderTransaction) =>
                        serviceProviderTransaction.id,
                );
            this.logger.log(JSON.stringify(serviceProviderTransactionsIds));

            const serviceProviderCreditTransactions =
                serviceProviderTransactions.filter(
                    (doctorTransaction) =>
                        doctorTransaction.type === PaymentMethod.CREDIT_CARD,
                );
            const grossRevenue = serviceProviderTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const onlineConsulationRevenue = 0;
            const homeVisitRevenue = 0;
            const clinicVisitRevenue = 0;
            const totalCash = 0;
            const totalCredit = serviceProviderCreditTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            this.logger.log(`totalCredit=>${totalCredit}`);
            const netRevenue =
                grossRevenue -
                (grossRevenue * serviceProvider.kashfDonationPercentage) / 100;
            if (serviceProviderTransactions.length) {
                const billToSave = this.billsRepository.create({
                    clinicVisitRevenue,
                    grossRevenue,
                    totalCash,
                    totalCredit,
                    homeVisitRevenue,
                    onlineConsulationRevenue,
                    netRevenue,
                    serviceProvider,
                    isDonation: true,
                    transactions: serviceProviderTransactions,
                    kashfPercentage: serviceProvider.kashfPercentage,
                    status: BillStatus.NOT_SETTLED,
                    start: moment()
                        .subtract(1, 'months')
                        .startOf('month')
                        .toDate(),
                    end: moment().subtract(1, 'months').endOf('month').toDate(),
                    total: grossRevenue - totalCredit,
                });
                const createdBill = await this.billsRepository.save(billToSave);

                this.logger.log(createdBill);
                await this.transactionsRepository.update(
                    { id: In(serviceProviderTransactionsIds) },
                    { bill: createdBill },
                );
            }
        });
    }

    async createBillForDoctors(): Promise<void> {
        const doctors = await this.doctorsRepository.find({
            where: {
                isActive: true,
            },
        });
        const doctorsIds = doctors.map((doctor) => doctor.id);
        const transactions = await this.transactionsRepository.find({
            where: {
                doctor: {
                    id: In(doctorsIds),
                },
                serviceProvider: null,
                bill: null,
                status: TransactionStatus.SUCCEEDED,
                kind: TransactionKind.REQUEST_FEE,
            },
            relations: ['doctor', 'request'],
        });
        doctors.map(async (doctor) => {
            const doctorTransactions = transactions.filter(
                (transaction) => transaction.doctor.id === doctor.id,
            );
            const doctorTransactionsIds = doctorTransactions.map(
                (doctorTransaction) => doctorTransaction.id,
            );

            const doctorCashTransactions = doctorTransactions.filter(
                (doctorTransaction) =>
                    doctorTransaction.type === PaymentMethod.CASH,
            );

            const doctorCreditTransactions = doctorTransactions.filter(
                (doctorTransaction) =>
                    doctorTransaction.type === PaymentMethod.CREDIT_CARD,
            );
            const doctorClinicTransactions = doctorTransactions.filter(
                (doctorTransaction) =>
                    doctorTransaction.request instanceof
                    ConsultationRequestEntity &&
                    doctorTransaction.request.type === ConsultationType.CLINIC,
            );
            const doctorHomeVisitTransactions = doctorTransactions.filter(
                (doctorTransaction) =>
                    doctorTransaction.request instanceof
                    ConsultationRequestEntity &&
                    doctorTransaction.request.type ===
                    ConsultationType.HOME_VISIT,
            );
            const doctorOnlineConsulationTransactions =
                doctorTransactions.filter(
                    (doctorTransaction) =>
                        doctorTransaction.request instanceof
                        ConsultationRequestEntity &&
                        doctorTransaction.request.type ===
                        ConsultationType.ONLINE_CONSULTATION,
                );
            const grossRevenue = doctorTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const totalCash = doctorCashTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const totalCredit = doctorCreditTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const onlineConsulationRevenue =
                doctorOnlineConsulationTransactions.reduce(
                    (sum: number, transaction) =>
                        Number(sum) + Number(transaction.amount),
                    0,
                );

            const homeVisitRevenue = doctorHomeVisitTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const clinicVisitRevenue = doctorClinicTransactions.reduce(
                (sum: number, transaction) =>
                    Number(sum) + Number(transaction.amount),
                0,
            );
            const netRevenue =
                grossRevenue - (grossRevenue * doctor.kashfPercentage) / 100;
            if (doctorTransactions.length) {
                const billToSave = this.billsRepository.create({
                    clinicVisitRevenue,
                    grossRevenue,
                    totalCash,
                    totalCredit,
                    homeVisitRevenue,
                    onlineConsulationRevenue,
                    netRevenue,
                    doctor,
                    transactions: doctorTransactions,
                    kashfPercentage: doctor.kashfPercentage,
                    status: BillStatus.NOT_SETTLED,
                    start: moment()
                        .subtract(1, 'week')
                        .startOf('week')
                        .toDate(),
                    end: moment().subtract(1, 'week').endOf('week').toDate(),
                    total: grossRevenue - totalCash,
                });
                const createdBill = await this.billsRepository.save(billToSave);
                await this.transactionsRepository.update(
                    { id: In(doctorTransactionsIds) },
                    { bill: createdBill },
                );
                doctor.walletBalance += netRevenue;
                await this.doctorsRepository.update(
                    {
                        id: doctor.id,
                    },
                    { walletBalance: doctor.walletBalance },
                );
            }
        });
    }

    async downloadBills(
        providerId: string,
        httpQueryString: string,
        user: UserDetailsDto,
        lang: string,
        isDonation: boolean,
    ): Promise<string> {
        let query = this.billsRepository
            .createQueryBuilder('bill')
            .leftJoinAndSelect('bill.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'user')
            .leftJoinAndSelect(
                'serviceProvider.serviceProviderType',
                'serviceProviderType',
            )
            .leftJoinAndSelect(
                'serviceProviderType.translations',
                'serviceProviderTypeTranslations',
                'serviceProviderTypeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('bill.doctor', 'doctor')
            .select([
                'bill.id',
                'bill.status',
                'bill.isDonation',
                'bill.start',
                'bill.end',
                'bill.kashfPercentage',
                'bill.onlineConsulationRevenue',
                'bill.homeVisitRevenue',
                'bill.clinicVisitRevenue',
                'bill.grossRevenue',
                'bill.netRevenue',
                'doctor.id',
                'serviceProvider.id',
                'user.id',
                'user.name',
                'user.phone',
                'user.email',
                'serviceProviderType.id',
                'serviceProviderTypeTranslations.id',
                'serviceProviderTypeTranslations.title',
            ])
            .where('bill.isDonation = :isDonation', {
                isDonation,
            });

        if (httpQueryString) {
            query = this.buildGetBillsQuery(query, httpQueryString);
        }
        if (user && user.role === RoleType.SERVICE_PROVIDER) {
            providerId = user.serviceProvider.id.toString();
        }
        if (providerId) {
            query.andWhere('serviceProvider.id = :serviceProviderId', {
                serviceProviderId: parseInt(providerId, 10),
            });
        }
        const bills = await query.getMany();
        const billsDto = bills.map((bill) =>
            this.billsMapper.fromEntityToDTO(BillDto, bill),
        );

        const fields = [
            'id',
            'status',
            'start',
            'end',
            'onlineConsulationRevenue',
            'homeVisitRevenue',
            'clinicVisitRevenue',
            'grossRevenue',
            'kashfPercentage',
            'netRevenue',
        ];
        return this.dataDownloadService.downloadCsv(fields, billsDto);
    }

    async downloadBillsForAdmin(
        providerId: string,
        httpQueryString: string,
        lang: string,
        isDonation: boolean,
    ): Promise<string> {
        let query = this.billsRepository
            .createQueryBuilder('bill')
            .leftJoinAndSelect('bill.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'user')
            .leftJoinAndSelect(
                'user.translations',
                'userTranslations',
                'userTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect(
                'serviceProvider.serviceProviderType',
                'serviceProviderType',
            )
            .leftJoinAndSelect(
                'serviceProviderType.translations',
                'serviceProviderTypeTranslations',
                'serviceProviderTypeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('bill.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .select([
                'bill.id',
                'bill.status',
                'bill.isDonation',
                'bill.start',
                'bill.end',
                'bill.kashfPercentage',
                'bill.onlineConsulationRevenue',
                'bill.homeVisitRevenue',
                'bill.clinicVisitRevenue',
                'bill.grossRevenue',
                'bill.netRevenue',
                'doctor.id',
                'serviceProvider.id',
                'user.id',
                'user.name',
                'user.email',
                'userTranslations.name',
                'user.phone',
                'doctorUser.id',
                'doctorUser.name',
                'doctorUser.email',
                'doctorUser.phone',
                'serviceProviderType.id',
                'serviceProviderTypeTranslations.id',
                'serviceProviderTypeTranslations.title',
            ])
            .where('bill.isDonation = :isDonation', {
                isDonation,
            });

        if (httpQueryString) {
            query = this.buildGetBillsQuery(query, httpQueryString);
        }
        if (providerId) {
            query.andWhere('serviceProvider.id = :serviceProviderId', {
                serviceProviderId: providerId,
            });
        }
        const bills = await query.getMany();
        let fields = [];
        const billsDto = bills.map((bill: any) => {
            if (bill.serviceProvider) {
                fields = [
                    'id',
                    'dueDate',
                    'serviceProviderName',
                    'type',
                    'email',
                    'mobileNumber',
                    'balance',
                    'status',
                ];
                return {
                    id: bill.id,
                    dueDate: bill.end,
                    serviceProviderName:
                        bill.serviceProvider.user.translations[0].name,
                    type: bill.serviceProvider.serviceProviderType
                        .translations[0].title,
                    email: bill.serviceProvider.user.email,
                    mobileNumber: bill.serviceProvider.user.phone,
                    balance: bill.netRevenue,
                    status: bill.status,
                };
            }
            fields = [
                'id',
                'dueDate',
                'doctorName',
                'email',
                'mobileNumber',
                'balance',
                'status',
            ];
            return {
                id: bill.id,
                dueDate: bill.end,
                doctorName: bill.doctor.user.name,
                email: bill.doctor.user.email,
                mobileNumber: bill.doctor.user.phone,
                balance: bill.netRevenue,
                status: bill.status,
            };
        });

        return this.dataDownloadService.downloadCsv(fields, billsDto);
    }

    async getBills(
        httpQueryString: string,
        pageOptionsDto: PageOptionsDto,
        user: UserDetailsDto,
        lang: string,
        isDonation: boolean,
    ): Promise<BillsPageDto> {
        let query = this.billsRepository
            .createQueryBuilder('bill')
            .leftJoinAndSelect('bill.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'user')
            .leftJoinAndSelect(
                'user.translations',
                'userTranslations',
                'userTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect(
                'serviceProvider.serviceProviderType',
                'serviceProviderType',
            )
            .leftJoinAndSelect(
                'serviceProviderType.translations',
                'serviceProviderTypeTranslations',
                'serviceProviderTypeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('bill.doctor', 'doctor')
            .leftJoinAndSelect('bill.billTransaction', 'transaction')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .select([
                'bill.id',
                'bill.status',
                'bill.isDonation',
                'bill.start',
                'bill.end',
                'bill.kashfPercentage',
                'bill.onlineConsulationRevenue',
                'bill.homeVisitRevenue',
                'bill.clinicVisitRevenue',
                'transaction.bankName',
                'bill.grossRevenue',
                'bill.netRevenue',
                'doctor.id',
                'userTranslations.name',
                'serviceProvider.id',
                'user.id',
                'user.name',
                'user.email',
                'user.phone',
                'doctorUser.id',
                'doctorUser.name',
                'doctorUser.email',
                'doctorUser.phone',
                'serviceProviderType.id',
                'serviceProviderTypeTranslations.id',
                'serviceProviderTypeTranslations.title',
            ])
            .where('bill.isDonation = :isDonation', {
                isDonation,
            });

        if (httpQueryString) {
            query = this.buildGetBillsQuery(query, httpQueryString);
        }
        if (user && user.role === RoleType.SERVICE_PROVIDER) {
            query.andWhere('serviceProvider.id = :serviceProviderId', {
                serviceProviderId: user.serviceProvider.id,
            });
        }
        const [bills, pageMetaDto] = await query.paginate(pageOptionsDto);
        let billsDto: any = bills.map((bill) =>
            this.billsMapper.fromEntityToDTO(BillDto, bill),
        );
        if (
            user &&
            (user.role === RoleType.ADMIN || user.role === RoleType.SUPER_ADMIN)
        ) {
            billsDto = bills.map((bill: any) => {
                if (bill.serviceProvider) {
                    return {
                        id: bill.id,
                        start: bill.start,
                        end: bill.end,
                        serviceProviderName:
                            bill.serviceProvider.user.translations[0].name,
                        type: bill.serviceProvider.serviceProviderType
                            .translations[0].title,
                        email: bill.serviceProvider.user.email,
                        mobileNumber: bill.serviceProvider.user.phone,
                        doctorName:
                            bill.doctor && bill.doctor.user
                                ? bill.doctor.user.name
                                : bill.serviceProvider.user.translations[0]
                                    .name,
                        serviceProviderId: bill.serviceProvider.id,
                        kashfPercentage: bill.kashfPercentage,
                        doctorId: bill.doctor?.id || -1,
                        balance: bill.netRevenue,
                        status: bill.status,
                    };
                }
                return {
                    id: bill.id,
                    start: bill.start,
                    dueDate: bill.end,
                    doctorName: bill.doctor.user.name,
                    email: bill.doctor.user.email,
                    mobileNumber: bill.doctor.user.phone,
                    balance: bill.netRevenue,
                    doctorId: bill.doctor.id,
                    status: bill.status,
                };
            });
        }
        return new BillsPageDto(billsDto, pageMetaDto);
    }

    buildGetBillsQuery(
        query: SelectQueryBuilder<Bill>,
        httpQueryString: string,
    ): SelectQueryBuilder<Bill> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }
        if (httpQueryObject.search) {
            query.andWhere(
                '(doctorUser.name || doctorUser.email || user.phone || bill.id || bill.netRevenue || userTranslations.name || serviceProviderTypeTranslations.title) ILIKE :searchKey',
                {
                    searchKey: `%${httpQueryObject.search.value.trim()}%`,
                },
            );
        }

        if (httpQueryObject.filters) {
            httpQueryObject.filters.forEach((filter) => {
                switch (filter.type) {
                    case FilterType.FIXED:
                        switch (filter.by) {
                            case FilterByType.SERVICE_PROVIDER:
                                query.andWhere(
                                    'serviceProvider.id = :serviceProviderId',
                                    {
                                        serviceProviderId: parseInt(
                                            filter.value,
                                            10,
                                        ),
                                    },
                                );
                                break;
                            case FilterByType.TYPE:
                                if (filter.value === 'service_providers') {
                                    query.andWhere(
                                        'serviceProvider.id IS NOT NULL',
                                    );
                                }
                                break;
                            case FilterByType.PAYMENT_METHOD:
                                if (filter.value === 'bankAccount') {
                                    query.andWhere(
                                        'transaction.bankName IS NOT NULL',
                                    );
                                } else {
                                    query.andWhere(
                                        'transaction.bankName IS NULL',
                                    );
                                }

                                break;
                            case FilterByType.SERVICE_PROVIDER_TYPE:
                                query.andWhere(
                                    'serviceProviderType.id = :serviceProviderTypeId',
                                    {
                                        serviceProviderTypeId: parseInt(
                                            filter.value,
                                            10,
                                        ),
                                    },
                                );
                                break;
                            case FilterByType.DOCTOR:
                                query.andWhere(
                                    'doctor.id = :doctorId AND serviceProvider.id IS NULL',
                                    {
                                        doctorId: filter.value,
                                    },
                                );
                        }
                        break;
                    case FilterType.RANGE:
                        switch (filter.by) {
                            case FilterByType.DATE:
                                query.andWhere(
                                    'bill.end between :min AND :max',
                                    {
                                        min: filter.min,
                                        max: filter.max,
                                    },
                                );
                        }
                }
            });
        }
        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.NAME:
                    query.orderBy('doctorUser.name', httpQueryObject.sort.type);
                    break;
                case SortByType.DATE:
                    query.orderBy('bill.end', httpQueryObject.sort.type);
                    break;
                case SortByType.SERVICE_PROVIDER_NAME:
                    query.orderBy(
                        'userTranslations.name',
                        httpQueryObject.sort.type,
                    );
                    break;
                case SortByType.SERVICE_PROVIDER_TYPE:
                    query.orderBy(
                        'serviceProviderTypeTranslations.title',
                        httpQueryObject.sort.type,
                    );
            }
        }

        return query;
    }

    async createBillTransaction(
        createBillTransactionDto: CreateBillTransactionDto,
        user: UserDetailsDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const query = this.billsRepository
            .createQueryBuilder('bill')
            .leftJoinAndSelect('bill.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('bill.doctor', 'doctor')
            .select(['bill.id', 'serviceProvider.id'])
            .where('bill.id = :id', { id: createBillTransactionDto.billId });

        if (user.role === RoleType.SERVICE_PROVIDER) {
            query.andWhere('serviceProvider.id = :serviceProviderId', {
                serviceProviderId: user.serviceProvider.id,
            });
        }
        const bill = await query.getOne();
        if (!bill) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        PaymentMessagesKeys.PAYMENT_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const billTransaction = this.billTransactionRepository.create({
            bill,
            date: createBillTransactionDto.date,
            receipt: createBillTransactionDto.receipt,
            transactionId: createBillTransactionDto.transactionId,
            bankName: createBillTransactionDto.bankName,
        });
        const createdBillTransaction =
            await this.billTransactionRepository.save(billTransaction);
        await this.billsRepository.update(
            { id: createBillTransactionDto.billId },
            { status: BillStatus.SETTLED },
        );

        if (bill.doctor) {
            bill.doctor.walletBalance -= bill.netRevenue;
            await this.doctorsRepository.update(
                { id: bill.doctor.id },
                { walletBalance: bill.doctor.walletBalance },
            );
        }

        return {
            createdId: createdBillTransaction.id,
            isSuccessful: true,
            message: await this.i18n.translate(
                BillTransactionMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async getDoctorsWalletBalance(
        doctorId: number,
        lang: string,
    ): Promise<BillDto[]> {
        const doctor = await this.doctorsRepository.findOne({
            where: {
                id: doctorId,
            },
        });
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const bills = await this.billsRepository
            .createQueryBuilder('bill')
            .select([
                'bill.id',
                'bill.onlineConsulationRevenue',
                'bill.homeVisitRevenue',
                'bill.clinicVisitRevenue',
                'bill.netRevenue',
                'bill.createdAt',
            ])
            .where('bill.doctor_id = :doctorId', {
                doctorId,
            })
            .andWhere('bill.status = :status', {
                status: BillStatus.NOT_SETTLED,
            })
            .getMany();

        return bills.map((bill) =>
            this.billsMapper.fromEntityToDTO(BillDto, bill),
        );
    }
}
