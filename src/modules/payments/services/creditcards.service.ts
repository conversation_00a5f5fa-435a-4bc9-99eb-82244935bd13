import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { I18nService } from 'nestjs-i18n';

import { BasicOperationsResponse } from '../../../common/dto/basicOperationsResponse.dto';
import { UserEntity } from '../../users/entities/user.entity';
import { GetCardsDto } from '../dto/get-cards.dto';
import { CreditCard } from '../entities/creditcard.entity';
import { CreditCardMessagesKeys } from '../payment.enum';
import { CreditCardsRepository } from '../repositories/creditcards.repository';

@Injectable()
export class CreditCardsService {
    constructor(
        private readonly creditCardsRepository: CreditCardsRepository,
        private readonly i18n: I18nService,
    ) { }

    async addCard(
        userId: number,
        cardNumber: string,
        token: string,
        cardType: string,
    ): Promise<number> {
        const card = await this.findCard(userId, cardNumber);
        if (card) {
            await this.updateCardToken(card.id, token);
            return card.id;
        }

        const user = new UserEntity();
        user.id = userId;

        const creditCardEntity = this.creditCardsRepository.create({
            user,
            cardNumber,
            token,
            cardType,
        });
        const createdCard = await this.creditCardsRepository.save(
            creditCardEntity,
        );
        return createdCard.id;
    }
    async updateCardToken(id: number, token: string): Promise<void> {
        await this.creditCardsRepository.update({ id }, { token });
    }
    async findCard(userId: number, cardNumber: string): Promise<CreditCard> {
        return this.creditCardsRepository.findOne({
            where: {
                cardNumber,
                user: { id: userId },
            }
        });
    }

    async getAllCard(userId: number): Promise<GetCardsDto[]> {
        const cards = await this.creditCardsRepository.find({
            where: { user: { id: userId } },
            select: ['cardNumber', 'id', 'cardType'],
        });
        return cards.map((card) => plainToClass(GetCardsDto, card));
    }
    async removeCard(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const creditCard = await this.creditCardsRepository.findOne({
            where: { id },
        });

        if (!creditCard) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        CreditCardMessagesKeys.CARD_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        await this.creditCardsRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                CreditCardMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }
}
