import {
    BadRequestException,
    forwardRef,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { SelectQueryBuilder } from 'typeorm';

import {
    ConsultationRequestStatus,
    PaymentStatus,
} from '../../../common/constants/status';
import {
    CallBackType,
    FilterByType,
    FilterType,
    PaymentMethod,
    RoleType,
    SortByType,
    TransactionKind,
} from '../../../common/constants/types';
import { BasicOperationsResponse } from '../../../common/dto/basicOperationsResponse.dto';
import { PageOptionsDto } from '../../../common/dto/pageOptionsDto';
import { ConfigService } from '../../../config/config.service';
import { AvailableLanguageCodes } from '../../../i18n/languageCodes';
import { IHttpQuery } from '../../../interfaces/IHttpQuery';
import { DataDownloadService } from '../../../shared/services/data-download.service';
import { DebugLogger } from '../../../shared/services/logger.service';
import { PaymobService } from '../../../shared/services/paymob.service';
import { BookingsService } from '../../booking/booking.service';
import { ConsultationRequestsService } from '../../consultation-requests/consultation-requests.service';
import { DoctorsService } from '../../doctors/doctors.service';
import { NotificationTypes } from '../../doctors/notifications.enum';
import { CreateNotificationDto } from '../../notifications/dto/create-notification.dto';
import { NotificationsService } from '../../notifications/notifications.service';
import { UserDetailsDto } from '../../users/dto/userDetails.dto';
import { UserEntity } from '../../users/entities/user.entity';
import { UsersService } from '../../users/users.service';
import { CreateDonationUrlDto } from '../dto/create-donation-url.dto';
import { PaymentDto } from '../dto/payment.dto';
import { PaymentsPageDto } from '../dto/payments-page.dto';
import { Transaction } from '../entities/transaction.entity';
import {
    IPaymobTransactionCallBack,
    PaymentMessagesKeys,
    TransactionStatus,
} from '../payment.enum';
import { PaymentsMapper } from '../payments.mapper';
import { TransactionsRepository } from '../repositories/payments.repository';
import { CreditCardsService } from './creditcards.service';
import { PaymobTransactionsService } from './paymob.transactions.service';
import { TransactionsService } from './transactions.service';
import { IPaymobProcessingCallbackPayload } from '../interfaces/i-paymob-processing-callback-payload';
import { tryCatch } from '../../../utils/helpers';
import { ConsultationRequestMessagesKeys } from '../../consultation-requests/notification.enum';
import { AuthMessagesKeys } from '../../auth/translate.enum';

@Injectable()
export class PaymentsService {
    constructor(
        private readonly transactionsRepository: TransactionsRepository,
        private readonly userService: UsersService,
        private readonly paymobService: PaymobService,
        private readonly configService: ConfigService,
        @Inject(forwardRef(() => ConsultationRequestsService))
        private readonly consultationRequestsService: ConsultationRequestsService,
        @Inject(forwardRef(() => DoctorsService))
        private readonly doctorsService: DoctorsService,
        private readonly bookingsService: BookingsService,
        private readonly transactionsService: TransactionsService,
        private readonly paymobTransactionsService: PaymobTransactionsService,
        private readonly i18n: I18nService,
        private readonly creditCardsService: CreditCardsService,
        private readonly paymentsMapper: PaymentsMapper,
        private readonly dataDownloadService: DataDownloadService,
        private readonly notificationsService: NotificationsService,
        private readonly logger: DebugLogger,
    ) {
    }

    async createPaymentURL(
        userId: number,
        requestId: number,
        kind: TransactionKind,
        lang: string,
    ): Promise<string> {
        const request = await this.consultationRequestsService.getRequest(
            requestId,
            lang,
        );

        if (!request) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ConsultationRequestMessagesKeys.REQUEST_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.FORBIDDEN,
            );
        }

        let amount: number;

        if (kind === TransactionKind.REQUEST_FEE) {
            amount = Number(request.fees) - Number(request.discountAmount);
            const canCreateTransaction = [
                ConsultationRequestStatus.ACCEPTED,
                ConsultationRequestStatus.PENDINGPAYMENT,
            ].includes(request.status);
            if (!canCreateTransaction) {
                const translationResult = await tryCatch(
                    this.i18n.translate(
                        PaymentMessagesKeys.REQUEST_NOT_ACCEPTED,
                        {
                            lang,
                        },
                    ),
                );
                if (!translationResult.isSuccess) {
                    this.logger.error(
                        `Error translating message: ${translationResult.error.message}`,
                    );
                }
                throw new HttpException(
                    translationResult.value as string,
                    HttpStatus.BAD_REQUEST,
                );
            }
        } else if (kind === TransactionKind.KASHF_FEE) {
            amount =
                (request.fees - request.discountAmount) *
                (request.kashfPercentage / 100);
        }

        if (amount <= 1) {
            const translationResult = await tryCatch(
                this.i18n.translate(
                    PaymentMessagesKeys.REQUEST_AMOUNT_SHOULD_BE_GREATER_THAN_ZERO,
                    {
                        lang,
                    },
                ),
            );
            if (!translationResult.isSuccess) {
                this.logger.error(
                    `Error translating message: ${translationResult.error.message}`,
                );
            }
            throw new HttpException(
                translationResult.value as string,
                HttpStatus.BAD_REQUEST,
            );
        }

        const totalAmount = Math.ceil(amount * 100).toString();
        const user = await this.userService.findOneById(userId, lang);

        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.FORBIDDEN,
            );
        }
        const { doctor, patient, paymentMethod } = request;

        if (
            kind === TransactionKind.REQUEST_FEE &&
            request.paymentStatus === PaymentStatus.PAID
        ) {
            const translationResult = await tryCatch(
                this.i18n.translate(
                    PaymentMessagesKeys.PAYMENT_ALREADY_SUCCEDED,
                    {
                        lang,
                    },
                ),
            );
            if (!translationResult.isSuccess) {
                this.logger.error(
                    `Error translating message: ${translationResult.error.message}`,
                );
            }
            throw new HttpException(
                translationResult.value as string,
                HttpStatus.BAD_REQUEST,
            );
        }
        if (
            (kind === TransactionKind.REQUEST_FEE &&
                request.status === ConsultationRequestStatus.PENDINGPAYMENT) ||
            (kind === TransactionKind.KASHF_FEE && !request.isKashfFeePaid)
        ) {
            await this.transactionsRepository.delete({
                kind,
                request: { id: request.id },
            });
        }

        const transactionId = await this.transactionsService.createTransaction(
            user.id,
            patient.id,
            doctor.id,
            request.id,
            kind === TransactionKind.REQUEST_FEE
                ? paymentMethod
                : PaymentMethod.CREDIT_CARD,
            amount,
            TransactionStatus.INITIATED,
            request.serviceProvider ? request.serviceProvider.id : null,
            request.kashfPercentage,
            kind,
        );

        if (kind === TransactionKind.REQUEST_FEE) {
            await this.consultationRequestsService.updateRequestStatusToPendingPayment(
                request.id,
                transactionId,
            );
        }

        const orderId = await this.paymobService.createOrder(totalAmount, []);
        await this.paymobTransactionsService.createPayMobTransaction(
            transactionId,
            orderId,
            null,
            TransactionStatus.INITIATED,
            null,
        );
        const paymentToken = await this.paymobService.getPaymentKey(
            orderId,
            totalAmount,
            user.email,
            user.name,
            user.phone,
        );
        return this.paymobService.generateUrl(paymentToken);
    }

    async createDonationPaymentUrl(
        createDonationUrlDto: CreateDonationUrlDto,
        lang: string,
    ): Promise<string> {
        // await this.serviceProviderService.isAcceptingDonation(
        //     createDonationUrlDto.serviceProviderId,
        //     lang,
        // );

        let user = new UserEntity();
        if (createDonationUrlDto.email) {
            user.email = createDonationUrlDto.email;
        }

        if (createDonationUrlDto.phone) {
            user.phone = createDonationUrlDto.phone;
        }

        if (createDonationUrlDto.name) {
            user.name = createDonationUrlDto.name;
        }

        if (createDonationUrlDto.userId) {
            user = await this.userService.findUser(
                createDonationUrlDto.userId,
                ['id', 'email', 'name', 'phone'],
                lang,
            );
        }

        const totalAmount = createDonationUrlDto.amount * 100;

        const transaction =
            await this.transactionsService.createDonationTransaction(
                user.id,
                PaymentMethod.CREDIT_CARD,
                totalAmount,
                TransactionStatus.INITIATED,
                createDonationUrlDto.serviceProviderId,
                TransactionKind.DONATION_FEE,
            );

        const orderId = await this.paymobService.createOrder(
            totalAmount.toString(),
            [],
        );

        await this.paymobTransactionsService.createPayMobTransaction(
            transaction.id,
            orderId,
            null,
            TransactionStatus.INITIATED,
            null,
        );

        const paymentToken = await this.paymobService.getPaymentKey(
            orderId,
            totalAmount.toString(),
            user.email,
            user.name,
            user.phone,
        );

        return this.paymobService.generateUrl(paymentToken);
    }

    async createBookingPaymentUrl(bookingId: number, lang: string) {
        const booking = await this.bookingsService.findOne(bookingId, lang);

        const totalAmount = booking.package.price * 100;

        const transactionId =
            await this.transactionsService.createTransactionForRequest(
                totalAmount,
                booking.user,
                booking,
                booking.package.serviceProvider,
                booking.package.doctor,
            );

        await this.bookingsService.updateStatusToPendingPayment(
            booking.id,
            transactionId,
        );

        const orderId = await this.paymobService.createOrder(
            totalAmount.toString(),
            [],
        );

        await this.paymobTransactionsService.createPayMobTransaction(
            transactionId,
            orderId,
            null,
            TransactionStatus.INITIATED,
            null,
        );

        const paymentToken = await this.paymobService.getPaymentKey(
            orderId,
            totalAmount.toString(),
            booking.user.email,
            booking.user.name,
            booking.user.phone,
        );

        return this.paymobService.generateUrl(paymentToken);
    }

    async payWithSavedToken(
        userId: number,
        cardId: number,
        requestId: number,
        kind: TransactionKind,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const request = await this.consultationRequestsService.getRequest(
            requestId,
            lang,
        );
        let amount: number;
        if (kind === TransactionKind.REQUEST_FEE) {
            amount = request.fees - request.discountAmount;
            const canCreateTransaction = [
                ConsultationRequestStatus.ACCEPTED,
                ConsultationRequestStatus.PENDINGPAYMENT,
            ].includes(request.status);
            if (!canCreateTransaction) {
                const translationResult = await tryCatch(
                    this.i18n.translate(
                        PaymentMessagesKeys.REQUEST_NOT_ACCEPTED,
                        {
                            lang,
                        },
                    ),
                );
                if (!translationResult.isSuccess) {
                    this.logger.error(
                        `Error translating message: ${translationResult.error.message}`,
                    );
                }
                throw new HttpException(
                    translationResult.value as string,
                    HttpStatus.BAD_REQUEST,
                );
            }
        } else if (kind === TransactionKind.KASHF_FEE) {
            amount =
                (request.fees - request.discountAmount) *
                (request.kashfPercentage / 100);
        }

        if (amount <= 1) {
            const translationResult = await tryCatch(
                this.i18n.translate(
                    PaymentMessagesKeys.REQUEST_AMOUNT_SHOULD_BE_GREATER_THAN_ZERO,
                    {
                        lang,
                    },
                ),
            );
            if (!translationResult.isSuccess) {
                this.logger.error(
                    `Error translating message: ${translationResult.error.message}`,
                );
            }
            throw new HttpException(
                translationResult.value as string,
                HttpStatus.BAD_REQUEST,
            );
        }

        const totalAmount = Math.ceil(amount * 100).toString();
        const user = await this.userService.findOneById(userId, lang);
        const creditcard = user.creditcards.find((card) => card.id === cardId);
        if (!creditcard) {
            const translationResult = await tryCatch(
                this.i18n.translate(
                    PaymentMessagesKeys.NO_CARDS_FOUND_FOR_THIS_USER,
                    {
                        lang,
                    },
                ),
            );
            if (!translationResult.isSuccess) {
                this.logger.error(
                    `Error translating message: ${translationResult.error.message}`,
                );
            }
            throw new HttpException(
                translationResult.value as string,
                HttpStatus.BAD_REQUEST,
            );
        }
        const { doctor, patient, paymentMethod } = request;

        if (
            (kind === TransactionKind.REQUEST_FEE &&
                request.status === ConsultationRequestStatus.PENDINGPAYMENT) ||
            (kind === TransactionKind.KASHF_FEE && !request.isKashfFeePaid)
        ) {
            await this.transactionsRepository.delete({
                kind,
                request: { id: request.id },
            });
        }

        let transactionId: number;

        const transaction = await this.transactionsService.getTransaction(
            requestId,
            kind,
        );

        if (transaction) {
            transactionId = transaction.id;
        } else {
            transactionId = await this.transactionsService.createTransaction(
                user.id,
                patient.id,
                doctor.id,
                request.id,
                kind === TransactionKind.REQUEST_FEE
                    ? paymentMethod
                    : PaymentMethod.CREDIT_CARD,
                amount,
                TransactionStatus.INITIATED,
                request.serviceProvider ? request.serviceProvider.id : null,
                request.kashfPercentage,
                kind,
            );
        }

        if (kind === TransactionKind.REQUEST_FEE) {
            await this.consultationRequestsService.updateRequestStatusToPendingPayment(
                request.id,
                transactionId,
            );
        }

        const orderId = await this.paymobService.createOrder(totalAmount, []);
        await this.paymobTransactionsService.createPayMobTransaction(
            transactionId,
            orderId,
            null,
            TransactionStatus.INITIATED,
            null,
        );
        const paymentToken = await this.paymobService.getPaymentKey(
            orderId,
            totalAmount,
            user.email,
            user.name,
            user.phone,
        );

        const data = await this.paymobService.payWithSavedToken(
            paymentToken,
            creditcard.token,
        );
        if (!data.isSuccess) {
            throw new HttpException(data.error.message, HttpStatus.BAD_REQUEST);
        }
        const transactionData = data.value;
        await this.updateTransaction(transactionData, transactionData);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                PaymentMessagesKeys.PAYMENT_CREATED_AND_PAID_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async processingCallback(
        data: IPaymobProcessingCallbackPayload,
    ): Promise<any> {
        try {
            if (data.type === CallBackType.TOKEN) {
                const paymobTransaction =
                    await this.paymobTransactionsService.getPayMobTransactionByPaymobOrderId(
                        data.obj.order_id,
                    );

                if (!paymobTransaction) {
                    throw new HttpException(
                        'Paymob transaction not found',
                        HttpStatus.NOT_FOUND,
                    );
                }

                await this.creditCardsService.addCard(
                    paymobTransaction.transaction.user.id,
                    data.obj.masked_pan,
                    data.obj.token,
                    data.obj.card_subtype,
                );
            }
            if (data.type === CallBackType.TRANSACTION) {
                const transactionData: IPaymobTransactionCallBack = {
                    id: data.obj.id,
                    amountInCents: data.obj.amount_cents,
                    integrationId: data.obj.integration_id,
                    isPending: data.obj.pending,
                    isRefunded: data.obj.is_refunded,
                    isSucceeded: data.obj.success,
                    paymobOrderId: data.obj.order.id,
                    paymobUserId: data.obj.profile_id,
                    transactionResponseCode: data.obj.data.txn_response_code,
                    status: TransactionStatus.PROCESSING,
                    data,
                };
                await this.updateTransaction(transactionData, data);
            }
            if (data.error_occured) {
                throw new HttpException(
                    data.data.json.error.explanation,
                    HttpStatus.BAD_REQUEST,
                );
            }
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    async responseCallback(data: any): Promise<{
        status: 'failed' | 'success' | 'declined';
        kind: TransactionKind;
        lang: string;
    }> {
        try {
            const transactionData: IPaymobTransactionCallBack = {
                id: data.id,
                amountInCents: Number(data.amount_cents),
                integrationId: Number(data.integration_id),
                isPending: data.pending,
                isRefunded: data.is_refunded,
                isSucceeded: data.success,
                paymobOrderId: data.order,
                transactionResponseCode: data.txn_response_code,
                status: TransactionStatus.SUCCEEDED,
                data,
            };
            const transaction = await this.updateTransaction(
                transactionData,
                data,
            );

            if (
                !transactionData.isSucceeded ||
                data.txn_response_code === 'ERROR'
            ) {
                return {
                    status: 'failed',
                    kind: transaction.kind,
                    lang: transaction.user.appLanguage,
                };
            }

            if (
                !transactionData.isSucceeded ||
                data.txn_response_code === 'DECLINED'
            ) {
                return {
                    status: 'declined',
                    kind: transaction.kind,
                    lang: transaction.user.appLanguage,
                };
            }

            return {
                status: 'success',
                kind: transaction.kind,
                lang: transaction.user.appLanguage,
            };
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    async updateTransaction(
        transactionData: IPaymobTransactionCallBack,
        data: any,
    ): Promise<Transaction> {
        let transactionStatus = transactionData.status;

        if (transactionData.transactionResponseCode !== 'APPROVED') {
            transactionStatus = TransactionStatus.FAILED;
        }
        const paymobTransaction =
            await this.paymobTransactionsService.getPayMobTransactionByPaymobOrderId(
                transactionData.paymobOrderId,
            );

        if (!paymobTransaction) {
            throw new HttpException(
                'Paymob transaction not found',
                HttpStatus.NOT_FOUND,
            );
        }

        await this.paymobTransactionsService.createPayMobTransaction(
            paymobTransaction.transaction.id,
            transactionData.paymobOrderId,
            transactionData.id,
            transactionStatus,
            data,
        );

        const transaction = paymobTransaction.transaction;
        if (
            transaction.kind === TransactionKind.REQUEST_FEE &&
            transactionStatus === TransactionStatus.SUCCEEDED
        ) {
            await this.consultationRequestsService.updateRequestStatusToPaid(
                transaction.request.id,
            );

            // Create notifications for user and doctor
            if (transaction.user && transaction.doctor) {
                // Create notification for user
                const userNotification: CreateNotificationDto = {
                    userId: transaction.user.id,
                    data: {
                        requestId: transaction.request.id.toString(),
                        type: NotificationTypes.NOTIFY_PAYMENT_SUCCESSFUL,
                    },
                    notificationTranslations: [
                        {
                            title: 'تم الدفع بنجاح',
                            body: 'تم الدفع بنجاح للاستشارة',
                            languageCode: AvailableLanguageCodes.ar,
                        },
                        {
                            title: 'Payment successful',
                            body: 'Payment for consultation was successful',
                            languageCode: AvailableLanguageCodes.en,
                        },
                    ],
                };

                const createdUserNotification =
                    await this.notificationsService.createNotification(
                        userNotification,
                    );

                const sendNotificationResult = await tryCatch(
                    this.notificationsService.sendNotification(
                        createdUserNotification.createdId,
                        transaction.user.id,
                        transaction.user.appLanguage || 'en',
                    ),
                );

                if (!sendNotificationResult.isSuccess) {
                    this.logger.error(
                        `Error sending user notification: ${sendNotificationResult.error.message}`,
                    );
                }

                // Create notification for doctor
                const doctorNotification: CreateNotificationDto = {
                    userId: transaction.doctor.user?.id,
                    data: {
                        requestId: transaction.request.id.toString(),
                        type: NotificationTypes.NOTIFY_PAYMENT_SUCCESSFUL,
                    },
                    notificationTranslations: [
                        {
                            title: 'تم الدفع بنجاح',
                            body: 'تم الدفع بنجاح للاستشارة',
                            languageCode: AvailableLanguageCodes.ar,
                        },
                        {
                            title: 'Payment successful',
                            body: 'Payment for consultation was successful',
                            languageCode: AvailableLanguageCodes.en,
                        },
                    ],
                };

                const createdDoctorNotification =
                    await this.notificationsService.createNotification(
                        doctorNotification,
                    );

                const sendNotificationDoctorResult = await tryCatch(
                    this.notificationsService.sendNotification(
                        createdDoctorNotification.createdId,
                        transaction.doctor.user?.id,
                        transaction.doctor.user?.appLanguage || 'en',
                    ),
                );

                if (!sendNotificationDoctorResult.isSuccess) {
                    this.logger.error(
                        `Error sending doctor notification: ${sendNotificationDoctorResult.error.message}`,
                    );
                }
            }
        }

        if (
            transaction.kind === TransactionKind.BOOKING_FEE &&
            transactionStatus === TransactionStatus.SUCCEEDED
        ) {
            await this.bookingsService.updateBookingStatusToPaid(
                transaction.requestId,
            );
        }

        if (
            transaction.kind === TransactionKind.KASHF_FEE &&
            transactionStatus === TransactionStatus.SUCCEEDED
        ) {
            await this.consultationRequestsService.updateRequestStatusToPaid(
                transaction.request.id,
            );
            const isPendingKashfFees =
                await this.consultationRequestsService.isPendingKashfFees(
                    transaction.doctor.id,
                );

            if (!isPendingKashfFees) {
                await this.doctorsService.unsetPendingShareStatusForDoctor(
                    transaction.doctor.id,
                );
                const notification: CreateNotificationDto = {
                    userId: transaction.user.id,
                    data: {
                        requestId: transaction.request.id.toString(),
                        type: NotificationTypes.NOTIFY_ACCOUNT_REACTIVATED,
                    },
                    notificationTranslations: [
                        {
                            title: 'تم إعادة تفعيل الحساب',
                            body: 'تم إعادة تفعيل الحساب',
                            languageCode: AvailableLanguageCodes.ar,
                        },
                        {
                            title: 'Account reactivated',
                            body: 'Account reactivated',
                            languageCode: AvailableLanguageCodes.en,
                        },
                    ],
                };
                const createdNotification =
                    await this.notificationsService.createNotification(
                        notification,
                    );
                const sendNotificationResult = await tryCatch(
                    this.notificationsService.sendNotification(
                        createdNotification.createdId,
                        transaction.user.id,
                        transaction.user.appLanguage || 'en',
                    ),
                );

                if (!sendNotificationResult.isSuccess) {
                    this.logger.error(
                        `Error sending notification: ${sendNotificationResult.error.message}`,
                    );
                }
            }
        }
        await this.transactionsRepository.update(
            { id: transaction.id },
            {
                status: transactionStatus,
            },
        );
        return transaction;
    }

    async refundTransaction(
        id: number,
        paymobTransactionId: string,
        amount: string,
        requestId: number,
    ): Promise<void> {
        try {
            const transactionDataResult = await this.paymobService.refund(
                amount,
                paymobTransactionId,
            );

            if (!transactionDataResult.isSuccess) {
                // Handle case where error might be undefined
                const errorMessage = transactionDataResult.error
                    ? transactionDataResult.error.message
                    : 'Refund transaction failed';

                throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
            }
            const transactionData: IPaymobTransactionCallBack =
                transactionDataResult.value;

            let transactionStatus = transactionData.status;

            if (transactionData.transactionResponseCode !== 'APPROVED') {
                transactionStatus = TransactionStatus.FAILED;
            }
            await this.paymobTransactionsService.createPayMobTransaction(
                Number(id),
                transactionData.paymobOrderId,
                transactionData.id,
                transactionStatus,
                transactionData.data,
            );
            const transaction = await this.transactionsRepository.findOne({
                where: { id },
            });

            if (
                transaction.kind === TransactionKind.REQUEST_FEE &&
                transactionStatus === TransactionStatus.REFUNDED
            ) {
                await this.consultationRequestsService.updateRequestStatusToRefunded(
                    requestId,
                );
            }

            if (
                transaction.kind === TransactionKind.BOOKING_FEE &&
                transactionStatus === TransactionStatus.REFUNDED
            ) {
                await this.bookingsService.updateStatusToRefunded(
                    transaction.requestId,
                );
            }
            await this.transactionsRepository.update(id, {
                status: transactionStatus,
                refundedAmount: Number(amount) / 100,
            });
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    async downloadPayments(
        providerId: string,
        httpQueryString: string,
        user: UserDetailsDto,
        lang: string,
        kind: TransactionKind,
    ): Promise<string> {
        let query = this.transactionsRepository
            .createQueryBuilder('transaction')
            .leftJoinAndSelect('transaction.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'serviceProviderUser')
            .leftJoinAndSelect('transaction.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .leftJoinAndSelect('transaction.request', 'request')
            .leftJoinAndSelect('transaction.patient', 'patient')
            .leftJoinAndSelect('patient.user', 'patientUser')
            .leftJoinAndSelect(
                'serviceProviderUser.translations',
                'serviceProviderUserTranslations',
                'serviceProviderUserTranslations.languageCode = :lang',
                { lang },
            )
            .select([
                'transaction.id',
                'transaction.amount',
                'transaction.kashfPercentage',
                'transaction.createdAt',
                'request.type',
                'request.from',
                'request.id',
                'request.date',
                'doctor.id',
                'doctorUser.id',
                'doctorUser.name',
                'patient.id',
                'patientUser.id',
                'patientUser.name',
                'serviceProvider.id',
                'serviceProviderUser.id',
                'serviceProviderUserTranslations.id',
                'serviceProviderUserTranslations.name',
            ])
            .where('serviceProvider.kind = :kind', {
                kind,
            });

        if (httpQueryString) {
            query = this.buildGetPaymentsQuery(query, httpQueryString);
        }
        if (user && user.role === RoleType.SERVICE_PROVIDER) {
            providerId = user.serviceProvider.id.toString();
        }
        if (providerId) {
            query.andWhere('serviceProvider.id = :serviceProviderId', {
                serviceProviderId: parseInt(providerId, 10),
            });
        }
        const transactions = await query.getMany();
        const transactionsDto = transactions.map((transaction) => {
            const paymentDto = this.paymentsMapper.fromEntityToDTO(
                PaymentDto,
                transaction,
            );
            paymentDto.consultationType = paymentDto.request.type;
            paymentDto.date = paymentDto.request.date.toString();
            paymentDto.time = paymentDto.request.from;
            paymentDto.requestId = paymentDto.request.id;
            paymentDto.doctorName = paymentDto.doctor.user.name;
            paymentDto.patientName = paymentDto.patient.user.name;
            paymentDto.doctorId = paymentDto.doctor.id.toString();
            paymentDto.patientName =
                paymentDto.patient &&
                paymentDto.patient.user &&
                paymentDto.patient.user.name;
            paymentDto.subTotal = Number(
                paymentDto.amount -
                (paymentDto.amount * paymentDto.kashfPercentage) / 100,
            ).toFixed(2);
            return paymentDto;
        });

        const fields = [
            'requestId',
            'consultationType',
            'date',
            'time',
            'doctorName',
            'doctorId',
            'patientName',
            'amount',
            'kashfPercentage',
            'subTotal',
        ];
        return this.dataDownloadService.downloadCsv(fields, transactionsDto);
    }

    async getPayments(
        httpQueryString: string,
        pageOptionsDto: PageOptionsDto,
        user: UserDetailsDto,
        lang: string,
        kind: TransactionKind,
    ): Promise<PaymentsPageDto> {
        let query = this.transactionsRepository
            .createQueryBuilder('transaction')
            .leftJoinAndSelect('transaction.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'serviceProviderUser')
            .leftJoinAndSelect('transaction.doctor', 'doctor')
            .leftJoinAndSelect('doctor.user', 'doctorUser')
            .leftJoinAndSelect('transaction.request', 'request')
            .leftJoinAndSelect('transaction.patient', 'patient')
            .leftJoinAndSelect('patient.user', 'patientUser')
            .leftJoinAndSelect(
                'serviceProviderUser.translations',
                'serviceProviderUserTranslations',
                'serviceProviderUserTranslations.languageCode = :lang',
                { lang },
            )
            .select([
                'transaction.id',
                'transaction.amount',
                'transaction.kashfPercentage',
                'transaction.createdAt',
                'request.type',
                'request.from',
                'request.id',
                'request.date',
                'doctor.id',
                'doctorUser.id',
                'doctorUser.name',
                'patient.id',
                'patientUser.id',
                'patientUser.name',
                'serviceProvider.id',
                'serviceProviderUser.id',
                'serviceProviderUserTranslations.id',
                'serviceProviderUserTranslations.name',
            ])
            .where('serviceProvider.kind = :kind', {
                kind,
            });
        this.logger.log('query 1');
        if (httpQueryString) {
            query = this.buildGetPaymentsQuery(query, httpQueryString);
        }
        this.logger.log('query 2');
        if (user.role === RoleType.SERVICE_PROVIDER) {
            query.andWhere('serviceProvider.id = :serviceProviderId', {
                serviceProviderId: user.serviceProvider.id,
            });
        }
        this.logger.log('query 3');
        const [transactions, pageMetaDto] =
            await query.paginate(pageOptionsDto);
        this.logger.log('query 4');
        const transactionsDto = transactions.map((transaction) => {
            const paymentDto = this.paymentsMapper.fromEntityToDTO(
                PaymentDto,
                transaction,
            );
            if (paymentDto.serviceProvider) {
                paymentDto.serviceProvider.user.name =
                    paymentDto.serviceProvider.user.translations[0].name;
                delete paymentDto.serviceProvider.user.translations;
            }
            paymentDto.subTotal = Number(
                paymentDto.amount -
                (paymentDto.amount * paymentDto.kashfPercentage) / 100,
            ).toFixed(2);
            return paymentDto;
        });

        this.logger.log('query 5');
        return new PaymentsPageDto(transactionsDto, pageMetaDto);
    }

    buildGetPaymentsQuery(
        tQuery: SelectQueryBuilder<Transaction>,
        httpQueryString: string,
    ): SelectQueryBuilder<Transaction> {
        let query = tQuery;
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }
        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.DATE:
                    query.orderBy(
                        'transaction.createdAt',
                        httpQueryObject.sort.type,
                    );
                    break;
                case SortByType.DOCTOR_NAME:
                    query.orderBy('doctorUser.name', httpQueryObject.sort.type);
            }
        }
        if (httpQueryObject.search) {
            query.andWhere(
                '(request.id || patientUser.name || doctorUser.name || doctor.id || amount || transaction.kashfPercentage) ILIKE :searchKey',
                {
                    searchKey: `%${httpQueryObject.search.value}%`,
                },
            );
        }
        if (httpQueryObject.filters) {
            httpQueryObject.filters.forEach((filter) => {
                switch (filter.type) {
                    case FilterType.FIXED:
                        switch (filter.by) {
                            case FilterByType.SERVICE_PROVIDER:
                                query = query.andWhere(
                                    'serviceProvider.id = :serviceProviderId',
                                    {
                                        serviceProviderId: parseInt(
                                            filter.value,
                                            10,
                                        ),
                                    },
                                );
                                break;
                            case FilterByType.CONSULTATION_TYPE:
                                query.andWhere('request.type = :reqType', {
                                    reqType: filter.value,
                                });
                                break;
                            case FilterByType.DOCTOR:
                                query.andWhere('doctor.id = :id', {
                                    id: filter.value,
                                });
                        }
                        break;
                    case FilterType.RANGE:
                        switch (filter.by) {
                            case FilterByType.DATE:
                                query.andWhere(
                                    'transaction.createdAt between :min AND :max',
                                    {
                                        min: filter.min,
                                        max: filter.max,
                                    },
                                );
                        }
                }
            });
        }
        return query;
    }
}
