import { Injectable } from '@nestjs/common';

import { PaymobTransactions } from '../entities/paymob-transactions.entity';
import { Transaction } from '../entities/transaction.entity';
import { TransactionStatus } from '../payment.enum';
import { PaymobTransactionsRepository } from '../repositories/payment.logs.repository';

@Injectable()
export class PaymobTransactionsService {
    constructor(
        private readonly paymobTransactionsRepository: PaymobTransactionsRepository,
    ) {}

    async createPayMobTransaction(
        transactionId: number,
        paymobOrderId: string,
        paymobTransactionId: number | null,
        status: TransactionStatus,
        callbackData: any,
    ): Promise<number> {
        const transaction = new Transaction();
        transaction.id = transactionId;
        transaction.status = status;

        const paymobTransaction = this.paymobTransactionsRepository.create({
            transaction,
            paymobOrderId,
            status,
            paymobTransactionId,
            callbackData,
        });

        const createdPaymobTransaction = await this.paymobTransactionsRepository.save(
            paymobTransaction,
        );

        return createdPaymobTransaction.id;
    }

    async getPayMobTransactionByPaymobOrderId(
        paymobOrderId: string,
    ): Promise<PaymobTransactions> {
        return this.paymobTransactionsRepository
            .createQueryBuilder('paymobTransaction')
            .leftJoin('paymobTransaction.transaction', 'transaction')
            .leftJoin('transaction.request', 'request')
            .leftJoin('transaction.user', 'user')
            .leftJoin('transaction.doctor', 'doctor')
            .select([
                'transaction.id',
                'request.id',
                'transaction.amount',
                'transaction.kind',
                'user.id',
                'doctor.id',
                'user.appLanguage',
                'paymobTransaction.paymobOrderId',
                'paymobTransaction.status',
            ])
            .where('paymobTransaction.paymobOrderId = :paymobOrderId', {
                paymobOrderId,
            })
            .getOne();
    }

    async findUserIdByOrderId(
        paymobOrderId: string,
    ): Promise<PaymobTransactions> {
        return this.paymobTransactionsRepository
            .createQueryBuilder('paymobTransaction')
            .leftJoin('paymobTransaction.transaction', 'transaction')
            .leftJoin('transaction.user', 'user')
            .select(['paymobTransaction.id', 'transaction.id', 'user.id'])
            .where('paymobTransaction.paymobOrderId = :paymobOrderId', {
                paymobOrderId,
            })
            .getOne();
    }
}
