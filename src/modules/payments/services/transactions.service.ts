import { forwardRef, Inject, Injectable } from '@nestjs/common';

import {
    PaymentMethod,
    TransactionKind,
} from '../../../common/constants/types';
import { DebugLogger } from '../../../shared/services/logger.service';
import { BookingsService } from '../../booking/booking.service';
import { Booking } from '../../booking/entities/booking.entity';
import { ConsultationRequestsService } from '../../consultation-requests/consultation-requests.service';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { Requestable, Transaction } from '../entities/transaction.entity';
import { TransactionStatus } from '../payment.enum';
import { TransactionsRepository } from '../repositories/payments.repository';

@Injectable()
export class TransactionsService {
    constructor(
        private readonly transactionsRepository: TransactionsRepository,
        private readonly bookingsService: BookingsService,
        @Inject(forwardRef(() => ConsultationRequestsService))
        private readonly consultationRequestsService: ConsultationRequestsService,
        private readonly logger: DebugLogger,
    ) { }

    async createTransaction(
        userId: number,
        patientId: number,
        doctorId: number,
        requestId: number,
        type: PaymentMethod,
        amount: number,
        status: TransactionStatus,
        serviceProviderId: number,
        kashfPercentage: number,
        kind: TransactionKind,
    ): Promise<number> {
        const user = new DoctorEntity();
        user.id = userId;

        const doctor = new DoctorEntity();
        doctor.id = doctorId;

        const patient = new Patient();
        patient.id = patientId;

        const request = new ConsultationRequestEntity();
        request.id = requestId;

        const serviceProvider = new ConsultationRequestEntity();
        serviceProvider.id = serviceProviderId;

        const transactionToSave = this.transactionsRepository.create({
            type,
            status,
            amount,
            request,
            patient,
            doctor,
            user,
            serviceProvider,
            kashfPercentage,
            kind,
        });
        const createdTransaction = await this.transactionsRepository.save(
            transactionToSave,
        );
        return createdTransaction.id;
    }

    async createDonationTransaction(
        userId: number,
        type: PaymentMethod,
        amount: number,
        status: TransactionStatus,
        serviceProviderId: number,
        kind: TransactionKind,
    ): Promise<Transaction> {
        const serviceProvider = new ConsultationRequestEntity();
        serviceProvider.id = serviceProviderId;

        const user = new UserEntity();
        user.id = userId;

        const transactionToSave = this.transactionsRepository.create({
            type,
            status,
            amount,
            user,
            serviceProvider,
            kind,
        });

        return this.transactionsRepository.save(transactionToSave);
    }

    async getTransaction(
        requestId: number,
        kind: TransactionKind,
    ): Promise<Transaction> {
        return this.transactionsRepository.findOne({
            where: {
                kind,
                request: { id: requestId },
            },
        });
    }

    async findOneById(id: number): Promise<Transaction | null> {
        const transaction = await this.transactionsRepository.findOne({
            where: { id },
        });
        if (transaction) {
            await this.loadRequestRelation(transaction); // Load the related request
        }
        return transaction;
    }

    async removeTransaction(id: number, kind: TransactionKind): Promise<void> {
        await this.transactionsRepository.delete({ id, kind });
    }

    async createTransactionForRequest(
        amount: number,
        user: UserEntity,
        requestObject?: Requestable,
        serviceProvider?: ServiceProvider,
        doctor?: DoctorEntity,
        patient?: Patient,
        status = TransactionStatus.INITIATED,
        type = PaymentMethod.CREDIT_CARD,
        kashfPercentage = 0,
    ): Promise<number> {
        const requestId = requestObject?.id ?? null;

        let kind: TransactionKind;

        // Determine the type based on the passed object
        if (requestObject instanceof ConsultationRequestEntity) {
            kind = TransactionKind.REQUEST_FEE;
        } else if (requestObject instanceof Booking) {
            kind = TransactionKind.BOOKING_FEE;
        } else if (requestObject == null) {
            kind = TransactionKind.DONATION_FEE;
        }

        const newTransaction = this.transactionsRepository.create({
            amount,
            requestId,
            kind,
            status,
            user,
            serviceProvider,
            doctor,
            patient,
            kashfPercentage,
            type,
        });

        const createdTransaction =
            await this.transactionsRepository.save(newTransaction);
        return createdTransaction.id;
    }
    async loadRequestRelation(transaction: Transaction): Promise<void> {
        if (!transaction.requestId || !transaction.kind) {
            transaction.request = null;
            return;
        }

        let request: Requestable = null;
        try {
            switch (transaction.kind) {
                case TransactionKind.REQUEST_FEE:
                    request = await this.consultationRequestsService.findOne(
                        Number(transaction.requestId),
                    );
                    break;
                case TransactionKind.BOOKING_FEE:
                    request = await this.bookingsService.findOne(
                        transaction.requestId,
                    ); // Example
                    break;
                // Add cases for other request types
                default:
                    this.logger.warn(
                        `Unknown requestType: ${transaction.kind} for transaction ${transaction.id}`,
                    );
            }
        } catch (error) {
            this.logger.error(
                `Error loading request ${transaction.kind} with ID ${transaction.requestId}:`,
                error,
            );
            // Decide how to handle errors, maybe set request to null or re-throw
        }

        transaction.request = request;
    }

    async loadRequestRelationsForMultiple(
        transactions: Transaction[],
    ): Promise<void> {
        const requestsToFetch: { [key in TransactionKind]?: number[] } = {};

        // Group request IDs by type
        for (const transaction of transactions) {
            if (transaction.requestId && transaction.kind) {
                if (!requestsToFetch[transaction.kind]) {
                    requestsToFetch[transaction.kind] = [];
                }
                // Avoid duplicates if multiple transactions point to the same request
                if (
                    !requestsToFetch[transaction.kind].includes(
                        transaction.requestId,
                    )
                ) {
                    requestsToFetch[transaction.kind].push(
                        transaction.requestId,
                    );
                }
            } else {
                transaction.request = null; // Explicitly set to null if no ID/type
            }
        }

        // Fetch requests in batches
        const loadedRequests: { [key: string]: Requestable } = {}; // Key: "Type:ID"

        // eslint-disable-next-line guard-for-in
        for (const typeStr in requestsToFetch) {
            const type = typeStr as TransactionKind;
            const ids = requestsToFetch[type];
            if (!ids || ids.length === 0) {
                continue;
            }

            let fetchedItems: Requestable[] = [];
            try {
                switch (type) {
                    case TransactionKind.REQUEST_FEE:
                        fetchedItems =
                            await this.consultationRequestsService.findByIds(
                                ids,
                            );
                        break;
                    case TransactionKind.BOOKING_FEE:
                        fetchedItems =
                            await this.bookingsService.findByIds(ids);
                    // Add cases for other types
                }
            } catch (error) {
                this.logger.error(
                    `Error bulk loading requests of type ${type} with IDs ${JSON.stringify(
                        ids,
                    )}: ${error.message}`,
                );
                // Decide how to handle batch errors
            }
            fetchedItems.forEach((item) => {
                if (item) {
                    loadedRequests[`${type}:${item.id}`] = item;
                }
            });
        }

        // Assign loaded requests back to transactions
        for (const transaction of transactions) {
            if (transaction.requestId && transaction.kind) {
                const key = `${transaction.kind}:${transaction.requestId}`;
                transaction.request = loadedRequests[key] || null; // Assign found request or null
            }
        }
    }

    async findAll(): Promise<Transaction[]> {
        const transactions = await this.transactionsRepository.find();
        // Load relations for all transactions efficiently
        await this.loadRequestRelationsForMultiple(transactions);
        return transactions;
    }
}
