import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsBoolean,
    IsEnum,
    IsISO8601,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator';

import {
    ConsultationType,
    DiscountType,
} from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreatePromoCodeDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsString()
    @IsNotEmpty()
    code: string;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isActive?: boolean;

    @ApiProperty({ enum: DiscountType })
    @Expose()
    @IsEnum(DiscountType)
    discountType: string;

    @ApiProperty()
    @Expose()
    @IsNumber()
    amount: number;

    @ApiProperty()
    @Expose()
    @IsNumber()
    maxNumberOfUsage: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    country?: string;

    @ApiPropertyOptional({
        enum: ConsultationType,
        default: ConsultationType.ALL,
    })
    @Expose()
    @IsOptional()
    @IsEnum(ConsultationType)
    consultationType?: string;

    @ApiPropertyOptional({ example: 'YYYY-MM-DD' })
    @Expose()
    @IsOptional()
    @IsISO8601()
    startDate?: string;

    @ApiPropertyOptional({ example: 'YYYY-MM-DD' })
    @Expose()
    @IsOptional()
    @IsISO8601()
    endDate?: string;
}
