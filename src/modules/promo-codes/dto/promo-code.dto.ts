import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsBoolean,
    IsEnum,
    IsISO8601,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator';

import {
    ConsultationType,
    DiscountType,
} from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

export class PromoCodeDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    code?: string;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isActive?: boolean;

    @ApiPropertyOptional({ enum: DiscountType })
    @Expose()
    @IsOptional()
    @IsEnum(DiscountType)
    discountType?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsNumber()
    amount?: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsNumber()
    maxNumberOfUsage?: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    country?: string;

    @ApiPropertyOptional({
        enum: ConsultationType,
        default: ConsultationType.ALL,
    })
    @Expose()
    @IsOptional()
    @IsEnum(ConsultationType)
    consultationType?: string;

    @ApiPropertyOptional({ example: 'YYYY-MM-DD' })
    @Expose()
    @IsOptional()
    @IsISO8601()
    startDate?: string;

    @ApiPropertyOptional({ example: 'YYYY-MM-DD' })
    @Expose()
    @IsOptional()
    @IsISO8601()
    endDate?: string;
}
