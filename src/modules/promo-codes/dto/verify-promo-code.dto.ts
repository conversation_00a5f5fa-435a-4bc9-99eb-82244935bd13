import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class VerifyPromoCodeDto extends AbstractDto {
    @ApiProperty()
    @Expose()
    @IsString()
    code: string;

    @ApiProperty()
    @Expose()
    @IsNumber()
    requestId: number;
}
