import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { PromoCode } from './promo-code.entity';

@Entity('promo_codes-user')
export class PromoCodeUser extends AbstractEntity {
    @ManyToOne(() => PromoCode, {
        onDelete: 'CASCADE',
    })
    @JoinColumn()
    promoCode: PromoCode;

    @ManyToOne(() => UserEntity, {
        onDelete: 'CASCADE',
    })
    @JoinColumn()
    user: UserEntity;
}
