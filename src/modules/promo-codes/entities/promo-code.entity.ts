import { Expose } from 'class-transformer';
import {
    IsEnum,
    IsISO8601,
    IsN<PERSON>ber,
    IsOptional,
    IsString,
} from 'class-validator';
import { Column, Entity } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import {
    ConsultationType,
    DiscountType,
} from '../../../common/constants/types';

@Entity('promo_codes')
export class PromoCode extends AbstractEntity {
    @Column({ unique: true })
    @Expose()
    @IsOptional()
    @IsString()
    code?: string;

    @Column({ type: 'enum', enum: DiscountType, default: DiscountType.FIXED })
    @Expose()
    @IsOptional()
    @IsEnum(DiscountType)
    discountType?: DiscountType;

    @Column({ default: false })
    @Expose()
    isActive: boolean;

    @Column()
    @Expose()
    @IsOptional()
    @IsNumber()
    amount?: number;

    @Column()
    @Expose()
    @IsOptional()
    @IsNumber()
    maxNumberOfUsage?: number;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    country?: string;

    @Column({
        type: 'enum',
        enum: ConsultationType,
        default: ConsultationType.ALL,
    })
    @Expose()
    @IsOptional()
    @IsEnum(ConsultationType)
    consultationType?: ConsultationType;

    @Column({ type: 'date', nullable: true })
    @Expose()
    @IsOptional()
    @IsISO8601()
    startDate?: string;

    @Column({ type: 'date', nullable: true })
    @Expose()
    @IsOptional()
    @IsISO8601()
    endDate?: string | Date;
}
