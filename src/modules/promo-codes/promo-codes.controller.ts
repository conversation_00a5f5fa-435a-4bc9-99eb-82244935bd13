import {
    Body,
    Controller,
    // Delete,
    Get,
    Param,
    Post,
    Put,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    Api<PERSON><PERSON><PERSON>A<PERSON>,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreatePromoCodeDto } from './dto/create-promo-code.dto';
import { PromoCodeDto } from './dto/promo-code.dto';
import { UpdatePromoCodeDto } from './dto/update-promo-code.dto';
import { VerifyPromoCodeResponseDto } from './dto/verify-promo-code-response.dto';
import { VerifyPromoCodeDto } from './dto/verify-promo-code.dto';
import { PromoCodesService } from './promo-codes.service';

@Controller('promo-codes')
@ApiTags('promo-codes')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class PromoCodesController {
    constructor(private readonly promoCodesServie: PromoCodesService) {}

    /*********************** CRUD Operations *****************************/
    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get promo code',
        type: PromoCodeDto,
    })
    async getPromoCode(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<PromoCodeDto> {
        return this.promoCodesServie.getPromoCode(id, lang);
    }

    @Get()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get all promo codes',
        type: [PromoCodeDto],
    })
    async getAllPromoCodes(): Promise<PromoCodeDto[]> {
        return this.promoCodesServie.getAllPromoCodes();
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create promo code',
        type: CreateOperationsResponse,
    })
    async createPromoCode(
        @Body() createPromoCodeDto: CreatePromoCodeDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.promoCodesServie.createPromoCode(createPromoCodeDto, lang);
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update promo code',
        type: BasicOperationsResponse,
    })
    async updatePromoCode(
        @Param('id') id: number,
        @Body() updatePromoCodeDto: UpdatePromoCodeDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.promoCodesServie.updatePromoCode(
            id,
            updatePromoCodeDto,
            lang,
        );
    }

    // @Delete(':id')
    // @UseGuards(AuthGuard, RolesGuard)
    // @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    // @ApiResponse({
    //     description: 'Delete promo code',
    //     type: BasicOperationsResponse,
    // })
    // async deletePromoCode(
    //     @Param('id') id: number,
    //     @I18nLang() lang: string,
    // ): Promise<BasicOperationsResponse> {
    //     return this.promoCodesServie.deletePromoCode(id, lang);
    // }

    @Post('verify')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.PATIENT)
    @ApiResponse({
        description: 'verify promo code',
        type: BasicOperationsResponse,
    })
    async verifyPromoCode(
        @Req() req: Request,
        @Body() verifyPromoCodeDto: VerifyPromoCodeDto,
        @I18nLang() lang: string,
    ): Promise<VerifyPromoCodeResponseDto> {
        return this.promoCodesServie.verifyPromoCode(
            req.user.id,
            verifyPromoCodeDto.requestId,
            verifyPromoCodeDto.code,
            lang,
        );
    }
}
