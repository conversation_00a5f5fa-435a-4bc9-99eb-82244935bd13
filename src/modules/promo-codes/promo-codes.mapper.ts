import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreatePromoCodeDto } from './dto/create-promo-code.dto';
import { PromoCodeDto } from './dto/promo-code.dto';
import { UpdatePromoCodeDto } from './dto/update-promo-code.dto';
import { PromoCode } from './entities/promo-code.entity';
type PromoCodeDTOs = CreatePromoCodeDto | UpdatePromoCodeDto | PromoCodeDto;

@Injectable()
export class PromoCodesMapper extends AbstractMapper<PromoCodeDTOs, PromoCode> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<PromoCode>,
        sourceObject: PromoCodeDTOs,
    ): PromoCode {
        const promoCodeEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(promoCodeEntity);
        return promoCodeEntity;
    }

    fromEntityToDTO(
        destination: ClassType<PromoCodeDto>,
        sourceObject: PromoCode,
    ): PromoCodeDto {
        const promoCodeDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(promoCodeDto);
        return promoCodeDto;
    }
}
