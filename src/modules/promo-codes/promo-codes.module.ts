import { Modu<PERSON> } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { ConsultationRequestsModule } from '../consultation-requests/consultation-requests.module';
import { PromoCodesController } from './promo-codes.controller';
import { PromoCodesMapper } from './promo-codes.mapper';
import { PromoCodesService } from './promo-codes.service';
import { PromoCodesUserRepository } from './repositories/promo-codes-user.repository';
import { PromoCodesRepository } from './repositories/promo-codes.repository';

@Module({
    imports: [SharedModule, ConsultationRequestsModule],
    controllers: [PromoCodesController],
    exports: [PromoCodesService],
    providers: [
        PromoCodesService,
        PromoCodesMapper,
        PromoCodesRepository,
        PromoCodesUserRepository,
    ],
})
export class PromoCodesModule {}
