import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import * as moment from 'moment';
import { I18nService } from 'nestjs-i18n';
import { LessThanOrEqual, MoreThanOrEqual } from 'typeorm';

import { ConsultationType, DiscountType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { ConsultationRequestsService } from '../consultation-requests/consultation-requests.service';
import { CreatePromoCodeDto } from './dto/create-promo-code.dto';
import { PromoCodeDto } from './dto/promo-code.dto';
import { UpdatePromoCodeDto } from './dto/update-promo-code.dto';
import { VerifyPromoCodeResponseDto } from './dto/verify-promo-code-response.dto';
import { PromoCode } from './entities/promo-code.entity';
import { PromoCodesMapper } from './promo-codes.mapper';
import { PromoCodesUserRepository } from './repositories/promo-codes-user.repository';
import { PromoCodesRepository } from './repositories/promo-codes.repository';
import { PromoCodesMessagesKeys } from './translate.enum';

@Injectable()
export class PromoCodesService {
    constructor(
        private readonly promoCodesRepository: PromoCodesRepository,
        private readonly promoCodesUserRepository: PromoCodesUserRepository,
        private readonly consultationRequestsService: ConsultationRequestsService,
        private readonly promoCodesMapper: PromoCodesMapper,
        private readonly i18n: I18nService,
        public readonly helperService: HelperService,
    ) { }

    async getPromoCode(id: number, lang: string): Promise<PromoCodeDto> {
        const promoCode = await this.promoCodesRepository.findOne({ where: { id } });
        if (!promoCode) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        PromoCodesMessagesKeys.PROMO_CODE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.promoCodesMapper.fromEntityToDTO(PromoCodeDto, promoCode);
    }
    async getAllPromoCodes(): Promise<PromoCodeDto[]> {
        const promoCodes = await this.promoCodesRepository.find({
            order: { createdAt: 'DESC' },
        });
        return promoCodes.map((promoCode) =>
            this.promoCodesMapper.fromEntityToDTO(PromoCodeDto, promoCode),
        );
    }
    async createPromoCode(
        createPromoCodeDto: CreatePromoCodeDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const promoCodeEntity = this.promoCodesMapper.fromDTOToEntity(
            PromoCode,
            createPromoCodeDto,
        );
        const promoCodeExists = await this.promoCodesRepository.findOne({
            where: { code: promoCodeEntity.code },
        });
        if (promoCodeExists) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        PromoCodesMessagesKeys.CODE_ALREADY_USED,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const promoCode = this.promoCodesRepository.create(promoCodeEntity);
        const createdPromoCode = await this.promoCodesRepository.save(
            promoCode,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                PromoCodesMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdPromoCode.id,
        };
    }
    async updatePromoCode(
        id: number,
        updatePromoCodeDto: UpdatePromoCodeDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const promoCode = await this.promoCodesRepository.findOne({ where: { id } });
        if (!promoCode) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        PromoCodesMessagesKeys.PROMO_CODE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const promoCodeEntity = this.promoCodesMapper.fromDTOToEntity(
            PromoCode,
            updatePromoCodeDto,
        );

        await this.promoCodesRepository.update({ id }, promoCodeEntity);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                PromoCodesMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }
    async deletePromoCode(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const promoCode = await this.promoCodesRepository.findOne({ where: { id } });
        if (!promoCode) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        PromoCodesMessagesKeys.PROMO_CODE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.promoCodesRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                PromoCodesMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async verifyPromoCode(
        userId: number,
        requestId: number,
        code: string,
        lang: string,
    ): Promise<VerifyPromoCodeResponseDto> {
        const request = await this.consultationRequestsService.getRequest(
            requestId,
            lang,
        );
        const today = moment().add(2, 'hour').toDate();
        const promoCode = await this.promoCodesRepository.findOne({
            where: [
                {
                    code,
                    consultationType: request.type,
                    endDate: MoreThanOrEqual(today.toISOString()),
                    startDate: LessThanOrEqual(today.toISOString()),
                    isActive: true,
                },
                {
                    code,
                    consultationType: ConsultationType.ALL,
                    endDate: MoreThanOrEqual(today.toISOString()),
                    startDate: LessThanOrEqual(today.toISOString()),
                    isActive: true,
                },
            ],
        });
        if (!promoCode) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        PromoCodesMessagesKeys.PROMO_CODE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const numberOfUsagePerUser = await this.promoCodesUserRepository.count({
            where: {
                promoCode: {
                    id: promoCode.id,
                },
                user: {
                    id: userId,
                },
            },
        });
        if (numberOfUsagePerUser === promoCode.maxNumberOfUsage) {
            return {
                isSuccessful: false,
                message: await this.i18n.translate(
                    PromoCodesMessagesKeys.CODE_ALREADY_USED,
                    {
                        lang,
                    },
                ),
            };
        }
        const discountAmount = this.calculateDiscountAmount(
            request.fees,
            promoCode.amount,
            promoCode.discountType,
        );
        const promoCodeUser = this.promoCodesUserRepository.create({
            promoCode: {
                id: promoCode.id,
            },
            user: {
                id: userId,
            },
        });
        await Promise.all([
            this.consultationRequestsService.updateRequestDiscountAmount(
                requestId,
                discountAmount,
                promoCode.code,
            ),
            this.promoCodesUserRepository.save(promoCodeUser),
        ]);
        return {
            discountAmount,
            isSuccessful: false,
            message: await this.i18n.translate(
                PromoCodesMessagesKeys.VALID_CODE,
                {
                    lang,
                },
            ),
        };
    }
    calculateDiscountAmount(
        requestAmount: number,
        promoCodeAmount: number,
        discountType: DiscountType,
    ): number {
        let discountAmount = promoCodeAmount;

        if (discountType === DiscountType.PERCENTAGE) {
            discountAmount = (requestAmount * promoCodeAmount) / 100;
        }

        discountAmount = Math.min(discountAmount, requestAmount);

        return discountAmount;
    }
}
