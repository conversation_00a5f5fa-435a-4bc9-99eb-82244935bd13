'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class AbstractCreateRatingDto extends AbstractDto {
    @IsNumber()
    @ApiProperty()
    @Expose()
    requestId: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    rating: number;

    @IsString()
    @IsOptional()
    @ApiPropertyOptional()
    @Expose()
    comment?: string;

    @IsNumber()
    @IsOptional()
    @ApiPropertyOptional()
    @Expose()
    callRating?: number;

    @IsString()
    @IsOptional()
    @ApiPropertyOptional()
    @Expose()
    callComment?: string;
}
