'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';

import { AbstractCreateRatingDto } from './abstract-create-rating.dto';

export class CreateRatingDto extends AbstractCreateRatingDto {
    @IsNumber()
    @ApiProperty()
    @Expose()
    ratedUserId: number;

    @IsNumber()
    @ApiProperty()
    @Expose()
    raterUserId: number;
}
