'use strict';

import { Expose, Type } from 'class-transformer';
import { IsDateString, IsNumber, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ConsultationRequestDto } from '../../consultation-requests/dto/consultation-request.dto';
import { UserDto } from '../../users/dto/user.dto';

export class GetRatingDto extends AbstractDto {
    @Expose()
    @Type(() => ConsultationRequestDto)
    consultationRequest?: ConsultationRequestDto;

    @Expose()
    @Type(() => UserDto)
    ratedUser?: UserDto;

    @IsNumber()
    @Expose()
    @IsOptional()
    rating?: number;

    @IsString()
    @Expose()
    @IsOptional()
    comment?: string;

    @IsNumber()
    @Expose()
    @IsOptional()
    callRating?: number;

    @IsString()
    @Expose()
    @IsOptional()
    callComment?: string;

    @Expose()
    @Type(() => UserDto)
    raterUser?: UserDto;

    @IsDateString()
    @Expose()
    @IsOptional()
    createdAt?: Date;
}
