'use strict';

import { Expose, Type } from 'class-transformer';
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ConsultationRequestDto } from '../../consultation-requests/dto/consultation-request.dto';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { UserDto } from '../../users/dto/user.dto';
import { UserEntity } from '../../users/entities/user.entity';
@Entity('ratings')
export class Rating extends AbstractEntity {
    @Column({ type: 'float', default: 0 })
    @Expose()
    rating: number;

    @Column({ type: 'float', nullable: true })
    @Expose()
    callRating: number;

    @Column({ nullable: true })
    @Expose()
    callComment: string;

    @Column({ nullable: true })
    @Expose()
    comment: string;

    @ManyToOne((_type) => UserEntity, (user) => user.ratings, {
        onDelete: 'CASCADE',
    })
    @Expose()
    @Type(() => UserDto)
    ratedUser: UserEntity;

    @ManyToOne(() => UserEntity, {
        onDelete: 'CASCADE',
    })
    @JoinColumn()
    @Expose()
    @Type(() => UserDto)
    raterUser: UserEntity;

    @ManyToOne(
        (_type) => ConsultationRequestEntity,
        (consultationRequest) => consultationRequest.ratings,
        {
            onDelete: 'CASCADE',
        },
    )
    @Expose()
    @Type(() => ConsultationRequestDto)
    consultationRequest: ConsultationRequestEntity;
}
