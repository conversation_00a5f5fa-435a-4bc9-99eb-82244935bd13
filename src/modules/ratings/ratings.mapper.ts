import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { UserEntity } from '../../modules/users/entities/user.entity';
import { ConsultationRequestEntity } from '../consultation-requests/entities/consultation-request.entity';
import { CreateRatingDto } from './dto/create-rating.dto';
import { GetRatingDto } from './dto/get-rating.dto';
import { Rating } from './entities/rating.entity';
type RatingDto = CreateRatingDto | GetRatingDto;

@Injectable()
export class RatingsMapper extends AbstractMapper<RatingDto, Rating> {
    constructor() {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<Rating>,
        sourceObject: CreateRatingDto,
    ): Rating {
        const ratingEntity = super.fromDTOToEntity(destination, sourceObject);
        const rated = new UserEntity();
        rated.id = sourceObject.ratedUserId;
        ratingEntity.ratedUser = rated;

        const rater = new UserEntity();
        rater.id = sourceObject.raterUserId;
        ratingEntity.raterUser = rater;

        const consultationRequest = new ConsultationRequestEntity();
        consultationRequest.id = sourceObject.requestId;
        ratingEntity.consultationRequest = consultationRequest;

        return ratingEntity;
    }

    fromEntityToDTO(
        destination: ClassType<GetRatingDto>,
        sourceObject: Rating,
    ): RatingDto {
        return super.fromEntityToDTO(destination, sourceObject);
    }
}
