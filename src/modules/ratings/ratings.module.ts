import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SharedModule } from '../../shared/shared.module';
import { AuthModule } from '../auth/auth.module';
import { SchedulesRepository } from '../schedules/repositories/schedules.repository';
import { SchedulesModule } from '../schedules/schedules.module';
import { UsersModule } from '../users/users.module';
import { Rating } from './entities/rating.entity';
import { RatingsMapper } from './ratings.mapper';
import { RatingsService } from './ratings.service';
import { RatingsRepository } from './repositories/ratings.repository';
@Module({
    imports: [
        TypeOrmModule.forFeature([Rating]),
        SharedModule,
        AuthModule,
        UsersModule,
        forwardRef(() => SchedulesModule),
    ],
    exports: [RatingsService],
    providers: [
        RatingsService,
        RatingsMapper,
        RatingsRepository,
        SchedulesRepository,
    ],
})
export class RatingsModule {}
