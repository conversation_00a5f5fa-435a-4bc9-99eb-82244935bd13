import { Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { SortingType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { UsersService } from '../users/users.service';
import { CreateRatingDto } from './dto/create-rating.dto';
import { GetRatingDto } from './dto/get-rating.dto';
import { Rating } from './entities/rating.entity';
import { RatingsMapper } from './ratings.mapper';
import { RatingsRepository } from './repositories/ratings.repository';
import { DoctorMessagesKeys } from './translate.enum';

@Injectable()
export class RatingsService {
    constructor(
        private readonly ratingsRepository: RatingsRepository,
        private readonly usersService: UsersService,
        private readonly ratingsMapper: RatingsMapper,
        private readonly i18n: I18nService,
    ) {}

    async createRating(
        createRatingDto: CreateRatingDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const ratingEntity = this.ratingsMapper.fromDTOToEntity(
            Rating,
            createRatingDto,
        );
        const rating = this.ratingsRepository.create(ratingEntity);
        await this.ratingsRepository.save(rating);

        await this.usersService.updateAverageRating(
            createRatingDto.ratedUserId,
            createRatingDto.rating,
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                DoctorMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async getDoctorReviews(
        doctorUserId: number,
        numberOfReviews = -1, // -1 means all
    ): Promise<GetRatingDto[]> {
        const query = this.ratingsRepository
            .createQueryBuilder('rating')
            .leftJoinAndSelect('rating.ratedUser', 'ratedUser')
            .leftJoinAndSelect('rating.raterUser', 'raterUser')
            .leftJoinAndSelect(
                'rating.consultationRequest',
                'consultationRequest',
            )
            .where('ratedUser.id = :id', { id: doctorUserId })
            .select([
                'rating.id',
                'rating.createdAt',
                'rating.rating',
                'rating.comment',
                'raterUser.id',
                'raterUser.name',
                'raterUser.avatar',
                'consultationRequest.id',
            ])
            .orderBy('rating.createdAt', SortingType.DESC);

        if (numberOfReviews > 0) {
            query.take(numberOfReviews);
        }

        const ratings = await query.getMany();

        return ratings.map((rating) =>
            this.ratingsMapper.fromEntityToDTO(GetRatingDto, rating),
        );
    }

    async getIsRequestRated(
        requestId: number,
        userId: number,
    ): Promise<boolean> {
        const isRated = await this.ratingsRepository.findOne({
            where: {
                consultationRequest: {
                    id: requestId,
                },
                raterUser: {
                    id: userId,
                },
            },
        });

        if (!isRated) {
            return false;
        }
        return true;
    }
}
