import { Expose } from 'class-transformer';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ReminderType } from '../../../common/constants/status';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';

@Entity('reminders')
export class ReminderEntity extends AbstractEntity {
    @ManyToOne(() => ConsultationRequestEntity, {
        cascade: true,
        onDelete: 'CASCADE',
    })
    @JoinColumn({ name: 'request_id' })
    request: ConsultationRequestEntity;

    @Column({ type: 'timestamp' })
    date: Date;

    @Column({ default: false })
    status: boolean;

    @Column({
        type: 'enum',
        enum: ReminderType,
    })
    @Expose()
    type: ReminderType;
}
