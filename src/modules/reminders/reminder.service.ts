import { Injectable } from '@nestjs/common';
import * as moment from 'moment';

import { ReminderType } from '../../common/constants/status';
import { ConsultationType } from '../../common/constants/types';
import { DebugLogger } from '../../shared/services/logger.service';
import { ConsultationRequestEntity } from '../consultation-requests/entities/consultation-request.entity';
import { ReminderEntity } from './entities/reminder.entity';
import { ReminderRepository } from './repositories/reminder.repository';

@Injectable()
export class ReminderService {
    constructor(
        private readonly reminderRepository: ReminderRepository,
        private readonly logger: DebugLogger,
    ) {}

    async createReminders(request: ConsultationRequestEntity): Promise<void> {
        const date = moment(
            moment(request.date, 'YYYY-MM-DD').format('YYYY-MM-DD') +
                ' ' +
                moment(request.from, 'hh:mm A').format('hh:mm A'),
            'YYYY-MM-DD hh:mm A',
        );

        const days = date.diff(moment(), 'days');
        const hours = date.diff(moment(), 'hours');
        const minutes = date.diff(moment(), 'minutes');

        const reminders: ReminderEntity[] = [];

        if (
            minutes > 15 &&
            request.type === ConsultationType.ONLINE_CONSULTATION
        ) {
            reminders.push(
                this.saveNewReminder(
                    date.clone().add(-15, 'minutes').toDate(),
                    request,
                    ReminderType.FIFTEEN_MINUTES_BEFORE,
                ),
            );
        }
        if (hours > 2) {
            reminders.push(
                this.saveNewReminder(
                    date.clone().add(-2, 'hours').toDate(),
                    request,
                    ReminderType.TWO_HOURS_BEFORE,
                ),
            );
        }
        if (days > 1) {
            reminders.push(
                this.saveNewReminder(
                    date.clone().add(-1, 'days').toDate(),
                    request,
                    ReminderType.ONE_DAY_BEFORE,
                ),
            );
        }

        if (reminders.length > 0) {
            await this.reminderRepository
                .createQueryBuilder()
                .insert()
                .values(reminders)
                .execute();
        }
    }

    private saveNewReminder(
        date: Date,
        request: ConsultationRequestEntity,
        type: ReminderType,
    ): ReminderEntity {
        const reminder = this.reminderRepository.create();
        reminder.date = date;
        reminder.request = request;
        reminder.type = type;
        return reminder;
    }

    async updateWithAccomplished(): Promise<
        { id: number; type: ReminderType }[]
    > {
        const query = this.reminderRepository
            .createQueryBuilder()
            .update(ReminderEntity)
            .set({ status: true })
            .where(
                "DATE_TRUNC('minute', date) = DATE_TRUNC('minute', CURRENT_TIMESTAMP)",
            )
            .andWhere('status = :status', {
                status: false,
            })
            .returning(['request_id as id', 'type']);

        try {
            const result = await query.execute();
            // result is an array of updated entities with the specified columns
            if (result.raw.length > 0) {
                this.logger.log(
                    `schedule_reminder_requested=>${JSON.stringify(
                        result.raw,
                    )}`,
                );

                return result.raw;
            }
            return []; // No records were updated
        } catch (error) {
            this.logger.error(JSON.stringify(error));
        }

        return [];
    }

    async deleteReminders(request: ConsultationRequestEntity): Promise<void> {
        const query = this.reminderRepository
            .createQueryBuilder()
            .update(ReminderEntity)
            .set({ status: true })
            .whereInIds(request.reminders.map((reminder) => reminder.id));

        try {
            await query.execute();
        } catch (error) {
            this.logger.error(JSON.stringify(error));
        }
    }
}
