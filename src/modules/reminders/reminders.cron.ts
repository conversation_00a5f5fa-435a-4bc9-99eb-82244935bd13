import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { PsuedoLogger } from '../../common/PsuedoLogger';
import { ConsultationRequestsService } from '../consultation-requests/consultation-requests.service';
import { ReminderService } from './reminder.service';

@Injectable()
export class CronConsultationRequest {
    private readonly logger = new PsuedoLogger();

    constructor(
        private reminderService: ReminderService,
        private consultationRequestsService: ConsultationRequestsService,
    ) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async remindPatientAndDoctorsForAppointments(): Promise<void> {
        const ids = await this.reminderService.updateWithAccomplished();
        this.consultationRequestsService
            .sendReminderNotifications(ids)
            .catch((error) => this.logger.error(error));
    }
}
