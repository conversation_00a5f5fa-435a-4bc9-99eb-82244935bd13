import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { ReminderService } from './reminder.service';
import { ReminderRepository } from './repositories/reminder.repository';

@Module({
    imports: [SharedModule],
    controllers: [],
    exports: [ReminderService],
    providers: [ReminderService,ReminderRepository],
})
export class RemindersModule {}
