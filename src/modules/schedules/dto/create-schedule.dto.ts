'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsBoolean,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
} from 'class-validator';

import { ConsultationType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreateScheduleDto extends AbstractDto {
    @IsEnum(ConsultationType)
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    type?: ConsultationType;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isEnabled?: boolean;

    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    fees: number;

    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    clinicId?: number;

    @IsNumber()
    @Expose()
    @ApiProperty()
    @IsNotEmpty()
    doctorId: number;

    @IsNumber()
    @Expose()
    @IsOptional()
    @ApiPropertyOptional()
    serviceProviderId?: number;
}
