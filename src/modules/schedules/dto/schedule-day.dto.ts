'use strict';

import { Expose, Type } from 'class-transformer';
import {
    IsBoolean,
    IsEnum,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator';

import { WorkingDaysType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ScheduleDto } from './schedule.dto';

export class ScheduleDayDto extends AbstractDto {
    @IsEnum(WorkingDaysType)
    @Expose()
    day: WorkingDaysType;

    @IsString()
    @Expose()
    from: Date;

    @IsString()
    @Expose()
    to: Date;

    @IsBoolean()
    @Expose()
    isActive: boolean;

    @IsString()
    @Expose()
    @IsOptional()
    breakFrom?: Date;

    @IsString()
    @Expose()
    @IsOptional()
    breakTo?: Date;

    @IsBoolean()
    @Expose()
    containsBreak: boolean;

    @IsNumber()
    @Expose()
    order: number;

    @Expose()
    @Type(() => ScheduleDto)
    @IsOptional()
    schedule?: ScheduleDto;
}
