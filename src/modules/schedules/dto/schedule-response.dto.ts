'use strict';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsBoolean,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { WorkingDayDto } from './working-day.dto';

export class ScheduleResponseDto extends AbstractDto {

    @IsString()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    type?: string;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isEnabled?: boolean;

    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    clinicId?: number;

    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    serviceProviderId?: number;

    @IsNumber()
    @Expose()
    @ApiProperty()
    @IsNotEmpty()
    doctorId: number;

    @ValidateNested()
    @Type(() => WorkingDayDto)
    @ApiProperty()
    @Expose()
    workingDays: WorkingDayDto[];
}
