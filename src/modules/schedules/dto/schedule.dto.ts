'use strict';

import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsNumber, IsOptional } from 'class-validator';

import { ConsultationType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ClinicDto } from '../../clinics/dto/clinic.dto';
import { DoctorDto } from '../../doctors/dto/doctor.dto';
import { ServiceProviderDto } from '../../service-providers/dto/service-provider.dto';
import { ScheduleDayDto } from './schedule-day.dto';

export class ScheduleDto extends AbstractDto {
    @IsEnum(ConsultationType)
    @Expose()
    @IsOptional()
    type?: ConsultationType;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isEnabled?: boolean;

    @IsNumber()
    @Expose()
    @IsOptional()
    fees?: number;

    @Expose()
    @Type(() => ClinicDto)
    @IsOptional()
    clinic?: ClinicDto;

    @Expose()
    @Type(() => DoctorDto)
    @IsOptional()
    doctor?: DoctorDto;

    @Expose()
    @Type(() => ServiceProviderDto)
    @IsOptional()
    serviceProvider?: ServiceProviderDto;

    @Expose()
    @Type(() => ScheduleDayDto)
    workingDays?: ScheduleDayDto[];
}
