'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { UpdateWorkingDayDto } from './update-working-day.dto';

export class UpdateScheduleDto extends AbstractDto {
    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isEnabled?: boolean;

    @IsNumber()
    @Expose()
    @IsOptional()
    fees?: number;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    cancelRequests?: boolean;

    @Expose()
    @IsOptional()
    @ApiPropertyOptional()
    @Type(() => UpdateWorkingDayDto)
    workingDays?: UpdateWorkingDayDto[];
}
