'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsNumber } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class UpdateWorkingDayDto extends AbstractDto {


    @Expose()
    @ApiProperty()
    from: Date;

    @Expose()
    @ApiProperty()
    to: Date;

    @IsBoolean()
    @Expose()
    @ApiProperty()
    isActive: boolean;
}
