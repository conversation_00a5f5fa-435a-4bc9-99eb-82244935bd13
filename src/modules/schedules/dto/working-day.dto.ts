'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsNumber, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class WorkingDayDto extends AbstractDto {


    @IsString()
    @Expose()
    @ApiProperty()
    day: string;

    @Expose()
    @ApiProperty()
    from: Date;

    @Expose()
    @ApiProperty()
    to: Date;

    @Expose()
    @ApiProperty()
    breakFrom: Date;

    @Expose()
    @ApiProperty()
    breakTo: Date;

    @IsBoolean()
    @Expose()
    @ApiProperty()
    containsBreak: boolean;

    @IsBoolean()
    @Expose()
    @ApiProperty()
    isActive: boolean;
}
