'use strict';

import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { WorkingDaysType } from '../../../common/constants/types';
import { ScheduleEntity } from './schedule.entity';
@Entity('schedule_days')
export class ScheduleDayEntity extends AbstractEntity {
    @Column()
    @Expose()
    order: number;

    @Column({ type: 'enum', enum: WorkingDaysType })
    @Expose()
    day: string;

    @Column({ type: 'time', default: '9:00 AM' })
    @Expose()
    from: Date;

    @Column({ type: 'time', default: '9:00 PM' })
    @Expose()
    to: Date;

    @Column({ type: 'time', nullable: true })
    @Expose()
    breakFrom: Date;

    @Column({ type: 'time', nullable: true })
    @Expose()
    breakTo: Date;

    @Column({ default: false })
    @Expose()
    isActive: boolean;

    @Column({ default: false })
    @Expose()
    containsBreak: boolean;

    @ManyToOne((_type) => ScheduleEntity, (schedule) => schedule.workingDays, {
        onDelete: 'CASCADE',
        cascade: true,
    })
    schedule: ScheduleEntity;
}
