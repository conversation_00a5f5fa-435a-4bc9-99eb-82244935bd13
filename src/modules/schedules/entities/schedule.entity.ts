'use strict';

import { Expose } from 'class-transformer';
import {
    Column,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    OneToOne,
} from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ConsultationType } from '../../../common/constants/types';
import { Clinic } from '../../clinics/entities/clinic.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { ScheduleDayEntity } from './schedule-day.entity';
@Entity('schedules')
export class ScheduleEntity extends AbstractEntity {
    @Column({ type: 'enum', enum: ConsultationType, nullable: true })
    @Expose()
    type: ConsultationType;

    @Column({ default: false })
    @Expose()
    isEnabled: boolean;

    @Column({ default: 0 })
    @Expose()
    fees: number;

    @OneToOne((_type) => Clinic, (clinic) => clinic.schedule, {
        onDelete: 'CASCADE',
    })
    @JoinColumn()
    @Expose()
    clinic?: Clinic;

    @ManyToOne((_type) => DoctorEntity, (doctor) => doctor.schedules, {
        onDelete: 'CASCADE',
    })
    @Expose()
    doctor: DoctorEntity;

    @ManyToOne(
        (_type) => ServiceProvider,
        (serviceProvider) => serviceProvider.schedules,
        {
            onDelete: 'CASCADE',
        },
    )
    @Expose()
    serviceProvider: ServiceProvider;

    @OneToMany(
        (_type) => ScheduleDayEntity,
        (scheduleDays) => scheduleDays.schedule,
        {
            onDelete: 'CASCADE',
        },
    )
    @Expose()
    workingDays: ScheduleDayEntity[];
}
