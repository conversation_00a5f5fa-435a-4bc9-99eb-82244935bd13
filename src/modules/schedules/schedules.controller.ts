'use strict';

import {
    Body,
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Put,
    Query,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { ScheduleResponseDto } from './dto/schedule-response.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { SchedulesService } from './schedules.service';

@Controller('schedules')
@ApiTags('schedules')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@ApiBearerAuth()
export class SchedulesController {
    constructor(private schedulesService: SchedulesService) {}
    @Get('/doctor/:doctorId')
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Get doctor schedule',
        type: [ScheduleResponseDto],
    })
    getDoctorSchedules(
        @Param('doctorId') doctorId: number,
        @Query('serviceProviderId') serviceProviderId: number,
        @Query('type') type: string,
    ): Promise<ScheduleResponseDto[]> {
        return this.schedulesService.getDoctorSchedule(
            doctorId,
            serviceProviderId,
            type,
        );
    }
    @Put(':id')
    @ApiResponse({
        description: 'Update Schedule',
        type: BasicOperationsResponse,
    })
    @UseGuards(AuthGuard, RolesGuard)
    @UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
    @Roles(RoleType.DOCTOR)
    update(
        @Req() req: Request,
        @Param('id') id: number,
        @Body() doctorDto: UpdateScheduleDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        let doctorId;
        if (req.user.role === RoleType.DOCTOR) {
            doctorId = req.user.doctor.id;
        }

        return this.schedulesService.updateSchedule(
            id,
            doctorId,
            req.user,
            doctorDto,
            lang,
        );
    }
}
