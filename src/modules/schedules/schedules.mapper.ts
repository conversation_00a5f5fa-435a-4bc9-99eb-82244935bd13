import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { Clinic } from '../clinics/entities/clinic.entity';
import { DoctorEntity } from '../doctors/entities/doctor.entity';
import { ServiceProvider } from '../service-providers/entities/service-provider.entity';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { ScheduleResponseDto } from './dto/schedule-response.dto';
import { ScheduleDto } from './dto/schedule.dto';
import { ScheduleEntity } from './entities/schedule.entity';

@Injectable()
export class SchedulesMapper extends AbstractMapper<
    ScheduleDto,
    ScheduleEntity
> {
    constructor() {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<ScheduleEntity>,
        sourceObject: CreateScheduleDto,
    ): ScheduleEntity {
        const scheduleEntity = super.fromDTOToEntity(destination, sourceObject);

        scheduleEntity.doctor = new DoctorEntity();
        scheduleEntity.doctor.id = sourceObject.doctorId;
        scheduleEntity.clinic = new Clinic();
        scheduleEntity.clinic.id = sourceObject.clinicId;
        scheduleEntity.serviceProvider = new ServiceProvider();
        scheduleEntity.serviceProvider.id = sourceObject.serviceProviderId;

        return scheduleEntity;
    }
    fromEntitiesToDTOs(sourceObject: ScheduleEntity[]): ScheduleResponseDto[] {
        const scheduleDtos: ScheduleResponseDto[] = [];
        sourceObject.forEach((schedule) => {
            const scheduleDto: ScheduleResponseDto = {
                id: schedule.id,
                clinicId: schedule.clinic ? schedule.clinic.id : null,
                isEnabled: schedule.isEnabled,
                type: schedule.type,
                doctorId: schedule.doctor.id,
                serviceProviderId: schedule.serviceProvider
                    ? schedule.serviceProvider.id
                    : null,
                workingDays: schedule.workingDays.map((workingDay) => ({
                    id: workingDay.id,
                    day: workingDay.day,
                    to: workingDay.to,
                    from: workingDay.from,
                    breakFrom: workingDay.breakFrom,
                    breakTo: workingDay.breakTo,
                    containsBreak: workingDay.containsBreak,
                    isActive: workingDay.isActive,
                })),
            };
            scheduleDtos.push(scheduleDto);
        });
        return scheduleDtos;
    }

    fromEntityToDTO(
        destination: ClassType<ScheduleDto>,
        sourceObject: ScheduleEntity,
    ): ScheduleDto {
        return super.fromEntityToDTO(destination, sourceObject);
    }
}
