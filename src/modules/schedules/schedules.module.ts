import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SharedModule } from '../../shared/shared.module';
import { ConsultationRequestsModule } from '../consultation-requests/consultation-requests.module';
import { ScheduleDayEntity } from './entities/schedule-day.entity';
import { ScheduleEntity } from './entities/schedule.entity';
import { SchedulesDaysRepository } from './repositories/schedules.days.repository';
import { SchedulesRepository } from './repositories/schedules.repository';
import { SchedulesController } from './schedules.controller';
import { SchedulesMapper } from './schedules.mapper';
import { SchedulesService } from './schedules.service';

@Module({
    imports: [
        TypeOrmModule.forFeature([ScheduleEntity, ScheduleDayEntity]),
        SharedModule,
        forwardRef(() => ConsultationRequestsModule)
    ],
    controllers: [SchedulesController],
    exports: [SchedulesService],
    providers: [
        SchedulesService,
        SchedulesMapper,
        SchedulesRepository,
        SchedulesDaysRepository,
    ],
})
export class SchedulesModule {}
