/* eslint-disable complexity */
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import * as moment from 'moment';
import { I18nService } from 'nestjs-i18n';
import { In } from 'typeorm';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';

import { ConsultationRequestStatus } from '../../common/constants/status';
import {
    ConsultationType,
    SortingType,
    WorkingDaysType,
} from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { DebugLogger } from '../../shared/services/logger.service';
import { ConsultationRequestsService } from '../consultation-requests/consultation-requests.service';
import { ConsultationRequestEntity } from '../consultation-requests/entities/consultation-request.entity';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { ScheduleResponseDto } from './dto/schedule-response.dto';
import { ScheduleDto } from './dto/schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { UpdateWorkingDayDto } from './dto/update-working-day.dto';
import { ScheduleDayEntity } from './entities/schedule-day.entity';
import { ScheduleEntity } from './entities/schedule.entity';
import { SchedulesDaysRepository } from './repositories/schedules.days.repository';
import { SchedulesRepository } from './repositories/schedules.repository';
import { SchedulesMapper } from './schedules.mapper';
import { ScheduleMessagesKeys } from './translate.enum';

@Injectable()
export class SchedulesService {
    constructor(
        public readonly schedulesRepository: SchedulesRepository,
        public readonly schedulesDaysRepository: SchedulesDaysRepository,
        public readonly schedulesMapper: SchedulesMapper,
        private readonly i18n: I18nService,
        @Inject(forwardRef(() => ConsultationRequestsService))
        private readonly consultationRequestsService: ConsultationRequestsService,
        private logger: DebugLogger,
    ) { }

    async createSchedule(
        createScheduleDto: CreateScheduleDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const scheduleEntity = this.schedulesMapper.fromDTOToEntity(
            ScheduleEntity,
            createScheduleDto,
        );
        this.logger.log(JSON.stringify(scheduleEntity));
        await this.validateBeforInsert(scheduleEntity);

        const createdSchedule = await this.schedulesRepository.insert(
            scheduleEntity,
        );

        this.logger.log(JSON.stringify(createdSchedule));

        await this.createScheduleDays(createdSchedule.raw[0].id);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ScheduleMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
            createdId: createdSchedule.raw[0].id,
        };
    }

    async getDoctorSchedule(
        doctorId: number,
        serviceProviderId: number,
        type: string,
    ): Promise<ScheduleResponseDto[]> {
        const query = this.schedulesRepository
            .createQueryBuilder('schedules')
            .leftJoinAndSelect('schedules.workingDays', 'workingDays')
            .leftJoinAndSelect('schedules.clinic', 'clinic')
            .leftJoinAndSelect('schedules.doctor', 'doctor')
            .leftJoinAndSelect('schedules.serviceProvider', 'serviceProvider')
            .orderBy('workingDays.order', 'ASC')
            .where('doctor.id = :doctorId', { doctorId });
        if (serviceProviderId) {
            query.andWhere(
                'schedules.serviceProvider.id = :serviceProviderId',
                { serviceProviderId },
            );
        }
        if (type) {
            query.andWhere('schedules.type = :type', { type });
        }
        const doctorSchedules = await query.getMany();

        return this.schedulesMapper.fromEntitiesToDTOs(doctorSchedules);
    }

    async getDoctorScheduleForPatient(
        doctorId: number,
        scheduleId: number,
        dayId?: number,
    ): Promise<ScheduleDto> {
        const query = this.schedulesRepository
            .createQueryBuilder('schedules')
            .leftJoinAndSelect('schedules.clinic', 'clinic')
            .leftJoinAndSelect('schedules.workingDays', 'workingDays')
            .leftJoinAndSelect('schedules.doctor', 'doctor')
            .select([
                'doctor.id',
                'schedules.id',
                'schedules.type',
                'schedules.isEnabled',
                'clinic.id',
                'workingDays.id',
                'workingDays.order',
                'workingDays.day',
                'workingDays.from',
                'workingDays.to',
                'workingDays.isActive',
            ])
            .where('schedules.id = :scheduleId AND doctor.id = :doctorId', {
                scheduleId,
                doctorId,
            })
            .orderBy('workingDays.order', SortingType.ASC);

        if (dayId) {
            query.andWhere('workingDays.id = :dayId', { dayId });
        }
        const scheduleEntity = await query.getOne();
        return this.schedulesMapper.fromEntityToDTO(
            ScheduleDto,
            scheduleEntity,
        );
    }

    /**
     * @deprecated since 10/2/2021
     */
    async getDoctorSchedulesForPatient(
        httpQueryObject: IHttpQuery,
        doctorId: number,
    ): Promise<ScheduleDto> {
        let query = this.schedulesRepository
            .createQueryBuilder('schedules')
            .leftJoinAndSelect('schedules.clinic', 'clinic')
            .leftJoinAndSelect('schedules.workingDays', 'workingDays')
            .leftJoinAndSelect('schedules.doctor', 'doctor')
            .select([
                'doctor.id',
                'schedules.id',
                'schedules.type',
                'schedules.isEnabled',
                'clinic.id',
                'workingDays.id',
                'workingDays.order',
                'workingDays.day',
                'workingDays.from',
                'workingDays.to',
                'workingDays.isActive',
            ])
            .where('doctor.id = :doctorId', { doctorId })
            .orderBy('workingDays.order', SortingType.ASC);

        if (httpQueryObject) {
            query = this.buildGetDoctorScheduleQuery(query, httpQueryObject);
        }

        const scheduleEntity = await query.getOne();
        return this.schedulesMapper.fromEntityToDTO(
            ScheduleDto,
            scheduleEntity,
        );
    }

    /**
     * @deprecated since 10/2/2021
     */
    buildGetDoctorScheduleQuery(
        query: SelectQueryBuilder<ScheduleEntity>,
        httpQueryObject: IHttpQuery,
    ): SelectQueryBuilder<ScheduleEntity> {
        if (httpQueryObject.filters) {
            httpQueryObject.filters.forEach((filter) => {
                switch (filter.by) {
                    case 'home':
                        query.andWhere('schedules.type = :type', {
                            type: ConsultationType.HOME_VISIT,
                        });
                        break;
                    case 'online':
                        query.andWhere('schedules.type = :type', {
                            type: ConsultationType.ONLINE_CONSULTATION,
                        });
                        break;
                    case 'clinic':
                        query.andWhere(
                            'schedules.type = :type AND clinic.id = :clinicId ',
                            {
                                type: ConsultationType.CLINIC,
                                clinicId: parseInt(filter.value, 10),
                            },
                        );
                        break;
                    case 'date':
                        /* eslint-disable no-case-declarations */
                        const date = moment(filter.value, 'YYYY-MM-DD');
                        const dayIndex =
                            date.day() + 1 > 6 ? 0 : date.day() + 1;
                        query.andWhere('workingDays.order = :order', {
                            order: dayIndex,
                        });
                }
            });
        }

        return query;
    }

    async updateSchedule(
        id: number,
        doctorId: number,
        user: UserDetailsDto,
        scheduleDto: UpdateScheduleDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const schedule = await this.schedulesRepository.findOne({
            where: {
                id,
                doctor: { id: doctorId },
            },
        });
        if (!schedule) {
            return {
                isSuccessful: false,
                message: await this.i18n.translate(
                    ScheduleMessagesKeys.SCHEDULE_NOT_FOUND,
                    {
                        lang,
                    },
                ),
            };
        }
        if (scheduleDto.isEnabled == null) {
            scheduleDto.isEnabled = schedule.isEnabled;
        }

        if (scheduleDto.fees == null) {
            scheduleDto.fees = schedule.fees;
        }
        await this.schedulesRepository.update(
            { id, doctor: { id: doctorId } },
            { isEnabled: scheduleDto.isEnabled, fees: scheduleDto.fees },
        );
        if (scheduleDto.cancelRequests) {
            const inActiveDays = await this.getInActiveDays(
                scheduleDto.workingDays,
            );
            const requests =
                await this.consultationRequestsService.getMonthlyDoctorRequests(
                    doctorId,
                );
            const requestsIds = this.getRequestsToCancel(
                requests,
                inActiveDays,
            );
            await Promise.all(
                requestsIds.map(async (requestId: number) => {
                    await this.consultationRequestsService.updateRequest(
                        { status: ConsultationRequestStatus.CANCELLED },
                        requestId,
                        user,
                        lang,
                    );
                }),
            );
        }
        if (scheduleDto.workingDays && scheduleDto.workingDays.length) {
            await this.schedulesDaysRepository.save(scheduleDto.workingDays);
        }

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ScheduleMessagesKeys.UPDATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
        };
    }

    async getInActiveDays(
        workingDays: UpdateWorkingDayDto[],
    ): Promise<string[]> {
        const workingDaysIds = workingDays.map((day) => day.id);

        const days = await this.schedulesDaysRepository.find({
            where: { id: In(workingDaysIds) },
        });

        return workingDays.map((workingDay) => {
            const day = days.find(
                (currentDay) => workingDay.id === currentDay.id,
            );
            if (
                day.isActive !== workingDay.isActive &&
                workingDay.isActive === false
            ) {
                return day.day;
            }
        });
    }

    getRequestsToCancel(
        requests: ConsultationRequestEntity[],
        inActiveDays: string[],
    ): number[] {
        const filterdRequests = requests.filter((request) => {
            const day = moment(request.date).format('dddd');
            return inActiveDays.includes(day);
        });
        return filterdRequests.map((request) => request.id);
    }

    async validateBeforInsert(scheduleEntity: ScheduleEntity): Promise<void> {
        if (!scheduleEntity.type && !scheduleEntity.clinic) {
            throw Error('you must specify type or clinic to create schedule');
        }
        const [scheduleExistForSameType, scheduleExistForSameClinic] =
            await Promise.all([
                this.schedulesRepository.findOne({
                    where: {
                        doctor: scheduleEntity.doctor,
                        type: scheduleEntity.type,
                        serviceProvider: scheduleEntity.serviceProvider.id
                            ? scheduleEntity.serviceProvider
                            : null,
                    },
                }),
                this.schedulesRepository.findOne({
                    where: {
                        doctor: scheduleEntity.doctor,
                        clinic: scheduleEntity.clinic,
                        serviceProvider: scheduleEntity.serviceProvider.id
                            ? scheduleEntity.serviceProvider
                            : null,
                    },
                }),
            ]);
        if (
            (scheduleExistForSameType &&
                scheduleEntity.type !== ConsultationType.CLINIC) ||
            scheduleExistForSameClinic
        ) {
            throw Error('Schedule is already exists');
        }
    }

    async createScheduleDays(scheduleId: number): Promise<void> {
        const days = [
            { day: WorkingDaysType.SATURDAY, order: 0 },
            { day: WorkingDaysType.SUNDAY, order: 1 },
            { day: WorkingDaysType.MONDAY, order: 2 },
            { day: WorkingDaysType.TUESDAY, order: 3 },
            { day: WorkingDaysType.WEDNESDAY, order: 4 },
            { day: WorkingDaysType.THURSDAY, order: 5 },
            { day: WorkingDaysType.FRIDAY, order: 6 },
        ];

        const schedule = new ScheduleEntity();
        schedule.id = scheduleId;

        await Promise.all(
            days.map(async (day) => {
                await this.schedulesDaysRepository.insert({
                    schedule,
                    order: day.order,
                    day: day.day,
                    isActive: true,
                });
            }),
        );
    }

    async findScheduleDay(scheduleDayId: number): Promise<ScheduleDayEntity> {
        return this.schedulesDaysRepository.findOne({
            where: {
                id: scheduleDayId,
            },
        });
    }
}
