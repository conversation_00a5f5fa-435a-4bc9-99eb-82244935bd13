import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>To<PERSON>ne,
    PrimaryGeneratedColumn,
} from 'typeorm';

import { Clinic } from '../../clinics/entities/clinic.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';

@Entity('secretaries')
export class SecretaryEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true })
    email: string;

    @Column({ unique: true })
    phone: string;

    @Column({ nullable: false })
    clinicId: number;

    @ManyToOne(() => Clinic, (clinic) => clinic.secretaries, {
        onDelete: 'CASCADE',
    })
    @JoinColumn({ name: 'clinic_id' })
    clinic: Clinic;

    @Column({ nullable: false })
    doctorId: number;

    @ManyToOne(() => DoctorEntity, (doctor) => doctor.secretaries, {
        onDelete: 'CASCADE',
    })
    @JoinColumn({ name: 'doctor_id' })
    doctor: DoctorEnti<PERSON>;
}
