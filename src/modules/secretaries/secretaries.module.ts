import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { SecretaryRepository } from './repositories/secretary.repository';
import { SecretaryService } from './secretary.service';

@Module({
    imports: [SharedModule],
    controllers: [],
    exports: [SecretaryService],
    providers: [SecretaryService,SecretaryRepository],
})
export class SecretariesModule {}
