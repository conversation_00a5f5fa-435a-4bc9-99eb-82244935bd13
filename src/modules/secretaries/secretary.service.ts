import { Injectable } from '@nestjs/common';

import { DebugLogger } from '../../shared/services/logger.service';
import { SecretaryRepository } from './repositories/secretary.repository';

@Injectable()
export class SecretaryService {
    constructor(
        private readonly secretariesRepository: Secretary<PERSON><PERSON><PERSON>itory,
        private readonly logger: Debug<PERSON>og<PERSON>,
    ) {}
}
