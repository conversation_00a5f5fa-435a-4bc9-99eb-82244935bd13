import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ArrayMinSize, IsArray, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ServiceProviderTypeTranslation } from '../entities/service-provider-type-translation.entity';

export class CreateServiceProviderTypeDto extends AbstractDto {
    @ApiProperty({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @IsArray()
    @ArrayMinSize(2)
    @Expose()
    @Type(() => ServiceProviderTypeTranslation)
    translations: ServiceProviderTypeTranslation[];
}
