import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ServiceProviderTypeTranslation } from '../entities/service-provider-type-translation.entity';

export class ServiceProviderTypeDto extends AbstractDto {
    @ApiPropertyOptional()
    @IsString()
    @Expose()
    @IsOptional()
    title?: string;

    @ApiPropertyOptional()
    @IsArray()
    @IsOptional()
    @Expose()
    @Type(() => ServiceProviderTypeTranslation)
    translations?: ServiceProviderTypeTranslation[];
}
