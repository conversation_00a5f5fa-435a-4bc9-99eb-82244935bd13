import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ServiceProviderTypeTranslation } from '../entities/service-provider-type-translation.entity';

export class UpdateServiceProviderTypeDto extends AbstractDto {
    @ApiPropertyOptional({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @IsArray()
    @IsOptional()
    @Expose()
    @Type(() => ServiceProviderTypeTranslation)
    translations?: ServiceProviderTypeTranslation[];
}
