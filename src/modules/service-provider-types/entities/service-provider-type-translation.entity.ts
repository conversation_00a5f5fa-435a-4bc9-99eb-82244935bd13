import { Expose } from 'class-transformer';
import { IsObject, IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ServiceProviderType } from './service-provider-type.entity';

@Entity('service_provider_types_translations')
export class ServiceProviderTypeTranslation extends AbstractEntity {
    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    title?: string;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    languageCode?: string;

    @ManyToOne(
        (_type) => ServiceProviderType,
        (serviceProviderType) => serviceProviderType.translations,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    @Expose()
    @IsOptional()
    @IsObject()
    serviceProviderType?: ServiceProviderType;
}
