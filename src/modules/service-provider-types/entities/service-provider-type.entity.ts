import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional } from 'class-validator';
import { Entity, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { ServiceProviderTypeTranslation } from './service-provider-type-translation.entity';

@Entity('service_provider_types')
export class ServiceProviderType extends AbstractEntity {
    @OneToMany(
        (_type) => ServiceProviderTypeTranslation,
        (serviceProviderTypeTranslation) =>
            serviceProviderTypeTranslation.serviceProviderType,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: ServiceProviderTypeTranslation[];

    @OneToMany(
        (_type) => ServiceProvider,
        (serviceProvider) => serviceProvider.serviceProviderType,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsArray()
    @IsOptional()
    @Type(() => ServiceProvider)
    serviceProviders?: ServiceProvider[];
}
