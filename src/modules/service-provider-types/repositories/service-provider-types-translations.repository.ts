import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { ServiceProviderTypeTranslation } from '../entities/service-provider-type-translation.entity';

@Injectable()
export class ServiceProviderTypesTranslationsRepository extends Repository<ServiceProviderTypeTranslation> {
    constructor(dataSource: DataSource) {
        super(ServiceProviderTypeTranslation, dataSource.createEntityManager());
    }
}
