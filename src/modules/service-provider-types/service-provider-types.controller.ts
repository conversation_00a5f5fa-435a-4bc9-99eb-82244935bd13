import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    Api<PERSON><PERSON>erAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateServiceProviderTypeDto } from './dto/create-service-provider-type.dto';
import { ServiceProviderTypeDto } from './dto/service-provider-type.dto';
import { UpdateServiceProviderTypeDto } from './dto/update-service-provider-type.dto';
import { ServiceProviderTypesService } from './service-provider-types.service';

@Controller('service-provider-types')
@ApiTags('service-provider-types')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class ServiceProviderTypesController {
    constructor(
        private readonly serviceProviderTypesService: ServiceProviderTypesService,
    ) {}

    /*********************** CRUD Operations *****************************/
    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN, RoleType.SERVICE_PROVIDER)
    @ApiResponse({
        description: 'Get service provider type',
        type: ServiceProviderTypeDto,
    })
    async getServiceProviderType(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<ServiceProviderTypeDto> {
        return this.serviceProviderTypesService.getServiceProviderType(
            id,
            lang,
        );
    }
    @Get()
    @ApiResponse({
        description: 'Get all service provider types',
        type: [ServiceProviderTypeDto],
    })
    async getAllServiceProviderTypes(): Promise<ServiceProviderTypeDto[]> {
        return this.serviceProviderTypesService.getAllServiceProviderTypes();
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create service provider type',
        type: CreateOperationsResponse,
    })
    async createServiceProviderType(
        @Body() createServiceProviderTypeDto: CreateServiceProviderTypeDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.serviceProviderTypesService.createServiceProviderType(
            createServiceProviderTypeDto,
            lang,
        );
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update service provider type',
        type: BasicOperationsResponse,
    })
    async updateServiceProviderType(
        @Param('id') id: number,
        @Body() updateServiceProviderTypeDto: UpdateServiceProviderTypeDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.serviceProviderTypesService.updateServiceProviderType(
            id,
            updateServiceProviderTypeDto,
            lang,
        );
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete service provider type',
        type: BasicOperationsResponse,
    })
    async deleteServiceProviderType(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.serviceProviderTypesService.deleteServiceProviderType(
            id,
            lang,
        );
    }
}
