import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreateServiceProviderTypeDto } from './dto/create-service-provider-type.dto';
import { ServiceProviderTypeDto } from './dto/service-provider-type.dto';
import { UpdateServiceProviderTypeDto } from './dto/update-service-provider-type.dto';
import { ServiceProviderType } from './entities/service-provider-type.entity';

type ServiceProviderTypeDTOs =
    | CreateServiceProviderTypeDto
    | UpdateServiceProviderTypeDto
    | ServiceProviderTypeDto;

@Injectable()
export class ServiceProviderTypesMapper extends AbstractMapper<
    ServiceProviderTypeDTOs,
    ServiceProviderType
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<ServiceProviderType>,
        sourceObject: ServiceProviderTypeDTOs,
    ): ServiceProviderType {
        const serviceProviderTypeEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(serviceProviderTypeEntity);
        return serviceProviderTypeEntity;
    }

    fromEntityToDTO(
        destination: ClassType<ServiceProviderTypeDto>,
        sourceObject: ServiceProviderType,
    ): ServiceProviderTypeDto {
        const serviceProviderTypeDto = super.fromEntityToDTO(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(serviceProviderTypeDto);
        return serviceProviderTypeDto;
    }
}
