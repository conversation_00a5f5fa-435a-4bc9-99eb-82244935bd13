import { Modu<PERSON> } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import {
    ServiceProviderTypesTranslationsRepository,
} from './repositories/service-provider-types-translations.repository';
import { ServiceProviderTypesRepository } from './repositories/service-provider-types.repository';
import { ServiceProviderTypesController } from './service-provider-types.controller';
import { ServiceProviderTypesMapper } from './service-provider-types.mapper';
import { ServiceProviderTypesService } from './service-provider-types.service';

@Module({
    imports: [SharedModule],
    controllers: [ServiceProviderTypesController],
    providers: [
        ServiceProviderTypesService,
        ServiceProviderTypesMapper,
        ServiceProviderTypesRepository,
        ServiceProviderTypesTranslationsRepository,
    ],
})
export class ServiceProviderTypesModule {
}
