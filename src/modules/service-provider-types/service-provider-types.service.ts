import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { CreateServiceProviderTypeDto } from './dto/create-service-provider-type.dto';
import { ServiceProviderTypeDto } from './dto/service-provider-type.dto';
import { UpdateServiceProviderTypeDto } from './dto/update-service-provider-type.dto';
import { ServiceProviderTypeTranslation } from './entities/service-provider-type-translation.entity';
import { ServiceProviderType } from './entities/service-provider-type.entity';
import { ServiceProviderTypesTranslationsRepository } from './repositories/service-provider-types-translations.repository';
import { ServiceProviderTypesRepository } from './repositories/service-provider-types.repository';
import { ServiceProviderTypesMapper } from './service-provider-types.mapper';
import { ServiceProviderTypeMessagesKeys } from './translate.enum';

@Injectable()
export class ServiceProviderTypesService {
    constructor(
        private readonly serviceProviderTypesRepository: ServiceProviderTypesRepository,
        private readonly serviceProviderTypesTranslationsRepository: ServiceProviderTypesTranslationsRepository,
        private readonly serviceProviderTypesMapper: ServiceProviderTypesMapper,
        private readonly i18n: I18nService,
        public readonly helperService: HelperService,
    ) { }
    async getServiceProviderType(
        id: number,
        lang: string,
    ): Promise<ServiceProviderTypeDto> {
        const serviceProviderType =
            await this.serviceProviderTypesRepository.findOne({
                where: { id },
                relations: ['translations'],
            });
        if (!serviceProviderType) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderTypeMessagesKeys.SERVICE_PROVIDER_TYPE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.serviceProviderTypesMapper.fromEntityToDTO(
            ServiceProviderTypeDto,
            serviceProviderType,
        );
    }
    async getAllServiceProviderTypes(): Promise<ServiceProviderTypeDto[]> {
        const serviceProviderTypes =
            await this.serviceProviderTypesRepository.find({
                relations: ['translations'],
            });
        return serviceProviderTypes.map((serviceProviderType) =>
            this.serviceProviderTypesMapper.fromEntityToDTO(
                ServiceProviderTypeDto,
                serviceProviderType,
            ),
        );
    }
    async createServiceProviderType(
        createServiceProviderTypeDto: CreateServiceProviderTypeDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const serviceProviderTypeEntity =
            this.serviceProviderTypesMapper.fromDTOToEntity(
                ServiceProviderType,
                createServiceProviderTypeDto,
            );
        const serviceProviderType = this.serviceProviderTypesRepository.create(
            serviceProviderTypeEntity,
        );
        const createdServiceProviderType =
            await this.serviceProviderTypesRepository.save(serviceProviderType);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderTypeMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdServiceProviderType.id,
        };
    }
    async updateServiceProviderType(
        id: number,
        updateServiceProviderTypeDto: UpdateServiceProviderTypeDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const serviceProviderType =
            await this.serviceProviderTypesRepository.findOne({
                where: { id },
            });
        if (!serviceProviderType) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderTypeMessagesKeys.SERVICE_PROVIDER_TYPE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const serviceProviderTypeEntity =
            this.serviceProviderTypesMapper.fromDTOToEntity(
                ServiceProviderType,
                updateServiceProviderTypeDto,
            );

        if (serviceProviderTypeEntity.translations) {
            await this.updateTranslation(
                id,
                serviceProviderTypeEntity.translations,
            );
            delete serviceProviderTypeEntity.translations;
        }
        await this.serviceProviderTypesRepository.update(
            { id },
            serviceProviderTypeEntity,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderTypeMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }
    async deleteServiceProviderType(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const serviceProviderType =
            await this.serviceProviderTypesRepository.findOne({
                where: { id },
            });
        if (!serviceProviderType) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderTypeMessagesKeys.SERVICE_PROVIDER_TYPE_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.serviceProviderTypesRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderTypeMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async updateTranslation(
        parentId: number,
        updatedTranslation: ServiceProviderTypeTranslation[],
    ): Promise<void> {
        await Promise.all(
            updatedTranslation.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);
                await this.serviceProviderTypesTranslationsRepository.update(
                    {
                        serviceProviderType: { id: parentId },
                        languageCode: translation.languageCode,
                    },
                    translation,
                );
            }),
        );
    }
}
