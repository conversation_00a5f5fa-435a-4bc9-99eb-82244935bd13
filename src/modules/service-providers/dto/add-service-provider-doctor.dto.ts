import { Expose, Type } from 'class-transformer';
import {
    ArrayMinSize,
    IsArray,
    IsNotEmpty,
    ValidateNested,
} from 'class-validator';

import { ServiceProviderScheduleDto } from './service-provider-doctor-schedule.dto';

export class AddServiceProviderDoctor {
    @ValidateNested()
    @Expose()
    @IsArray()
    @ArrayMinSize(3)
    @IsNotEmpty()
    @Type(() => ServiceProviderScheduleDto)
    serviceProviderSchedule?: ServiceProviderScheduleDto[];
}
