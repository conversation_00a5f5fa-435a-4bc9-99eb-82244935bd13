import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsNumber,
    IsOptional,
    IsString,
    IsUrl,
} from 'class-validator';

import { ILocation } from '../../../interfaces/ILocation';
import { CreateUserDto } from '../../users/dto/create-user.dto';
export class CreateServiceProviderDto extends CreateUserDto {
    @ApiProperty()
    @Expose()
    @IsNumber()
    serviceProviderTypeId: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    coordinatorPhone?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    coordinatorName?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    landlinePhone?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    agreeToTermsAndPolicy?: boolean;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    cityId?: number;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    governorateId?: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    area?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    location?: ILocation;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    website?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    commercialRecord?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    taxId?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    docs?: string[];

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    kashfPercentage?: number;

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    kashfDonationPercentage?: number;

    @Expose()
    @IsOptional()
    @IsString()
    signupCode?: string;
}
