import { Expose } from 'class-transformer';
import { IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class DownloadServiceProviderDto extends AbstractDto {
    @IsString()
    @Expose()
    name: string;

    @IsString()
    @Expose()
    type: string;

    @IsString()
    @Expose()
    email: string;

    @IsString()
    @Expose()
    coordinatorPhone: string;

    @IsString()
    @Expose()
    area: string;
}
