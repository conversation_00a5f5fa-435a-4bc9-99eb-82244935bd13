import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    ArrayMinSize,
    IsArray,
    IsBoolean,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    ValidateNested,
} from 'class-validator';

import { ConsultationType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { ScheduleDayDto } from '../../schedules/dto/schedule-day.dto';

export class ServiceProviderScheduleDto extends AbstractDto {
    @IsEnum(ConsultationType)
    @Expose()
    type?: ConsultationType;

    @IsNumber()
    @Expose()
    fees?: number;

    @IsBoolean()
    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    isEnabled?: boolean;

    @Expose()
    @ValidateNested()
    @IsArray()
    @IsNotEmpty()
    @ArrayMinSize(6)
    @Type(() => ScheduleDayDto)
    workingDays?: ScheduleDayDto[];
}
