import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsObject, IsOptional } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { UserDto } from '../../users/dto/user.dto';

export class ServiceProviderForDoctorDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => UserDto)
    user?: UserDto;
}
