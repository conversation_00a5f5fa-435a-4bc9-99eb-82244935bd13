import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsObject,
    IsOptional,
    IsString,
    IsUrl,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { ILocation } from '../../../interfaces/ILocation';
import { City } from '../../cities/entities/city.entity';
import { DoctorDetailsDto } from '../../doctors/dto/doctor-details.dto';
import { Governorate } from '../../governorates/entities/governorate.entity';
import { InsuranceCompanyDto } from '../../insurance-companies/dto/insurance-company.dto';
import { ServiceProviderTypeDto } from '../../service-provider-types/dto/service-provider-type.dto';
import { UserDto } from '../../users/dto/user.dto';

export class ServiceProviderDto extends AbstractDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    coordinatorPhone?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    coordinatorName?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    landlinePhone?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    agreeToTermsAndPolicy?: boolean;

    @Expose()
    @IsOptional()
    @Type(() => City)
    city?: City;

    @Expose()
    @IsOptional()
    @Type(() => Governorate)
    governorate?: Governorate;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    area?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    location?: ILocation;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    website?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    commercialRecord?: string;

    @IsString()
    @Expose()
    @IsOptional()
    instapayUrl?: string;

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    kashfPercentage?: number;

    @Expose()
    @ApiPropertyOptional()
    @IsOptional()
    kashfDonationPercentage?: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    taxId?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isFreeCall?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isFreeVisit?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isAcceptingDonation?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isCheckedIn?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    docs?: string[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => UserDto)
    user?: UserDto;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => ServiceProviderTypeDto)
    serviceProviderType?: ServiceProviderTypeDto;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => InsuranceCompanyDto)
    insuranceCompanies?: InsuranceCompanyDto[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => DoctorDetailsDto)
    doctors?: DoctorDetailsDto[];

    @Expose()
    @IsOptional()
    @IsString()
    signupCode?: string;
}
