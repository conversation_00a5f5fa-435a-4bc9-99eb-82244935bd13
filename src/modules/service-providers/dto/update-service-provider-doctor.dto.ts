import { Expose, Type } from 'class-transformer';
import { IsArray, IsNotEmpty, ValidateNested } from 'class-validator';

import { UpdateServiceProviderScheduleDto } from './update-service-provider-doctor-schedule.dto';

export class UpdateServiceProviderDoctor {
    @ValidateNested()
    @Expose()
    @IsArray()
    @IsNotEmpty()
    @Type(() => UpdateServiceProviderScheduleDto)
    serviceProviderSchedule?: UpdateServiceProviderScheduleDto[];
}
