import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsNumber,
    IsOptional,
    IsString,
    IsUrl,
} from 'class-validator';

import { ILocation } from '../../../interfaces/ILocation';
import { UpdateUserDto } from '../../users/dto/update-user.dto';

export class UpdateServiceProviderDto extends UpdateUserDto {
    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsNumber()
    serviceProviderTypeId?: number;

    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @Expose()
    kashfPercentage?: number;

    @IsNumber()
    @ApiPropertyOptional()
    @IsOptional()
    @Expose()
    kashfDonationPercentage?: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    landlinePhone?: string;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    cityId?: number;

    @IsOptional()
    @IsNumber()
    @Expose()
    @ApiPropertyOptional()
    governorateId?: number;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    area?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    location?: ILocation;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    website?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    commercialRecord?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsUrl()
    taxId?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isFreeVisit?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isFreeCall?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsBoolean()
    isAcceptingDonation?: boolean;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    docs?: string[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsArray()
    insuranceCompaniesIds?: number[];

    @IsString()
    @Expose()
    @IsOptional()
    instapayUrl?: string;

    @Expose()
    @IsOptional()
    @IsString()
    signupCode?: string;
}
