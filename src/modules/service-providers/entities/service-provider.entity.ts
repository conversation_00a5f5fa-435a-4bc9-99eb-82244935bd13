import { Expose, Type } from 'class-transformer';
import {
    <PERSON>A<PERSON>y,
    IsBoolean,
    IsObject,
    IsOptional,
    IsString,
} from 'class-validator';
import {
    Column,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    OneToOne,
} from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { IGeometry } from '../../../interfaces/ILocation';
import { ColumnNumericTransformer } from '../../../shared/services/column.numeric.transform';
import { City } from '../../cities/entities/city.entity';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { CustomNotification } from '../../custom-notifications/entities/custom-notification.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { DoctorServiceProviderCheckInLogs } from '../../doctors/entities/doctor.service-provider.check-in.logs.entity';
import { Governorate } from '../../governorates/entities/governorate.entity';
import { InsuranceCompany } from '../../insurance-companies/entities/insurance-company.entity';
import { Transaction } from '../../payments/entities/transaction.entity';
import { ScheduleEntity } from '../../schedules/entities/schedule.entity';
import { ServiceProviderType } from '../../service-provider-types/entities/service-provider-type.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { Package } from '../../packages/entities/package.entity';

@Entity('service_providers')
export class ServiceProvider extends AbstractEntity {
    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    coordinatorPhone?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    coordinatorName?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    instapayUrl?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    agreeToTermsAndPolicy?: boolean;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    landlinePhone?: string;

    @ManyToOne(
        (_type) => Governorate,
        (governorate) => governorate.serviceProviders,
    )
    @Expose()
    @IsOptional()
    governorate?: Governorate;

    @ManyToOne((_type) => City, (city) => city.serviceProviders)
    @Expose()
    @IsOptional()
    city?: City;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    area?: string;

    @Column({
        name: 'location',
        type: 'geometry',
        nullable: true,
        spatialFeatureType: 'Point',
        srid: 4326,
    })
    @Expose()
    @IsOptional()
    location?: IGeometry;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    website?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    commercialRecord?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    taxId?: string;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    kashfPercentage: number;

    @Column({
        nullable: true,
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    kashfDonationPercentage: number;

    @Column({ default: false })
    @Expose()
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @Column({ default: false })
    @Expose()
    @IsOptional()
    @IsBoolean()
    isFreeCall?: boolean;

    @Column({ default: false })
    @Expose()
    @IsOptional()
    @IsBoolean()
    isFreeVisit?: boolean;

    @Column('simple-array', { nullable: true })
    @Expose()
    @IsOptional()
    @IsArray()
    docs?: string[];

    @OneToOne((_type) => UserEntity, (user) => user.serviceProvider, {
        onDelete: 'CASCADE',
        cascade: ['insert'],
    })
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => UserEntity)
    @JoinColumn()
    user?: UserEntity;

    @ManyToOne(
        (_type) => ServiceProviderType,
        (serviceProviderType) => serviceProviderType.serviceProviders,
        {
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        },
    )
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => ServiceProviderType)
    serviceProviderType?: ServiceProviderType;

    @ManyToMany(
        () => InsuranceCompany,
        (insuranceCompany) => insuranceCompany.serviceProviders,
        { cascade: ['update'] },
    )
    @JoinTable({ name: 'provider_companies_company' })
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => InsuranceCompany)
    insuranceCompanies?: InsuranceCompany[];

    @ManyToMany(() => DoctorEntity, (doctor) => doctor.serviceProviders, {
        cascade: ['update'],
    })
    @JoinTable({ name: 'service_provider_doctors' })
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => DoctorEntity)
    doctors?: DoctorEntity[];

    @OneToMany(
        (_type) => DoctorEntity,
        (doctor) => doctor.onlineWithServiceProvider,
        {
            nullable: true,
        },
    )
    @Expose()
    @IsOptional()
    @Type(() => DoctorEntity)
    onlineDoctors?: DoctorEntity[];

    @OneToMany(
        (_type) => ScheduleEntity,
        (schedule) => schedule.serviceProvider,
        {
            cascade: true,
            onDelete: 'CASCADE',
        },
    )
    schedules: ScheduleEntity[];

    @OneToMany(
        (_type) => Transaction,
        (transaction) => transaction.serviceProvider,
        {
            cascade: true,
        },
    )
    transactions: Transaction[];

    @Expose()
    @IsArray()
    @IsOptional()
    @Type(() => ConsultationRequestEntity)
    @OneToMany(
        (_type) => ConsultationRequestEntity,
        (consultationRequest) => consultationRequest.serviceProvider,
        {
            cascade: true,
        },
    )
    consultationRequests?: ConsultationRequestEntity[];

    @OneToMany(
        (_type) => DoctorServiceProviderCheckInLogs,
        (doctorServiceProviderCheckInLogs) =>
            doctorServiceProviderCheckInLogs.serviceProvider,
        {
            cascade: true,
            onDelete: 'CASCADE',
        },
    )
    doctorCheckinLogs: DoctorServiceProviderCheckInLogs[];

    @Expose()
    @IsArray()
    @IsOptional()
    @Type(() => CustomNotification)
    @OneToMany(
        (_type) => CustomNotification,
        (customNotification) => customNotification.serviceProvider,
        {
            cascade: true,
        },
    )
    customNotifications?: CustomNotification[];

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    signupCode?: string;

    @Column({ default: false })
    @Expose()
    @IsOptional()
    @IsBoolean()
    isAcceptingDonation?: boolean;

    @OneToMany(() => Package, (pkg) => pkg.serviceProvider)
    packages: Package[];
}
