import {
    Body,
    Controller,
    Delete,
    ForbiddenException,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { AddServiceProviderDoctor } from './dto/add-service-provider-doctor.dto';
import { CreateServiceProviderResponseDto } from './dto/create-service-provider-response.dto';
import { CreateServiceProviderDto } from './dto/create-service-provider.dto';
import { DownloadServiceProviderDto } from './dto/download-service-provider.dto';
import { ServiceProviderDto } from './dto/service-provider.dto';
import { UpdateServiceProviderDoctor } from './dto/update-service-provider-doctor.dto';
import { UpdateServiceProviderDto } from './dto/update-service-provider.dto';
import { ServiceProvidersService } from './service-providers.service';

@Controller('service-providers')
@ApiTags('service-providers')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class ServiceProvidersController {
    constructor(
        private readonly serviceProvidersService: ServiceProvidersService,
    ) {}

    @Get('/download')
    @ApiResponse({
        description: 'Download service providers',
        type: [DownloadServiceProviderDto],
    })
    async downloadRequests(
        @Req() req: Request,
        @Res() res: Response,
        @I18nLang() lang: string,
        @Query('query') query: string,
    ): Promise<void> {
        const data = await this.serviceProvidersService.downloadServiceProviders(
            lang,
            query,
        );
        res.setHeader(
            'Content-disposition',
            'attachment; filename=requests.csv',
        );
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    /*********************** CRUD Operations *****************************/
    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        description: 'Get service provider',
        type: ServiceProviderDto,
    })
    async getServiceProvider(
        @Req() req: Request,
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<ServiceProviderDto> {
        return this.serviceProvidersService.getServiceProvider(
            id,
            req.user,
            lang,
        );
    }

    @Get()
    @ApiResponse({
        description: 'Get all service provider',
        type: [ServiceProviderDto],
    })
    async getAllServiceProviders(
        @Query('query') query: string,
        @I18nLang() lang: string,
    ): Promise<ServiceProviderDto[]> {
        return this.serviceProvidersService.getAllServiceProviders(query, lang);
    }

    @Post()
    @ApiResponse({
        description: 'Create service provider',
        type: CreateServiceProviderResponseDto,
    })
    async createServiceProvider(
        @Body() createServiceProviderDto: CreateServiceProviderDto,
        @I18nLang() lang: string,
    ): Promise<CreateServiceProviderResponseDto> {
        return this.serviceProvidersService.createServiceProvider(
            createServiceProviderDto,
            lang,
        );
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN, RoleType.SERVICE_PROVIDER)
    @ApiResponse({
        description: 'Update service provider',
        type: BasicOperationsResponse,
    })
    async updateServiceProvider(
        @Param('id') id: number,
        @Body() updateServiceProviderDto: UpdateServiceProviderDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.serviceProvidersService.updateServiceProvider(
            id,
            updateServiceProviderDto,
            lang,
        );
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Delete service provider',
        type: BasicOperationsResponse,
    })
    async deleteServiceProvider(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.serviceProvidersService.deleteServiceProvider(id, lang);
    }
    /*****************************Service Provider Doctors************************************/
    @Post(':id/doctors/:doctorId/schedule')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN, RoleType.SERVICE_PROVIDER)
    @ApiResponse({
        description: 'Assign Doctor to service provider',
        type: BasicOperationsResponse,
    })
    async assignDoctorServiceProvider(
        @Req() req: Request,
        @Param('id') id: number,
        @Param('doctorId') doctorId: number,
        @Body() addServiceProviderDoctor: AddServiceProviderDoctor,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        if (
            req.user.role === RoleType.SERVICE_PROVIDER &&
            id !== req.user.serviceProvider.id
        ) {
            throw new ForbiddenException();
        }

        return this.serviceProvidersService.assignDoctorServiceProvider(
            id,
            doctorId,
            addServiceProviderDoctor,
            lang,
        );
    }
    @Put(':id/doctors/:doctorId/schedule')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN, RoleType.SERVICE_PROVIDER)
    @ApiResponse({
        description: 'Update service provider Doctor schedule',
        type: BasicOperationsResponse,
    })
    async updateDoctorServiceProvider(
        @Req() req: Request,
        @Body() updateServiceProviderDoctor: UpdateServiceProviderDoctor,
        @Param('id') id: number,
        @Param('doctorId') doctorId: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        if (
            req.user.role === RoleType.SERVICE_PROVIDER &&
            id !== req.user.serviceProvider.id
        ) {
            throw new ForbiddenException();
        }

        return this.serviceProvidersService.updateDoctorServiceProvider(
            id,
            doctorId,
            updateServiceProviderDoctor,
            lang,
        );
    }
    @Delete(':id/doctors/:doctorId')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN, RoleType.SERVICE_PROVIDER)
    @ApiResponse({
        description: 'Delete Doctor from service provider',
        type: BasicOperationsResponse,
    })
    async deleteDoctorServiceProvider(
        @Req() req: Request,
        @Param('id') id: number,
        @Param('doctorId') doctorId: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        if (
            req.user.role === RoleType.SERVICE_PROVIDER &&
            id !== req.user.serviceProvider.id
        ) {
            throw new ForbiddenException();
        }
        return this.serviceProvidersService.deleteDoctorServiceProvider(
            id,
            doctorId,
            lang,
        );
    }
}
