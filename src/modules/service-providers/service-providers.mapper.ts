/* eslint-disable complexity */
import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { HelperService } from '../../shared/services/helper';
import { City } from '../cities/entities/city.entity';
import { Governorate } from '../governorates/entities/governorate.entity';
import { InsuranceCompany } from '../insurance-companies/entities/insurance-company.entity';
import { ServiceProviderType } from '../service-provider-types/entities/service-provider-type.entity';
import { CreateServiceProviderDto } from './dto/create-service-provider.dto';
import { DownloadServiceProviderDto } from './dto/download-service-provider.dto';
import { ServiceProviderDto } from './dto/service-provider.dto';
import { UpdateServiceProviderDto } from './dto/update-service-provider.dto';
import { ServiceProvider } from './entities/service-provider.entity';

type ServiceProviderDTOs =
    | CreateServiceProviderDto
    | UpdateServiceProviderDto
    | ServiceProviderDto;

@Injectable()
export class ServiceProvidersMapper extends AbstractMapper<
    ServiceProviderDto,
    ServiceProvider
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }

    fromDTOToEntity(
        destination: ClassType<ServiceProvider>,
        sourceObject: ServiceProviderDTOs,
    ): ServiceProvider {
        const serviceProviderEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(serviceProviderEntity);
        if (
            sourceObject instanceof CreateServiceProviderDto ||
            sourceObject instanceof UpdateServiceProviderDto
        ) {
            if (sourceObject.governorateId) {
                const governorate = new Governorate();
                governorate.id = sourceObject.governorateId;
                serviceProviderEntity.governorate = governorate;
            }
            if (sourceObject.cityId) {
                const city = new City();
                city.id = sourceObject.cityId;
                serviceProviderEntity.city = city;
            }
        }

        if (sourceObject instanceof CreateServiceProviderDto) {
            // create service provider type from the passed id
            const serviceProviderType = new ServiceProviderType();
            serviceProviderType.id = sourceObject.serviceProviderTypeId;
            serviceProviderEntity.serviceProviderType = serviceProviderType;

            // create IGeometry point from ILocation(long,lat) object
            if (sourceObject.location) {
                const { latitude, longitude } = sourceObject.location;
                serviceProviderEntity.location = {
                    type: 'Point',
                    coordinates: [longitude, latitude],
                };
            }
        } else if (sourceObject instanceof UpdateServiceProviderDto) {
            if (sourceObject.serviceProviderTypeId) {
                // create service provider type from the passed id
                const serviceProviderType = new ServiceProviderType();
                serviceProviderType.id = sourceObject.serviceProviderTypeId;
                serviceProviderEntity.serviceProviderType = serviceProviderType;
            }
            // create insurance companies from insuranceCompaniesIds array
            if (sourceObject.insuranceCompaniesIds) {
                serviceProviderEntity.insuranceCompanies = sourceObject.insuranceCompaniesIds.map(
                    (insuranceCompanyId) => {
                        const insuranceCompany = new InsuranceCompany();
                        insuranceCompany.id = insuranceCompanyId;
                        return insuranceCompany;
                    },
                );
            }
        }
        return serviceProviderEntity;
    }

    fromEntityToDTO(
        destination: ClassType<ServiceProviderDto>,
        sourceObject: ServiceProvider,
        lang: string,
    ): ServiceProviderDto {
        const serviceProviderDto = super.fromEntityToDTO(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(serviceProviderDto);

        // create ILocation(long,lat) object from IGeometry point
        if (serviceProviderDto.location) {
            serviceProviderDto.location = {
                latitude: sourceObject.location.coordinates[1],
                longitude: sourceObject.location.coordinates[0],
            };
        }

        if (sourceObject.city) {
            let cityName = sourceObject.city.name;
            if (lang === AvailableLanguageCodes.en) {
                cityName = sourceObject.city.nameEn;
            }
            serviceProviderDto.city = {
                id: sourceObject.city.id,
                name: cityName,
            };
        }
        if (sourceObject.governorate) {
            let governorateName = sourceObject.governorate.name;
            if (lang === AvailableLanguageCodes.en) {
                governorateName = sourceObject.governorate.nameEn;
            }

            serviceProviderDto.governorate = {
                id: sourceObject.governorate.id,
                name: governorateName,
            };
        }
        return serviceProviderDto;
    }

    fromEntityToDownloadData(
        destination: ClassType<DownloadServiceProviderDto>,
        sourceObject: ServiceProvider,
        lang: string,
    ): DownloadServiceProviderDto {
        return {
            id: sourceObject.id,
            area: sourceObject.area,
            coordinatorPhone: sourceObject.coordinatorPhone,
            email: sourceObject.user.email,
            name: sourceObject.user.translations.filter(
                (item) => item.languageCode === lang,
            )[0].name,
            type: sourceObject.serviceProviderType.translations.filter(
                (item) => item.languageCode === lang,
            )[0].title,
        };
    }
}
