import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SharedModule } from '../../shared/shared.module';
import { AuthModule } from '../auth/auth.module';
import { DoctorsModule } from '../doctors/doctors.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { SchedulesDaysRepository } from '../schedules/repositories/schedules.days.repository';
import { SchedulesRepository } from '../schedules/repositories/schedules.repository';
import { SchedulesModule } from '../schedules/schedules.module';
import { UsersModule } from '../users/users.module';
import { ServiceProvider } from './entities/service-provider.entity';
import { ServiceProvidersRepository } from './repositories/service-providers.repository';
import { ServiceProvidersController } from './service-providers.controller';
import { ServiceProvidersMapper } from './service-providers.mapper';
import { ServiceProvidersService } from './service-providers.service';

@Module({
    imports: [
        TypeOrmModule.forFeature([ServiceProvider]),
        UsersModule,
        AuthModule,
        SharedModule,
        forwardRef(() => DoctorsModule),
        forwardRef(() => SchedulesModule),
        forwardRef(() => NotificationsModule),
    ],
    controllers: [ServiceProvidersController],
    exports: [ServiceProvidersService],
    providers: [
        ServiceProvidersService,
        ServiceProvidersMapper,
        ServiceProvidersRepository,
        SchedulesDaysRepository,
        SchedulesRepository,
    ],
})
export class ServiceProvidersModule {}
