import {
    BadRequestException,
    forwardRef,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { I18nService } from 'nestjs-i18n';
import { SelectQueryBuilder } from 'typeorm';

import { RoleType, SortByType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { DataDownloadService } from '../../shared/services/data-download.service';
import { EmailService } from '../../shared/services/email.service';
import { DebugLogger } from '../../shared/services/logger.service';
import { AuthService } from '../auth/auth.service';
import { AuthMessagesKeys } from '../auth/translate.enum';
import { NotificationTyps } from '../consultation-requests/notification.enum';
import { DoctorsService } from '../doctors/doctors.service';
import { DoctorEntity } from '../doctors/entities/doctor.entity';
import { CreateNotificationDto } from '../notifications/dto/create-notification.dto';
import { NotificationsService } from '../notifications/notifications.service';
import { ScheduleEntity } from '../schedules/entities/schedule.entity';
import { SchedulesDaysRepository } from '../schedules/repositories/schedules.days.repository';
import { SchedulesRepository } from '../schedules/repositories/schedules.repository';
import { UserDetailsDto } from '../users/dto/userDetails.dto';
import { UserEntity } from '../users/entities/user.entity';
import { UsersMapper } from '../users/users.mapper';
import { UsersService } from '../users/users.service';
import { AddServiceProviderDoctor } from './dto/add-service-provider-doctor.dto';
import { CreateServiceProviderResponseDto } from './dto/create-service-provider-response.dto';
import { CreateServiceProviderDto } from './dto/create-service-provider.dto';
import { DownloadServiceProviderDto } from './dto/download-service-provider.dto';
import { ServiceProviderScheduleDto } from './dto/service-provider-doctor-schedule.dto';
import { ServiceProviderDto } from './dto/service-provider.dto';
import { UpdateServiceProviderDoctor } from './dto/update-service-provider-doctor.dto';
import { UpdateServiceProviderDto } from './dto/update-service-provider.dto';
import { ServiceProvider } from './entities/service-provider.entity';
import { ServiceProvidersRepository } from './repositories/service-providers.repository';
import { ServiceProvidersMapper } from './service-providers.mapper';
import { ServiceProviderMessagesKeys } from './translate.enum';

@Injectable()
export class ServiceProvidersService {
    constructor(
        private readonly usersMapper: UsersMapper,
        private readonly usersService: UsersService,
        private readonly serviceProvidersRepository: ServiceProvidersRepository,
        private readonly serviceProvidersMapper: ServiceProvidersMapper,
        private readonly authService: AuthService,
        @Inject(forwardRef(() => DoctorsService))
        private readonly doctorsService: DoctorsService,
        private readonly schedulesRepository: SchedulesRepository,
        private readonly schedulesDaysRepository: SchedulesDaysRepository,
        private readonly i18n: I18nService,
        private readonly emailService: EmailService,
        @Inject(forwardRef(() => NotificationsService))
        private readonly notificationsService: NotificationsService,
        private readonly dataDownloadService: DataDownloadService,
        private logger: DebugLogger,
    ) { }

    async getServiceProvider(
        id: number,
        user: UserDetailsDto | null,
        lang: string,
    ): Promise<ServiceProviderDto> {
        const selection = [
            'serviceProvider.id',
            'serviceProvider.landlinePhone',
            'serviceProvider.location',
            'serviceProvider.isActive',
            'serviceProvider.isAcceptingDonation',
            'serviceProvider.website',
            'serviceProvider.coordinatorName',
            'serviceProvider.coordinatorPhone',
            'serviceProvider.commercialRecord',
            'serviceProvider.taxId',
            'serviceProvider.kashfPercentage',
            'serviceProvider.instapayUrl',
            'serviceProvider.docs',
            'serviceProvider.area',
            'user.phone',
            'user.email',
            'user.avatar',
            'userTranslations',
            'insuranceCompanies.id',
            'insuranceCompanies.logoURL',
            'serviceProviderType.id',
            'serviceProviderTypeTranslations',
            'governorate',
            'city',
        ];

        if (
            user &&
            (user.role === RoleType.ADMIN ||
                user.role === RoleType.SUPER_ADMIN ||
                (user.role === RoleType.SERVICE_PROVIDER &&
                    user.serviceProvider.id === id))
        ) {
            selection.push('serviceProvider.signupCode');
        }
        const query = this.serviceProvidersRepository
            .createQueryBuilder('serviceProvider')
            .leftJoinAndSelect(
                'serviceProvider.serviceProviderType',
                'serviceProviderType',
            )
            .leftJoinAndSelect(
                'serviceProviderType.translations',
                'serviceProviderTypeTranslations',
                'serviceProviderTypeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect(
                'serviceProvider.insuranceCompanies',
                'insuranceCompanies',
            )
            .leftJoinAndSelect('serviceProvider.user', 'user')
            .leftJoinAndSelect('user.translations', 'userTranslations')
            .leftJoinAndSelect('serviceProvider.governorate', 'governorate')
            .leftJoinAndSelect('serviceProvider.city', 'city')
            .select(selection)
            .where('serviceProvider.id = :id', {
                id,
            });

        const serviceProvider = await query.getOne();

        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        let isCheckedIn = false;
        if (user && user.role === RoleType.DOCTOR) {
            const lastCheckIn = await this.doctorsService.getLastCheckIn(
                user.doctor.id,
                id,
            );
            if (lastCheckIn && lastCheckIn.isCheckedIn === true) {
                isCheckedIn = true;
            }
        }
        const serviceProviderDto = this.serviceProvidersMapper.fromEntityToDTO(
            ServiceProviderDto,
            serviceProvider,
            lang,
        );

        serviceProviderDto.isCheckedIn = isCheckedIn;

        if (
            user &&
            !(
                user.role === RoleType.ADMIN ||
                user.role === RoleType.SUPER_ADMIN ||
                (user.role === RoleType.SERVICE_PROVIDER &&
                    user.serviceProvider.id === id)
            )
        ) {
            delete serviceProviderDto.signupCode;
        }

        return serviceProviderDto;
    }

    buildGetServiceProvidersQuery(
        query: SelectQueryBuilder<ServiceProvider>,
        httpQueryString: string,
    ): SelectQueryBuilder<ServiceProvider> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }
        if (httpQueryObject.search) {
            query
                .orWhere('(serviceProvider.area)  ILIKE :searchKey', {
                    searchKey: `%${httpQueryObject.search.value}%`,
                })
                .orWhere(
                    '(serviceProvider.id || serviceProvider.coordinatorPhone)  ILIKE :searchKey',
                    {
                        searchKey: `%${httpQueryObject.search.value}%`,
                    },
                )
                .orWhere(
                    '(serviceProviderTypeTranslations.title)  ILIKE :searchKey',
                    {
                        searchKey: `%${httpQueryObject.search.value}%`,
                    },
                )
                .orWhere('(user.email)  ILIKE :searchKey', {
                    searchKey: `%${httpQueryObject.search.value}%`,
                })
                .orWhere('(userTranslations.name)  ILIKE :searchKey', {
                    searchKey: `%${httpQueryObject.search.value}%`,
                });
        }

        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.NAME:
                    query.orderBy(
                        'userTranslations.name',
                        httpQueryObject.sort.type,
                    );
            }
        }

        return query;
    }

    async getAllServiceProviders(
        httpQueryString: string,
        lang: string,
    ): Promise<ServiceProviderDto[]> {
        const serviceProviders = await this.listServiceProviders(
            httpQueryString,
            lang,
        );
        return serviceProviders.map((serviceProvider) => {
            const serviceProviderDto =
                this.serviceProvidersMapper.fromEntityToDTO(
                    ServiceProviderDto,
                    serviceProvider,
                    lang,
                );
            serviceProviderDto.user.name =
                serviceProviderDto.user.translations[0].name;
            serviceProviderDto.serviceProviderType.title =
                serviceProviderDto.serviceProviderType.translations[0].title;
            delete serviceProviderDto.user.translations;
            delete serviceProviderDto.serviceProviderType.translations;
            return serviceProviderDto;
        });
    }

    async listServiceProviders(
        httpQueryString: string,
        lang: string,
    ): Promise<ServiceProvider[]> {
        let query = this.serviceProvidersRepository
            .createQueryBuilder('serviceProvider')
            .leftJoinAndSelect(
                'serviceProvider.serviceProviderType',
                'serviceProviderType',
            )
            .leftJoinAndSelect(
                'serviceProviderType.translations',
                'serviceProviderTypeTranslations',
                'serviceProviderTypeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('serviceProvider.user', 'user')
            .leftJoinAndSelect(
                'user.translations',
                'userTranslations',
                'userTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('serviceProvider.governorate', 'governorate')
            .leftJoinAndSelect('serviceProvider.city', 'city')
            .select([
                'serviceProvider.id',
                'serviceProvider.governorate',
                'serviceProvider.city',
                'serviceProvider.area',
                'serviceProvider.isActive',
                'serviceProvider.isFreeCall',
                'serviceProvider.isFreeVisit',
                'serviceProvider.isAcceptingDonation',
                'user.phone',
                'user.email',
                'userTranslations',
                'serviceProvider.coordinatorName',
                'serviceProvider.coordinatorPhone',
                'serviceProviderType.id',
                'serviceProviderTypeTranslations',
                'governorate',
                'city',
            ]);

        if (httpQueryString) {
            query = this.buildGetServiceProvidersQuery(query, httpQueryString);
        }
        return query.getMany();
    }

    async createServiceProvider(
        createServiceProviderDto: CreateServiceProviderDto,
        lang: string,
    ): Promise<CreateServiceProviderResponseDto> {
        await this.usersService.checkUserExistence(
            createServiceProviderDto,
            lang,
            RoleType.SERVICE_PROVIDER,
        );
        const userEntity = this.usersMapper.fromDTOToEntity(
            UserEntity,
            createServiceProviderDto,
        );

        const serviceProviderEntity =
            this.serviceProvidersMapper.fromDTOToEntity(
                ServiceProvider,
                createServiceProviderDto,
            );

        userEntity.role = RoleType.SERVICE_PROVIDER;
        serviceProviderEntity.user = userEntity;
        const serviceProvider = this.serviceProvidersRepository.create(
            serviceProviderEntity,
        );

        const createdServiceProvider =
            await this.serviceProvidersRepository.save(serviceProvider);

        const tokenPayload = this.authService.generateTokens(
            createdServiceProvider.user.id,
            createdServiceProvider.user.role,
        );

        const header = await this.i18n.translate(
            ServiceProviderMessagesKeys.EMAIL_SERVICE_PROVIDER_REGISTERED_HEADER,
            { lang },
        );
        const body = await this.i18n.translate(
            ServiceProviderMessagesKeys.EMAIL_SERVICE_PROVIDER_REGISTERED_MESSAGE,
            { lang },
        );

        await Promise.all([
            this.usersService.updateRefreshToken(
                createdServiceProvider.user.id,
                tokenPayload.refreshToken,
            ),
            this.emailService.sendEmail(
                userEntity.email,
                'Pending Registration',
                'we received your registration request, pending Activation',
            ),
            this.usersService.confirmEmail(
                createdServiceProvider.user.id,
                lang,
            ),
            this.usersService.sendEmailToAdminOnServiceRegistration(lang),
            this.usersService.sendRegistrationEmail(
                createdServiceProvider.user.id,
                header,
                body,
                lang,
            ),
        ]);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),
            serviceProviderId: createdServiceProvider.id,
            userId: createdServiceProvider.user.id,
            token: tokenPayload,
        };
    }

    async updateServiceProvider(
        id: number,
        updateServiceProviderDto: UpdateServiceProviderDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const query = this.serviceProvidersRepository
            .createQueryBuilder('serviceProvider')
            .leftJoinAndSelect('serviceProvider.user', 'user')
            .leftJoinAndSelect(
                'user.translations',
                'userTranslations',
                'userTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect(
                'serviceProvider.insuranceCompanies',
                'insuranceCompanies',
            )
            .where('serviceProvider.id = :id', {
                id,
            });

        const serviceProvider = await query.getOne();
        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const userEntity = this.usersMapper.fromDTOToEntity(
            UserEntity,
            updateServiceProviderDto,
        );

        const serviceProviderEntity =
            this.serviceProvidersMapper.fromDTOToEntity(
                ServiceProvider,
                updateServiceProviderDto,
            );
        if (updateServiceProviderDto.password) {
            userEntity.password = bcrypt.hashSync(
                updateServiceProviderDto.password,
                10,
            );
        }
        // update insuranceCompanies
        if (updateServiceProviderDto.insuranceCompaniesIds) {
            await this.serviceProvidersRepository
                .createQueryBuilder()
                .relation('insuranceCompanies')
                .of(serviceProvider)
                .addAndRemove(
                    serviceProviderEntity.insuranceCompanies,
                    serviceProvider.insuranceCompanies,
                );
            delete serviceProviderEntity.insuranceCompanies;
        }
        if (serviceProviderEntity.isActive) {
            await this.emailService
                .sendEmail(
                    serviceProvider.user.email,
                    'Account Activated',
                    'your kashf service provider account is now active',
                )
                .catch((error) => console.error(error));
        }
        this.logger.debug(serviceProviderEntity);
        // TODO: use typeorm relation to update it in a better way
        await Promise.all([
            this.serviceProvidersRepository.update(
                { id },
                serviceProviderEntity,
            ),
            this.usersService.updateUser(
                serviceProvider.user.id,
                userEntity,
                lang,
            ),
        ]);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async deleteServiceProvider(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { id },
        });
        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        await this.serviceProvidersRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async assignDoctorServiceProvider(
        id: number,
        doctorId: number,
        addServiceProviderDoctor: AddServiceProviderDoctor,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const { serviceProviderSchedule } = addServiceProviderDoctor;
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { id },
            relations: ['user', 'user.translations'],
        });
        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const doctor = await this.doctorsService.findDoctor(doctorId);
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const serviceProviderExists = doctor.serviceProviders.find(
            (currentServiceProvider) =>
                currentServiceProvider.id === serviceProvider.id,
        );
        if (serviceProviderExists) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_DOCTOR_ALREADY_EXISTS,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.BAD_REQUEST,
            );
        }
        await Promise.all(
            serviceProviderSchedule.map(async (schedule) => {
                await this.createSchedule(doctorId, id, schedule);
            }),
        );
        await this.notifyDoctorToVerifyServiceProvider(
            id,
            doctor,
            serviceProvider,
        );
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.ADD_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async downloadServiceProviders(
        lang: string,
        httpQueryString: string,
    ): Promise<string> {
        const serviceProviders = await this.listServiceProviders(
            httpQueryString,
            lang,
        );
        const mappedServiceProviders = serviceProviders.map((request) =>
            this.serviceProvidersMapper.fromEntityToDownloadData(
                DownloadServiceProviderDto,
                request,
                lang,
            ),
        );

        const fields = [
            'id',
            'name',
            'type',
            'email',
            'coordinatorPhone',
            'area',
        ];
        return this.dataDownloadService.downloadCsv(
            fields,
            mappedServiceProviders,
        );
    }

    async notifyDoctorToVerifyServiceProvider(
        id: number,
        doctor: DoctorEntity,
        serviceProvider: ServiceProvider,
    ): Promise<void> {
        this.emailService
            .sendEmail(
                doctor.user.email,
                'Verification Email',
                'you have been added to service provider',
            )
            .catch((error) => console.error(error));
        let serviceProviderNameAr = '';
        let serviceProviderNameEn = '';
        serviceProvider.user.translations.forEach((user) => {
            if (user.languageCode === AvailableLanguageCodes.ar) {
                serviceProviderNameAr = user.name;
            } else if (user.languageCode === AvailableLanguageCodes.en) {
                serviceProviderNameEn = user.name;
            }
        });
        const notification: CreateNotificationDto = {
            userId: doctor.user.id,
            data: {
                doctorId: doctor.id.toString(),
                serviceProviderId: id.toString(),
                type: NotificationTyps.VERIFY_SERVICE_PROVIDER,
            },
            notificationTranslations: [
                {
                    title: 'تم اضافتك من قبل مقدم الخدمه',
                    body: `${serviceProviderNameAr} قام باضافه الملف الشخصي الخاص بك برجاء التاكيد حتي يتم تفعيل`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'You Have been added to a service provider',
                    body: `${serviceProviderNameEn} has asked to add your profile. Please Confirm to active your Profile`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                doctor.user.id,
                doctor.user.appLanguage,
            )
            .catch((error) => console.error(error));
    }

    async notifyDoctorToVerifySchedule(
        id: number,
        doctor: DoctorEntity,
        serviceProvider: ServiceProvider,
    ): Promise<void> {
        let serviceProviderNameAr = '';
        let serviceProviderNameEn = '';
        serviceProvider.user.translations.forEach((user) => {
            if (user.languageCode === AvailableLanguageCodes.ar) {
                serviceProviderNameAr = user.name;
            } else if (user.languageCode === AvailableLanguageCodes.en) {
                serviceProviderNameEn = user.name;
            }
        });
        const notification: CreateNotificationDto = {
            userId: doctor.user.id,
            data: {
                doctorId: doctor.id.toString(),
                serviceProviderId: id.toString(),
                type: NotificationTyps.VERIFY_SCHEDULE,
            },
            notificationTranslations: [
                {
                    title: 'تم اضافتك من قبل مقدم الخدمه',
                    body: `${serviceProviderNameAr} قام باضافه الملف الشخصي الخاص بك واضافه الجدول الزمني`,
                    languageCode: AvailableLanguageCodes.ar,
                },
                {
                    title: 'You Have been added to a service provider',
                    body: `${serviceProviderNameEn} has asked to add your profile. Please Confirm to active your Schedule`,
                    languageCode: AvailableLanguageCodes.en,
                },
            ],
        };
        const createdNotification =
            await this.notificationsService.createNotification(notification);
        this.notificationsService
            .sendNotification(
                createdNotification.createdId,
                doctor.user.id,
                doctor.user.appLanguage,
            )
            .catch((error) => console.error(error));
    }

    async updateDoctorServiceProvider(
        id: number,
        doctorId: number,
        updateServiceProviderDoctor: UpdateServiceProviderDoctor,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const { serviceProviderSchedule } = updateServiceProviderDoctor;
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { id },
        });
        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const doctor = await this.doctorsService.findDoctor(doctorId);
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        const schedule = await this.schedulesRepository.find({
            where: {
                doctor: {
                    id: doctor.id,
                },
                serviceProvider: {
                    id: serviceProvider.id,
                },
            },
        });

        this.logger.log(JSON.stringify(schedule));

        if (schedule.length > 0) {
            await Promise.all(
                serviceProviderSchedule.map(async (scheduleToUpdate) => {
                    await this.updateSchedule(doctorId, id, scheduleToUpdate);
                }),
            );
            this.emailService
                .sendEmail(
                    doctor.user.email,
                    'upddate schedule',
                    'your service provider schedule has been updated',
                )
                .catch((error) => console.error(error));
        } else {
            await Promise.all(
                serviceProviderSchedule.map(async (scheduleToUpdate) => {
                    await this.createSchedule(doctorId, id, scheduleToUpdate);
                }),
            );
        }

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.ADD_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async deleteDoctorServiceProvider(
        id: number,
        doctorId: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { id },
        });
        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const doctor = await this.doctorsService.findDoctor(doctorId);
        if (!doctor) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        await Promise.all([
            this.serviceProvidersRepository
                .createQueryBuilder()
                .relation('doctors')
                .of(serviceProvider)
                .remove(doctorId),
            this.schedulesRepository.delete({
                doctor: { id: doctorId },
                serviceProvider: { id },
            }),
        ]);
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                ServiceProviderMessagesKeys.DELETE_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    private async createSchedule(
        doctorId: number,
        serviceProviderId: number,
        schedule: ServiceProviderScheduleDto,
    ) {
        const doctor = new DoctorEntity();
        doctor.id = doctorId;

        const serviceProvider = new ServiceProvider();
        serviceProvider.id = serviceProviderId;
        const createdSchedule = await this.schedulesRepository.insert({
            doctor,
            serviceProvider,
            type: schedule.type,
            fees: schedule.fees,
        });

        const newSchedule = new ScheduleEntity();
        newSchedule.id = createdSchedule.raw[0].id;

        await Promise.all(
            schedule.workingDays.map(async (day) => {
                await this.schedulesDaysRepository.insert({
                    schedule: newSchedule,
                    from: day.from,
                    to: day.to,
                    order: day.order,
                    day: day.day,
                    breakFrom: day.breakFrom,
                    breakTo: day.breakTo,
                    isActive: day.isActive,
                    containsBreak: day.containsBreak,
                });
            }),
        );
    }

    private async updateSchedule(
        doctorId: number,
        serviceProviderId: number,
        schedule: ServiceProviderScheduleDto,
    ) {
        await this.schedulesRepository.update(
            {
                id: schedule.id,
                doctor: {
                    id: doctorId,
                },
                serviceProvider: {
                    id: serviceProviderId,
                },
            },
            {
                fees: schedule.fees,
            },
        );

        await Promise.all(
            schedule.workingDays.map(async (day) => {
                await this.schedulesDaysRepository.update(
                    { id: day.id, schedule: { id: schedule.id } },
                    {
                        from: day.from,
                        to: day.to,
                        breakFrom: day.breakFrom,
                        breakTo: day.breakTo,
                        isActive: day.isActive,
                        containsBreak: day.containsBreak,
                    },
                );
            }),
        );
    }

    async isAcceptingDonation(id: number, lang: string): Promise<void> {
        const serviceProvider = await this.serviceProvidersRepository.findOne({
            where: { id },
        });

        if (!serviceProvider) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        if (!serviceProvider.isAcceptingDonation) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        ServiceProviderMessagesKeys.SERVICE_PROVIDER_IS_NOT_VALID_FOR_DONATION,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
    }
}
