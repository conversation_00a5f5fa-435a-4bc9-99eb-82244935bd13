export enum ServiceProviderMessagesKeys {
    SERVICE_PROVIDER_NOT_FOUND = 'service-provider.SERVICE_PROVIDER_NOT_FOUND',
    SERVICE_PROVIDER_IS_NOT_VALID_FOR_DONATION = 'service-provider.IS_NOT_VALID_FOR_DONATION',
    CREATED_SUCCESSFULLY = 'service-provider.CREATED_SUCCESSFULLY',
    UPDATED_SUCCESSFULY = 'service-provider.UPDATED_SUCCESSFULY',
    DELETED_SUCCESSFULY = 'service-provider.DELETED_SUCCESSFULY',
    ADD_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY = 'service-provider.ADD_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY',
    SERVICE_PROVIDER_DOCTOR_CHECK_IN_SUCCESSFULY = 'service-provider.SERVICE_PROVIDER_DOCTOR_CHECK_IN_SUCCESSFULY',
    UPDATED_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY = 'service-provider.UPDATED_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY',
    DELETE_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY = 'service-provider.DELETE_SERVICE_PROVIDER_DOCTOR_SUCCESSFULY',
    SERVICE_PROVIDER_DOCTOR_ALREADY_EXISTS = 'service-provider.SERVICE_PROVIDER_DOCTOR_ALREADY_EXISTS',
    SERVICE_PROVIDER_DOCTOR_NOT_EXISTS = 'service-provider.SERVICE_PROVIDER_DOCTOR_NOT_EXISTS',
    EMAIL_SERVICE_PROVIDER_REGISTERED_HEADER = 'service-provider.EMAIL_SERVICE_PROVIDER_REGISTERED_HEADER',
    EMAIL_SERVICE_PROVIDER_REGISTERED_MESSAGE = 'service-provider.EMAIL_SERVICE_PROVIDER_REGISTERED_MESSAGE',
}
