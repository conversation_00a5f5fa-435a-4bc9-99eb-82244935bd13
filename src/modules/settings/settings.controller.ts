import {
    Body,
    Controller,
    Get,
    Post,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    <PERSON>pi<PERSON><PERSON><PERSON>,
    ApiHeader,
    ApiBearerAuth,
    ApiResponse,
} from '@nestjs/swagger';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { SettingsService } from './settings.service';
import Settings from './settings';
import SettingsDTO from './dto/update-settings.dto';
import { AuthGuard } from '../../guards/auth.guard';
import { RoleType } from '../../common/constants/types';
import { Roles } from '../../decorators/roles.decorator';
import { RolesGuard } from '../../guards/roles.guard';

@Controller('settings')
@ApiTags('settings')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class SettingsController {
    constructor(private readonly settingsService: SettingsService) {}

    @Get()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN, RoleType.ADMIN)
    @ApiResponse({
        description: 'get settings',
        type: Settings,
    })
    get(): Promise<Settings> {
        return this.settingsService.get();
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN, RoleType.ADMIN)
    async update(@Body() dto: SettingsDTO): Promise<Settings> {
        return this.settingsService.update(dto);
    }
}
