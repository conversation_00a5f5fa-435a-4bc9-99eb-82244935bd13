import { Injectable } from '@nestjs/common';
import SettingsDTO from './dto/update-settings.dto';
import * as admin from 'firebase-admin';
import Settings from './settings';
@Injectable()
export class SettingsService {
    private settings: Settings;
    private isFetched: boolean = false;

    get kashf_percentage(): number {
        return this.settings?.kashfPercentage || 0.2;
    }

    constructor() {
        this.settings = {};
    }

    async update(dto: SettingsDTO): Promise<Settings> {
        try {
            await admin
                .firestore()
                .collection('Settings')
                .doc('settings')
                .set({ ...dto }, { merge: true });

            Object.assign(this.settings, dto);
        } catch (e) {
            console.log(e);
        }

        return this.settings;
    }

    async get(): Promise<Settings> {
        if (!this.isFetched) {
            const doc = await admin
                .firestore()
                .collection('Settings')
                .doc('settings')
                .get();
            if (doc.exists) {
                this.isFetched = true;
                this.settings = doc.data() as Settings;
            }
        }
        return this.settings;
    }
}
