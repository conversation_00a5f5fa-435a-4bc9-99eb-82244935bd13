import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    ArrayMinSize,
    IsArray,
    IsOptional,
    ValidateNested,
} from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { SpecialityTranslation } from '../entities/speciality-translation.entity';

export class CreateSpecialityDto extends AbstractDto {
    @ApiProperty({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @IsArray()
    @ArrayMinSize(2)
    @Expose()
    @Type(() => SpecialityTranslation)
    translations: SpecialityTranslation[];

    @Expose()
    @IsOptional()
    parentId?: number;
}
