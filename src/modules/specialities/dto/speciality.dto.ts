'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { DoctorDto } from '../../doctors/dto/doctor.dto';
import { SpecialityTranslation } from '../entities/speciality-translation.entity';
export class SpecialityDto extends AbstractDto {
    @ApiPropertyOptional()
    @IsString()
    @Expose()
    @IsOptional()
    title?: string;

    @Expose()
    @IsOptional()
    parentId?: number;

    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => DoctorDto)
    doctors?: DoctorDto[];

    @ApiPropertyOptional()
    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => SpecialityTranslation)
    translations?: SpecialityTranslation[];

    @Expose()
    @IsOptional()
    parent?: SpecialityDto;

    @IsArray()
    @Expose()
    @IsOptional()
    children?: SpecialityDto[];
}
