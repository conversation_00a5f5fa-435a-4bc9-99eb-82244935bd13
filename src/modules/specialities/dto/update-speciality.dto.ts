import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { SpecialityTranslation } from '../entities/speciality-translation.entity';

export class UpdateSpecialityDto extends AbstractDto {
    @ApiPropertyOptional({ example: [{ title: 'title', languageCode: 'ar' }] })
    @ValidateNested()
    @Type(() => SpecialityTranslation)
    @Expose()
    @IsOptional()
    @IsArray()
    translations?: SpecialityTranslation[];

    @Expose()
    @IsOptional()
    parentId?: number;
}
