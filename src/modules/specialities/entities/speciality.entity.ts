import { Expose } from 'class-transformer';
import { IsArray, IsOptional } from 'class-validator';
import { Column, Entity, ManyToMany, ManyToOne, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { ConsultationRequestEntity } from '../../consultation-requests/entities/consultation-request.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { SpecialityTranslation } from './speciality-translation.entity';

@Entity('specialities')
export class Speciality extends AbstractEntity {
    @OneToMany(
        (_type) => SpecialityTranslation,
        (specialityTranslation) => specialityTranslation.speciality,
        {
            cascade: true,
        },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    translations: SpecialityTranslation[];

    @ManyToMany(() => DoctorEntity, (doctor) => doctor.specialities)
    @Expose()
    @IsOptional()
    @IsArray()
    doctors?: DoctorEntity[];

    @OneToMany(
        (_type) => ConsultationRequestEntity,
        (consultationRequest) => consultationRequest.choosenSpeciality,
    )
    consultationRequests?: ConsultationRequestEntity[];

    @ManyToOne(() => Speciality, { onDelete: 'SET NULL' })
    parent: Speciality;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    parentId: number;

    @OneToMany(() => Speciality, (speciality) => speciality.parent)
    children: Speciality[];
}
