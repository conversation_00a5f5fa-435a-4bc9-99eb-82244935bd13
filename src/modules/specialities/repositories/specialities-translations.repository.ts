import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { SpecialityTranslation } from '../entities/speciality-translation.entity';

@Injectable()
export class SpecialitiesTranslationsRepository extends Repository<
    SpecialityTranslation
> {
    constructor(dataSource: DataSource) {
        super(SpecialityTranslation, dataSource.createEntityManager());
    }
}
