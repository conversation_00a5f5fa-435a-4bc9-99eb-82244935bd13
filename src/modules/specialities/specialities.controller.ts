'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    Headers,
    Param,
    Post,
    Put,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { I18nLang } from 'nestjs-i18n';

import { RoleType, SourceType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { CreateSpecialityDto } from './dto/create-speciality.dto';
import { SpecialityDto } from './dto/speciality.dto';
import { UpdateSpecialityDto } from './dto/update-speciality.dto';
import { SpecialitiesService } from './specialities.service';

@Controller('specialities')
@ApiTags('specialities')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class SpecialitiesController {
    constructor(private specialitiesService: SpecialitiesService) {}

    @Get(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Get speciality',
        type: SpecialityDto,
    })
    async getSpeciality(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<SpecialityDto> {
        return this.specialitiesService.getSpeciality(id, lang);
    }

    @Get()
    @ApiResponse({
        description: 'Get all specialities',
        type: [SpecialityDto],
    })
    getAllSpecialities(
        @Headers('source-type') source: SourceType,
        @I18nLang() lang: string,
    ): Promise<SpecialityDto[]> {
        if (source && source === SourceType.ADMIN) {
            return this.specialitiesService.getAllSpecialitiesForAdmin();
        }
        return this.specialitiesService.getAllSpecialities(lang);
    }

    @Post()
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Create speciality',
        type: CreateOperationsResponse,
    })
    createSpeciality(
        @Body() createSpecialityDto: CreateSpecialityDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.specialitiesService.createSpeciality(
            createSpecialityDto,
            lang,
        );
    }

    @Put(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'Update speciality',
        type: BasicOperationsResponse,
    })
    async updateSpeciality(
        @Param('id') id: number,
        @Body() updateSpecialityDto: UpdateSpecialityDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.specialitiesService.updateSpeciality(
            id,
            updateSpecialityDto,
            lang,
        );
    }

    @Delete(':id')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.ADMIN, RoleType.SUPER_ADMIN)
    @ApiResponse({
        description: 'delete speciality',
        type: BasicOperationsResponse,
    })
    async deleteSpeciality(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.specialitiesService.deleteSpeciality(id, lang);
    }
}
