import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { HelperService } from '../../shared/services/helper';
import { CreateSpecialityDto } from './dto/create-speciality.dto';
import { SpecialityDto } from './dto/speciality.dto';
import { UpdateSpecialityDto } from './dto/update-speciality.dto';
import { Speciality } from './entities/speciality.entity';

type SpecialityDTOs = CreateSpecialityDto | UpdateSpecialityDto | SpecialityDto;

@Injectable()
export class SpecialitiesMapper extends AbstractMapper<
    SpecialityDTOs,
    Speciality
> {
    constructor(public readonly helperService: HelperService) {
        super();
    }
    fromDTOToEntity(
        destination: ClassType<Speciality>,
        sourceObject: SpecialityDTOs,
    ): Speciality {
        const specialityEntity = super.fromDTOToEntity(
            destination,
            sourceObject,
        );
        this.helperService.removeEmptyKeys(specialityEntity);
        return specialityEntity;
    }

    fromEntityToDTO(
        destination: ClassType<SpecialityDto>,
        sourceObject: Speciality,
    ): SpecialityDto {
        const specialityDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(specialityDto);
        return specialityDto;
    }
}
