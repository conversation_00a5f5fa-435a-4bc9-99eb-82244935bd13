import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SharedModule } from '../../shared/shared.module';
import { SpecialityTranslation } from './entities/speciality-translation.entity';
import { Speciality } from './entities/speciality.entity';
import { SpecialitiesTranslationsRepository } from './repositories/specialities-translations.repository';
import { SpecialitiesRepository } from './repositories/specialities.repository';
import { SpecialitiesController } from './specialities.controller';
import { SpecialitiesMapper } from './specialities.mapper';
import { SpecialitiesService } from './specialities.service';

@Module({
    imports: [
        TypeOrmModule.forFeature([Speciality, SpecialityTranslation]),
        SharedModule
    ],
    controllers: [SpecialitiesController],
    exports: [SpecialitiesService],
    providers: [
        SpecialitiesService,
        SpecialitiesMapper,
        SpecialitiesRepository,
        SpecialitiesTranslationsRepository,
    ],
})
export class SpecialitiesModule {}
