import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { HelperService } from '../../shared/services/helper';
import { CreateSpecialityDto } from './dto/create-speciality.dto';
import { SpecialityDto } from './dto/speciality.dto';
import { UpdateSpecialityDto } from './dto/update-speciality.dto';
import { SpecialityTranslation } from './entities/speciality-translation.entity';
import { Speciality } from './entities/speciality.entity';
import { SpecialitiesTranslationsRepository } from './repositories/specialities-translations.repository';
import { SpecialitiesRepository } from './repositories/specialities.repository';
import { SpecialitiesMapper } from './specialities.mapper';
import { SpecialityMessagesKeys } from './translate.enum';

@Injectable()
export class SpecialitiesService {
    constructor(
        private readonly specialitiesRepository: SpecialitiesRepository,
        private readonly specialitiesTranslationsRepository: SpecialitiesTranslationsRepository,
        private readonly specialitiesMapper: SpecialitiesMapper,
        private readonly i18n: I18nService,
        private readonly helperService: HelperService,
    ) { }

    async getSpeciality(id: number, lang: string): Promise<SpecialityDto> {
        const speciality = await this.specialitiesRepository.findOne({
            where: { id },
            relations: [
                'translations',
                'children',
                'parent',
                'children.translations',
                'parent.translations',
            ],
        });
        if (!speciality) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        SpecialityMessagesKeys.SPECIALITY_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.specialitiesMapper.fromEntityToDTO(
            SpecialityDto,
            speciality,
        );
    }

    async getAllSpecialities(lang: string): Promise<SpecialityDto[]> {
        const specialitiesEntities = await this.specialitiesRepository
            .createQueryBuilder('speciality')
            .leftJoinAndSelect(
                'speciality.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('speciality.children', 'children')
            .leftJoinAndSelect(
                'children.translations',
                'children_translations',
                'children_translations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('speciality.parent', 'parent')
            .leftJoinAndSelect(
                'parent.translations',
                'parent_translations',
                'parent_translations.languageCode = :lang',
                { lang },
            )
            .orderBy('translations.title', 'ASC')
            .getMany();

        const specialitiesDtos = specialitiesEntities.map((specialityEntity) =>
            this.specialitiesMapper.fromEntityToDTO(
                SpecialityDto,
                specialityEntity,
            ),
        );

        return specialitiesDtos.map((speciality) => ({
            id: speciality.id,
            title: speciality.translations[0].title,
            children: speciality.children,
            parent: speciality.parent,
        }));
    }

    async getAllSpecialitiesForAdmin(): Promise<SpecialityDto[]> {
        const specialities = await this.specialitiesRepository
            .createQueryBuilder('speciality')
            .leftJoinAndSelect('speciality.translations', 'translations')
            .leftJoinAndSelect('speciality.children', 'children')
            .leftJoinAndSelect('children.translations', 'children_translations')
            .leftJoinAndSelect('speciality.parent', 'parent')
            .leftJoinAndSelect('parent.translations', 'parent_translations')
            .orderBy('translations.title', 'ASC')
            .getMany();
        return specialities.map((speciality) =>
            this.specialitiesMapper.fromEntityToDTO(SpecialityDto, speciality),
        );
    }

    async createSpeciality(
        createSpecialityDto: CreateSpecialityDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        const specialityEntity = this.specialitiesMapper.fromDTOToEntity(
            Speciality,
            createSpecialityDto,
        );
        const speciality = this.specialitiesRepository.create(specialityEntity);
        const createdSpeciality = await this.specialitiesRepository.save(
            speciality,
        );

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                SpecialityMessagesKeys.CREATED_SUCCESSFULLY,
                {
                    lang,
                },
            ),

            createdId: createdSpeciality.id,
        };
    }

    async updateSpeciality(
        id: number,
        updateSpecialityDto: UpdateSpecialityDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const speciality = await this.specialitiesRepository.findOne({
            where: { id },
        });

        if (!speciality) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        SpecialityMessagesKeys.SPECIALITY_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },

                HttpStatus.NOT_FOUND,
            );
        }

        const specialityEntity = this.specialitiesMapper.fromDTOToEntity(
            Speciality,
            updateSpecialityDto,
        );

        if (specialityEntity.translations) {
            await this.updateTranslation(id, specialityEntity.translations);
            delete specialityEntity.translations;
        }
        await this.specialitiesRepository.update({ id }, specialityEntity);

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                SpecialityMessagesKeys.UPDATED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async deleteSpeciality(
        id: number,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const speciality = await this.specialitiesRepository.findOne({
            where: { id },
        });

        if (!speciality) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        SpecialityMessagesKeys.SPECIALITY_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },

                HttpStatus.NOT_FOUND,
            );
        }

        await this.specialitiesRepository.delete({ id });

        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                SpecialityMessagesKeys.DELETED_SUCCESSFULY,
                {
                    lang,
                },
            ),
        };
    }

    async updateTranslation(
        parentId: number,
        updatedTranslation: SpecialityTranslation[],
    ): Promise<void> {
        await Promise.all(
            updatedTranslation.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);
                await this.specialitiesTranslationsRepository.update(
                    {
                        speciality: { id: parentId },
                        languageCode: translation.languageCode,
                    },
                    translation,
                );
            }),
        );
    }
}
