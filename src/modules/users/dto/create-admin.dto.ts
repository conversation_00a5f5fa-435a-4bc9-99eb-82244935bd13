'use strict';

import { Expose } from 'class-transformer';
import { IsEmail, IsPhoneNumber, IsString } from 'class-validator';

import { RoleType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

export class CreateAdminDto extends AbstractDto {
    @IsString()
    @Expose()
    name: string;

    @IsEmail({}, { message: 'invalid email' })
    @Expose()
    email: string;

    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @Expose()
    phone: string;

    @IsString()
    @Expose()
    password: string;

    @IsString()
    @Expose()
    role: RoleType;

    @IsString()
    @Expose()
    accessibility: string;
}
