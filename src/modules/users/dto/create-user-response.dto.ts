'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';
import { TokenPayloadDto } from '../../auth/dto/token-payload.dto';

export class CreateUserResponseDto extends AbstractDto {
    @ApiProperty()
    @IsBoolean()
    @Expose()
    isSuccessful: boolean;

    @ApiProperty()
    @IsString()
    @Expose()
    message: string;

    @ApiProperty()
    @IsNumber()
    @Expose()
    userId: number;

    @ApiProperty({ type: TokenPayloadDto })
    @Expose()
    @Type(() => TokenPayloadDto)
    token: TokenPayloadDto;
}
