import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsEmail,
    IsEnum,
    IsOptional,
    IsPhoneNumber,
    IsString,
    ValidateNested,
} from 'class-validator';

import { GenderType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { UserTranslation } from '../entities/user-translation.entity';

export class CreateUserDto extends AbstractDto {
    @ApiPropertyOptional({
        example: [
            {
                name: 'name',
                description: 'description',
                languageCode: 'ar',
            },
        ],
    })
    @ValidateNested()
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => UserTranslation)
    translations?: UserTranslation[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    firebaseUserId?: string;

    @ApiProperty()
    @Expose()
    @IsEmail({}, { message: 'invalid email' })
    email: string;

    @ApiProperty()
    @Expose()
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    phone: string;

    @ApiProperty()
    @Expose()
    @IsString()
    password: string;

    @ApiPropertyOptional({ enum: GenderType })
    @Expose()
    @IsOptional()
    @IsEnum(GenderType)
    gender?: GenderType;

    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    avatar?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    appLanguage?: string;
}
