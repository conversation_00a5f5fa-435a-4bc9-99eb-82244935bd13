import { Expose } from 'class-transformer';
import { IsString } from 'class-validator';

import { AbstractDto } from '../../../common/dto/abstractDto';

export class DownloadAdminsDto extends AbstractDto {


    @IsString()
    @Expose()
    name: string;

    @IsString()
    @Expose()
    phone: string;

    @IsString()
    @Expose()
    email: string;

    @IsString()
    @Expose()
    role: string;

    @IsString()
    @Expose()
    isActive: boolean;
}
