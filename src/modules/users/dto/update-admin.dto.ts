'use strict';

import { Expose } from 'class-transformer';
import {
    IsBoolean,
    IsEmail,
    IsOptional,
    IsPhoneNumber,
    IsString,
} from 'class-validator';

import { RoleType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';

export class UpdateAdminDto extends AbstractDto {
    @IsString()
    @IsOptional()
    @Expose()
    name?: string;

    @IsEmail({}, { message: 'invalid email' })
    @IsOptional()
    @Expose()
    email?: string;

    @IsString()
    @Expose()
    @IsOptional()
    role?: RoleType;

    @IsString()
    @Expose()
    @IsOptional()
    accessibility?: string;

    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @IsOptional()
    @Expose()
    phone?: string;

    @IsBoolean()
    @IsOptional()
    @Expose()
    isActive?: boolean;
}
