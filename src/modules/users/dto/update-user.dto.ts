'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsEmail,
    IsEnum,
    IsOptional,
    IsPhoneNumber,
    IsString,
    ValidateNested,
} from 'class-validator';

import { GenderType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { UserTranslation } from '../entities/user-translation.entity';

export class UpdateUserDto extends AbstractDto {
    @ApiPropertyOptional({
        example: [
            {
                name: 'name',
                description: 'description',
                languageCode: 'ar',
            },
        ],
    })
    @ValidateNested()
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => UserTranslation)
    translations?: UserTranslation[];

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    firebaseUserId?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    about?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsEmail({}, { message: 'invalid email' })
    email?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsPhoneNumber(null, { message: 'invalid phone number' })
    phone?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsEnum(GenderType)
    gender?: GenderType;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    avatar?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsString()
    @IsOptional()
    appLanguage?: string;

    @ApiPropertyOptional()
    @Expose()
    @IsOptional()
    @IsString()
    password?: string;
}
