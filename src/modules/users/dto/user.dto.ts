'use strict';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsEmail,
    IsEnum,
    IsNumber,
    IsOptional,
    IsPhoneNumber,
    IsString,
} from 'class-validator';

import { GenderType, RoleType } from '../../../common/constants/types';
import { AbstractDto } from '../../../common/dto/abstractDto';
import { CreditCard } from '../../payments/entities/creditcard.entity';
import { UserTranslation } from '../entities/user-translation.entity';

export class UserDto extends AbstractDto {
    @IsString()
    @Expose()
    @IsOptional()
    name?: string;

    @IsString()
    @Expose()
    @IsOptional()
    about?: string;

    @IsString()
    @Expose()
    @IsOptional()
    description?: string;

    @IsEmail({}, { message: 'invalid email' })
    @Expose()
    @IsOptional()
    email?: string;

    @IsPhoneNumber(null, { message: 'invalid phone number' })
    @Expose()
    @IsOptional()
    phone?: string;

    @IsString()
    @Expose()
    @IsOptional()
    password?: string;

    @IsEnum(GenderType)
    @Expose()
    @IsOptional()
    gender?: GenderType;

    @IsString()
    @Expose()
    @IsOptional()
    avatar?: string;

    @Expose()
    @IsString()
    @IsOptional()
    appLanguage?: string;

    @Expose()
    @IsOptional()
    @IsString()
    firebaseUserId?: string;

    @IsNumber()
    @Expose()
    @IsOptional()
    totalConsultations?: number;

    @IsNumber()
    @Expose()
    @IsOptional()
    averageRating?: number;

    @IsString()
    @Expose()
    @IsOptional()
    role?: RoleType;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isEmailVerified?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isActive?: boolean;

    @IsBoolean()
    @Expose()
    @IsOptional()
    isComplete?: boolean;

    @IsArray()
    @Expose()
    @IsOptional()
    @Type(() => CreditCard)
    creditcards?: CreditCard[];

    @ApiPropertyOptional()
    @IsArray()
    @IsOptional()
    @Expose()
    @Type(() => UserTranslation)
    translations?: UserTranslation[];

    @IsString()
    @Expose()
    @IsOptional()
    createdAt?: Date;
}
