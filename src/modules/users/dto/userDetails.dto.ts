'use strict';

import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

import { DoctorDto } from '../../doctors/dto/doctor.dto';
import { PatientDto } from '../../patients/dto/patient.dto';
import { ServiceProviderDto } from '../../service-providers/dto/service-provider.dto';
import { UserDto } from './user.dto';

export class UserDetailsDto extends UserDto {
    @Expose()
    @IsOptional()
    doctor?: DoctorDto;

    @Expose()
    @IsOptional()
    patient?: PatientDto;

    @Expose()
    @IsOptional()
    serviceProvider?: ServiceProviderDto;
}
