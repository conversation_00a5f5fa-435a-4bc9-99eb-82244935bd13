import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDefined, IsNotEmpty, ValidateNested } from 'class-validator';

import { PageMetaDto } from '../../../common/dto/pageMetaDto';
import { UserDto } from './user.dto';

export class UsersPageDto {
    @ApiProperty({
        type: UserDto,
        isArray: true,
    })
    @Expose()
    @ValidateNested()
    @Type(() => UserDto)
    @IsDefined()
    @ApiProperty()
    @IsNotEmpty()
    readonly data: UserDto[];

    @ApiProperty()
    readonly meta: PageMetaDto;

    constructor(data: UserDto[], meta: PageMetaDto) {
        this.data = data;
        this.meta = meta;
    }
}
