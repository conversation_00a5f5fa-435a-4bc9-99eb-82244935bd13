import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';
import { Column, Entity, OneToMany } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { Clinic } from '../../clinics/entities/clinic.entity';
import { City } from './city.entity';

@Entity('governorates')
export class Governorate extends AbstractEntity {
    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    nameEn?: string;

    @OneToMany((_type) => City, (city) => city.governorate, {
        cascade: true,
    })
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => City)
    cities?: City[];

    @OneToMany((_type) => Clinic, (clinic) => clinic.governorate, {
        cascade: true,
    })
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => Clinic)
    clinics?: Clinic[];
}
