import { Column, Entity, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from './user.entity';

@Entity('fcm_tokens')
export class UserFcmToken extends AbstractEntity {
    @Column()
    public deviceId: string;

    @Column()
    public fcmToken: string;

    @ManyToOne(() => UserEntity, (user) => user.userFcmTokens, {
        onDelete: 'CASCADE',
    })
    public user: UserEntity;
}
