import { Expose, Type } from 'class-transformer';
import { IsObject, IsOptional, IsString } from 'class-validator';
import { Column, Entity, Index, ManyToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from './user.entity';

@Entity('users_translations')
export class UserTranslation extends AbstractEntity {
    @Column()
    @Index()
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    about?: string;

    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    description?: string;

    @Column()
    @Expose()
    @IsOptional()
    @IsString()
    languageCode?: string;

    @ManyToOne((_type) => UserEntity, (user) => user.translations, {
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    @Expose()
    @IsOptional()
    @IsObject()
    @Type(() => UserEntity)
    user?: UserEntity;
}
