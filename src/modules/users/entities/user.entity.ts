import { Expose, Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, IsObject, IsOptional, IsString } from 'class-validator';
import { Column, Entity, OneToMany, OneToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { GenderType, RoleType } from '../../../common/constants/types';
import { AvailableLanguageCodes } from '../../../i18n/languageCodes';
import { ColumnNumericTransformer } from '../../../shared/services/column.numeric.transform';
import { AccountantEntity } from '../../accountants/entities/accountant.entity';
import { DoctorEntity } from '../../doctors/entities/doctor.entity';
import { Notification } from '../../notifications/entities/notification.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { CreditCard } from '../../payments/entities/creditcard.entity';
import { Transaction } from '../../payments/entities/transaction.entity';
import { Rating } from '../../ratings/entities/rating.entity';
import { ServiceProvider } from '../../service-providers/entities/service-provider.entity';
import { UserFcmToken } from './user-fcm-tokens.entity';
import { UserTranslation } from './user-translation.entity';
import { Complaint } from '../../complaints/entities/complaint.entity';
import { Booking } from '../../booking/entities/booking.entity';
import { Review } from '../../booking/entities/review.entity';

@Entity('users')
export class UserEntity extends AbstractEntity {
    @Column({ nullable: true })
    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Column({ nullable: true })
    @Expose()
    firebaseUserId: string;

    @Column({ type: 'enum', enum: RoleType, default: RoleType.PATIENT })
    @Expose()
    role: RoleType;

    @Column({ nullable: true })
    @Expose()
    accessibility: string;

    @Column()
    @Expose()
    email: string;

    @Column({ select: false, nullable: true })
    @Expose()
    password: string;

    @Column({})
    @Expose()
    phone: string;

    @IsOptional()
    @Column({ nullable: true })
    @Expose()
    migratedUserId?: number;

    @Column({ nullable: true, default: 'https://bit.ly/1nRvZRB' })
    @Expose()
    avatar: string;

    @Column({ nullable: true, default: AvailableLanguageCodes.en })
    @Expose()
    appLanguage: string;

    @Column({ nullable: true })
    refreshToken: string;

    @OneToMany(() => UserFcmToken, (userFcmToken) => userFcmToken.user, {
        onDelete: 'CASCADE',
    })
    public userFcmTokens: UserFcmToken[];

    @Column({ type: 'enum', enum: GenderType, nullable: true })
    @Expose()
    gender: GenderType;

    @Column({
        type: 'decimal',
        default: 0,
        precision: 16,
        scale: 2,
        transformer: new ColumnNumericTransformer(),
    })
    @Expose()
    averageRating: number;

    @Column({ default: false })
    @Expose()
    isEmailVerified: boolean;

    @Column({ default: false })
    @Expose()
    isDeleted: boolean;

    @Column({ default: false })
    @Expose()
    isComplete: boolean;

    @Column({ default: false })
    @Expose()
    isActive: boolean;

    @Column({ nullable: true })
    @Expose()
    verificationCode: string;

    @Column({ default: 0 })
    @Expose()
    totalConsultations: number;

    @OneToOne(() => DoctorEntity, (doctor) => doctor.user, {
        onDelete: 'CASCADE',
    })
    doctor: DoctorEntity;

    @OneToOne(() => AccountantEntity, (accountant) => accountant.user, {
        onDelete: 'CASCADE',
    })
    accountant: AccountantEntity;

    @OneToOne(() => Patient, (patient) => patient.user, {
        onDelete: 'CASCADE',
    })
    patient: Patient;

    @Expose()
    @IsObject()
    @Type(() => ServiceProvider)
    @OneToOne(
        () => ServiceProvider,
        (serviceProvider) => serviceProvider.user,
        {
            onDelete: 'CASCADE',
        },
    )
    serviceProvider?: ServiceProvider;

    @OneToMany(() => Rating, (rating) => rating.ratedUser, {
        onDelete: 'CASCADE',
    })
    ratings: Rating[];

    @OneToMany(
        (_type) => UserTranslation,
        (userTranslation) => userTranslation.user,
        { cascade: true },
    )
    @Expose()
    @IsOptional()
    @IsArray()
    @Type(() => UserTranslation)
    translations?: UserTranslation[];

    @OneToMany(() => Notification, (notification) => notification.user, {
        onDelete: 'CASCADE',
    })
    notifications: Notification[];

    @OneToMany(() => CreditCard, (creditcard) => creditcard.user)
    creditcards: CreditCard[];

    @OneToMany((_type) => Transaction, (transaction) => transaction.patient, {
        cascade: true,
    })
    transactions: Transaction[];

    @OneToMany(() => Complaint, (complaint) => complaint.user)
    complaints: Complaint[];

    @OneToMany(() => Booking, (booking) => booking.user)
    bookings: Booking[];

    @OneToMany(() => Review, (review) => review.user)
    reviews: Review[];
}
