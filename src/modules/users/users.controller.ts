'use strict';

import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiHeader,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { I18nLang } from 'nestjs-i18n';

import { RoleType } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { Roles } from '../../decorators/roles.decorator';
import { AuthGuard } from '../../guards/auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { AuthUserInterceptor } from '../../interceptors/auth-user-interceptor.service';
import { HeaderInterceptor } from '../../interceptors/language.interceptor';
import { ChangePasswordDto } from './dto/changePassword.dto';
import { CreateAdminDto } from './dto/create-admin.dto';
import { DownloadAdminsDto } from './dto/download-admins.dto';
import { UpdateAdminDto } from './dto/update-admin.dto';
import { UserDetailsDto } from './dto/userDetails.dto';
import { UsersPageDto } from './dto/usersPage.dto';
import { UsersPageOptionsDto } from './dto/usersPageOptions.dto';
import { UsersService } from './users.service';

@Controller('users')
@ApiTags('users')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
@UseInterceptors(AuthUserInterceptor, HeaderInterceptor)
@ApiBearerAuth()
export class UsersController {
    constructor(private usersService: UsersService) {}

    @Post('/admins')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Create new admin',
        type: CreateOperationsResponse,
    })
    createAdmin(
        @Body() createAdminDto: CreateAdminDto,
        @I18nLang() lang: string,
    ): Promise<CreateOperationsResponse> {
        return this.usersService.createAdmin(createAdminDto, lang);
    }
    @Get('/admins')
    @UseGuards(AuthGuard, RolesGuard)
    @Roles(RoleType.SUPER_ADMIN)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'list admins',
        type: UsersPageDto,
    })
    listAdmins(
        @Query('query') query: string,
        @Query(new ValidationPipe({ transform: true }))
        pageOptionsDto: UsersPageOptionsDto,
    ): Promise<UsersPageDto> {
        return this.usersService.listAdmins(query, pageOptionsDto);
    }

    @Get('/admins/:id')
    @Roles(RoleType.SUPER_ADMIN)
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'view admin',
        type: UserDetailsDto,
    })
    viewAdmin(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<UserDetailsDto> {
        return this.usersService.viewAdmin(id, lang);
    }

    @Get('/download/admins')
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        description: 'Download admins',
        type: [DownloadAdminsDto],
    })
    async downloadAdmins(
        @Req() req: Request,
        @Res() res: Response,
        @Query('query') query: string,
    ): Promise<void> {
        const data = await this.usersService.downloadAdmins(query);
        res.setHeader('Content-disposition', 'attachment; filename=admins.csv');
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).end(data);
    }

    @Put('/admins/:id')
    @Roles(RoleType.SUPER_ADMIN)
    @UseGuards(AuthGuard, RolesGuard)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'update admin',
        type: BasicOperationsResponse,
    })
    updateAdmin(
        @Param('id') id: number,
        @Body() updateAdminDto: UpdateAdminDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        return this.usersService.updateAdmin(id, updateAdminDto, lang);
    }

    @Delete('/admins/:id')
    @Roles(RoleType.SUPER_ADMIN)
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, RolesGuard)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'update admin',
        type: BasicOperationsResponse,
    })
    deleteAdmin(@Param('id') id: number): Promise<BasicOperationsResponse> {
        return this.usersService.deleteAdmin(id);
    }

    @Get('/roles')
    @Roles(RoleType.SUPER_ADMIN)
    @UseGuards(AuthGuard, RolesGuard)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Get roles',
        type: [String],
    })
    getRoles(): string[] {
        return this.usersService.getAdminRoles();
    }

    @Get(':id')
    @Roles(RoleType.ADMIN)
    @UseGuards(AuthGuard, RolesGuard)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Get one user by ID',
        type: UserDetailsDto,
    })
    getUser(
        @Param('id') id: number,
        @I18nLang() lang: string,
    ): Promise<UserDetailsDto> {
        return this.usersService.findOneById(id, lang);
    }

    @Put('password')
    @UseGuards(AuthGuard, RolesGuard)
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'update password',
        type: ChangePasswordDto,
    })
    changePassword(
        @Req() req: Request,
        @Body() changePasswordDto: ChangePasswordDto,
        @I18nLang() lang: string,
    ): Promise<BasicOperationsResponse> {
        const id = req.user.id;
        return this.usersService.changePassword(
            id,
            changePasswordDto.password,
            changePasswordDto.oldPassword,
            lang,
        );
    }
}
