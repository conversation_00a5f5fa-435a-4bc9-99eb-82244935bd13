import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { PsuedoLogger } from '../../common/PsuedoLogger';
import { UsersService } from './users.service';

@Injectable()
export class UsersCorn {
    private readonly logger = new PsuedoLogger();

    constructor(private userService: UsersService) {}

    @Cron(CronExpression.EVERY_DAY_AT_NOON)
    async updateUserDataIsCompleted(): Promise<void> {
        await this.userService.updateIsCompletedUser();
    }
}
