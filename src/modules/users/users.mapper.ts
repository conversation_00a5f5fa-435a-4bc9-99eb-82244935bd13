/* eslint-disable complexity */
import { Injectable } from '@nestjs/common';
import { ClassConstructor as ClassType, plainToClass } from 'class-transformer';

import { AbstractMapper } from '../../common/abstract.mapper';
import { RoleType } from '../../common/constants/types';
import { HelperService } from '../../shared/services/helper';
import { CreateUserDto } from './dto/create-user.dto';
import { DownloadAdminsDto } from './dto/download-admins.dto';
import { UserDto } from './dto/user.dto';
import { UserDetailsDto } from './dto/userDetails.dto';
import { UserEntity } from './entities/user.entity';

type UserDTOs = CreateUserDto | UserDetailsDto | UserDto;

@Injectable()
export class UsersMapper extends AbstractMapper<UserDTOs, UserEntity> {
    constructor(public readonly helperService: HelperService) {
        super();
    }

    fromDTOToEntity(
        destination: ClassType<UserEntity>,
        sourceObject: UserDTOs,
    ): UserEntity {
        const userEntity = super.fromDTOToEntity(destination, sourceObject);
        this.helperService.removeEmptyKeys(userEntity);
        return userEntity;
    }

    fromEntityToDTO(
        destination: ClassType<UserDTOs>,
        sourceObject: UserEntity,
    ): UserDTOs {
        const userDto = super.fromEntityToDTO(destination, sourceObject);
        this.helperService.removeEmptyKeys(userDto);
        return userDto;
    }

    fromEntityToUserDetailsDTO(
        destination: ClassType<UserDetailsDto>,
        sourceObject: UserEntity,
    ): UserDetailsDto {
        const user = plainToClass(destination, sourceObject);
        if (sourceObject.role === RoleType.DOCTOR) {
            user.doctor.specialities = sourceObject.doctor.specialities
                ? sourceObject.doctor.specialities.map((speciality) => ({
                    id: speciality.id,
                    title: speciality.translations[0]
                        ? speciality.translations[0].title
                        : '',
                }))
                : [];
            user.doctor.grade = sourceObject.doctor.grade
                ? {
                    id: sourceObject.doctor.grade.id,
                    title: sourceObject.doctor.grade.translations[0]
                        ? sourceObject.doctor.grade.translations[0].title
                        : '',
                }
                : {
                    id: null,
                    title: '',
                };
        }
        if (sourceObject.role === RoleType.SERVICE_PROVIDER) {
            if (user.translations && user.translations.length) {
                user.name = user.translations[0].name;
                user.description = user.translations[0].description;

                delete user.translations;
            }
            if (
                user.serviceProvider &&
                user.serviceProvider.serviceProviderType &&
                user.serviceProvider.serviceProviderType.translations &&
                user.serviceProvider.serviceProviderType.translations.length
            ) {
                user.serviceProvider.serviceProviderType.title =
                    user.serviceProvider.serviceProviderType.translations[0].title;
                delete user.serviceProvider.serviceProviderType.translations;
            }
            if (
                user.serviceProvider &&
                user.serviceProvider.insuranceCompanies
            ) {
                user.serviceProvider.insuranceCompanies.map(
                    (insuranceCompany) => {
                        if (
                            insuranceCompany &&
                            insuranceCompany.translations &&
                            insuranceCompany.translations.length
                        ) {
                            insuranceCompany.title =
                                insuranceCompany.translations[0].title;
                        }
                    },
                );
            }
        }
        return user;
    }

    fromEntityToDownloadData(
        destination: ClassType<DownloadAdminsDto>,
        sourceObject: UserEntity,
    ): DownloadAdminsDto {
        return {
            id: sourceObject.id,
            email: sourceObject.email,
            isActive: sourceObject.isActive,
            name: sourceObject.name,
            phone: sourceObject.phone,
            role: sourceObject.role,
        };
    }
}
