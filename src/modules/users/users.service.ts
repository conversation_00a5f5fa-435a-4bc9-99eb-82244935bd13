import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { I18nService } from 'nestjs-i18n';
import { Repository, SelectQueryBuilder, UpdateResult } from 'typeorm';

import {
    FirebaseTopics,
    Languages,
    RoleType,
    SortByType,
} from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { CreateOperationsResponse } from '../../common/dto/createOperationsResponse.dto';
import { ConfigService } from '../../config/config.service';
import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { DataDownloadService } from '../../shared/services/data-download.service';
import { EmailService } from '../../shared/services/email.service';
import { HelperService } from '../../shared/services/helper';
import { DebugLogger } from '../../shared/services/logger.service';
import { ValidatorService } from '../../shared/services/validator.service';
import { UpdateUserFcmTokenDto } from '../auth/dto/update-user-fcm-token.dto';
import {
    AuthMessagesKeys,
    SyndicateUpdateMessageKeys,
} from '../auth/translate.enum';
import { CreateAdminDto } from './dto/create-admin.dto';
import { DownloadAdminsDto } from './dto/download-admins.dto';
import { UpdateAdminDto } from './dto/update-admin.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserDto } from './dto/user.dto';
import { UserDetailsDto } from './dto/userDetails.dto';
import { UsersPageDto } from './dto/usersPage.dto';
import { UsersPageOptionsDto } from './dto/usersPageOptions.dto';
import { UserTranslation } from './entities/user-translation.entity';
import { UserEntity } from './entities/user.entity';
import { UsersMapper } from './users.mapper';
import { FcmTokensResponse } from './utils';
import { InjectRepository } from '@nestjs/typeorm';
import { UserFcmToken } from './entities/user-fcm-tokens.entity';

@Injectable()
export class UsersService {
    constructor(
        @InjectRepository(UserEntity)
        public readonly usersRepository: Repository<UserEntity>,
        @InjectRepository(UserTranslation)
        public readonly userTranslationsRepository: Repository<UserTranslation>,
        public readonly validatorService: ValidatorService,
        @InjectRepository(UserFcmToken)
        public readonly userFcmTokensRepository: Repository<UserFcmToken>,
        private readonly i18n: I18nService,
        public readonly usersMapper: UsersMapper,
        private readonly dataDownloadService: DataDownloadService,
        public readonly emailService: EmailService,
        public readonly helperService: HelperService,
        public readonly configService: ConfigService,
        public readonly jwtService: JwtService,
        public readonly logger: DebugLogger,
    ) {
    }

    async getUsersFcmTokensByLang(
        lang: Languages,
        role?: FirebaseTopics,
    ): Promise<string[]> {
        const query = this.userFcmTokensRepository
            .createQueryBuilder('userFcmTokens')
            .leftJoinAndSelect('userFcmTokens.user', 'user')
            .select(['userFcmTokens.fcmToken', 'user.appLanguage', 'user.role'])
            .where('user.appLanguage = :lang', { lang });

        if (role) {
            query.andWhere('user.role = :role', { role });
        }
        const tokens = await query.getMany();
        return tokens.map((item) => item.fcmToken);
    }

    async getUsersFcmTokens(role?: FirebaseTopics): Promise<FcmTokensResponse> {
        const res = {} as FcmTokensResponse;
        res.ar = await this.getUsersFcmTokensByLang(Languages.AR, role);
        res.en = await this.getUsersFcmTokensByLang(Languages.EN, role);
        return res;
    }

    getAdminRoles(): string[] {
        return [RoleType.ADMIN];
    }

    async createAdmin(
        createAdminDto: CreateAdminDto,
        lang: string,
    ): Promise<CreateOperationsResponse> {
        await this.checkUserExistence(createAdminDto, lang, RoleType.ADMIN);

        const adminDto = this.usersMapper.fromDTOToEntity(
            UserEntity,
            createAdminDto,
        );
        adminDto.isActive = true;
        const admin = this.usersRepository.create(adminDto);
        const createdAdmin = await this.usersRepository.save(admin);

        return {
            createdId: createdAdmin.id,
            isSuccessful: true,
            message: 'Admin is added successfully',
        };
    }

    async downloadAdmins(httpQueryString: string): Promise<string> {
        let query = this.usersRepository
            .createQueryBuilder('user')
            .select([
                'user.id',
                'user.role',
                'user.name',
                'user.phone',
                'user.email',
                'user.isActive',
            ])
            .where('user.role IN (:...roles)', { roles: this.getAdminRoles() });

        if (httpQueryString) {
            query = this.buildGetAdminsQuery(query, httpQueryString);
        }

        const requests = await query.getMany();

        const mappedRequests = requests.map((request) =>
            this.usersMapper.fromEntityToDownloadData(
                DownloadAdminsDto,
                request,
            ),
        );

        const fields = ['id', 'email', 'isActive', 'name', 'phone', 'role'];
        return this.dataDownloadService.downloadCsv(fields, mappedRequests);
    }

    buildGetAdminsQuery(
        query: SelectQueryBuilder<UserEntity>,
        httpQueryString: string,
    ): SelectQueryBuilder<UserEntity> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }
        // TODO: fix filter once the doctor has en and ar names
        if (httpQueryObject.search) {
            query.andWhere(
                '(user.id || user.name || user.phone || user.email || user.role ) ILIKE :searchKey',
                {
                    searchKey: `%${httpQueryObject.search.value}%`,
                },
            );
        }

        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.NAME:
                    query.orderBy('user.name', httpQueryObject.sort.type);
                    break;
                case SortByType.ROLE:
                    query.orderBy('user.role', httpQueryObject.sort.type);
            }
        }

        return query;
    }

    async listAdmins(
        httpQueryString: string,
        pageOptionsDto: UsersPageOptionsDto,
    ): Promise<UsersPageDto> {
        let queryBuilder = this.usersRepository
            .createQueryBuilder('user')
            .select([
                'user.id',
                'user.role',
                'user.name',
                'user.email',
                'user.phone',
                'user.accessibility',
                'user.isActive',
            ])
            .where('user.role IN (:...roles)', { roles: this.getAdminRoles() })
            .andWhere('user.isDeleted = :isDeleted', { isDeleted: false });
        if (httpQueryString) {
            queryBuilder = this.buildGetAdminsQuery(
                queryBuilder,
                httpQueryString,
            );
        }
        const [users, pageMetaDto] = await queryBuilder.paginate(
            pageOptionsDto,
        );
        return new UsersPageDto(users, pageMetaDto);
    }

    async viewAdmin(id: number, lang: string): Promise<UserDetailsDto> {
        const user = await this.usersRepository
            .createQueryBuilder('user')
            .where('user.id = :id', { id })
            .select([
                'user.id',
                'user.role',
                'user.name',
                'user.email',
                'user.phone',
                'user.accessibility',
                'user.isActive',
            ])
            .getOne();

        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.usersMapper.fromEntityToUserDetailsDTO(
            UserDetailsDto,
            user,
        );
    }

    async updateAdmin(
        id: number,
        updateAdminDto: UpdateAdminDto,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const user = await this.usersRepository.findOne({
            where: [{ id, role: RoleType.ADMIN }],
        });
        if (!user) {
            throw new HttpException(
                this.i18n.translate(AuthMessagesKeys.USER_NOT_FOUND, { lang }),
                HttpStatus.NOT_FOUND,
            );
        }
        const adminDto = this.usersMapper.fromDTOToEntity(
            UserEntity,
            updateAdminDto,
        );
        await this.usersRepository.update({ id }, adminDto);
        return {
            isSuccessful: true,
            message: 'Admin Updated successfully',
        };
    }

    async deleteAdmin(id: number): Promise<BasicOperationsResponse> {
        await this.usersRepository.delete({ id });
        return {
            isSuccessful: true,
            message: 'Admin Deleted successfully',
        };
    }

    async deletePatient(id: number): Promise<BasicOperationsResponse> {
        await this.usersRepository.delete({ id });
        return {
            isSuccessful: true,
            message: 'Admin Deleted successfully',
        };
    }

    async throwErrorIfUserExist(
        user: UserEntity,
        userDto: UserDto,
        lang: string,
    ): Promise<void> {
        if (user) {
            /* eslint-disable */
            const message =
                user.email === userDto.email ?
                    await this.i18n.translate(AuthMessagesKeys.EMAIL_EXISTS, { lang }) :
                    await this.i18n.translate(AuthMessagesKeys.PHONE_EXISTS, { lang });

            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }

    }

    async checkUserExistence(
        userDto: UserDto,
        lang: string,
        role: RoleType,
    ): Promise<void> {
        const users = await this.usersRepository.find({
            where: [{ email: userDto.email }, { phone: userDto.phone }],
        });

        const someOfUsersHasSameRole = users.some(user => user.role === role);
        if (users && users.length && someOfUsersHasSameRole) {
            const user = users.find(user => user.role === role);
            await this.throwErrorIfUserExist(user, userDto, lang);
        }
    }

    async findOneById(id: number, lang: string): Promise<UserDetailsDto> {
        const user = await this.usersRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.serviceProvider', 'serviceProvider')
            .leftJoinAndSelect('serviceProvider.insuranceCompanies', 'insuranceCompanies')
            .leftJoinAndSelect(
                'insuranceCompanies.translations',
                'insuranceCompaniesTranslations',
                'insuranceCompaniesTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('serviceProvider.serviceProviderType', 'serviceProviderType')
            .leftJoinAndSelect(
                'serviceProviderType.translations',
                'serviceProviderTypeTranslations',
                'serviceProviderTypeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('user.patient', 'patient')
            .leftJoinAndSelect(
                'user.translations',
                'translations',
                'translations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('user.creditcards', 'creditcards')
            .leftJoinAndSelect('user.doctor', 'doctor')
            .leftJoinAndSelect('doctor.serviceProviders', 'serviceProviders')
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('patient.medications', 'medications')
            .leftJoinAndSelect(
                'medications.translations',
                'medicationsTranslations',
                'medicationsTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('patient.membership', 'membership')
            .leftJoinAndSelect(
                'membership.translations',
                'membershipTranslations',
                'membershipTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('patient.nationality', 'nationality')
            .leftJoinAndSelect(
                'nationality.translations',
                'nationalityTranslations',
                'nationalityTranslations.languageCode = :lang',
                { lang },
            )
            .where('user.id = :id', { id })
            .andWhere('user.isDeleted = :isDelete', { isDelete: false })
            .getOne();

        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return this.usersMapper.fromEntityToUserDetailsDTO(
            UserDetailsDto,
            user,
        );
    }

    async findUserByEmail(email: string, lang: string): Promise<UserEntity> {
        const user = await this.usersRepository.findOne({
            where: { email, isDeleted: false },
            select: ['id', 'password', 'verificationCode', 'role', 'refreshToken', 'email'],
        });
        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.EMAIL_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return user;
    }

    async findUser(id: number, select: (keyof UserEntity)[], lang: string): Promise<UserEntity> {
        const user = await this.usersRepository.findOne({
            where: { id, isDeleted: false },
            select,
        });

        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return user;
    }

    async findUserById(id: number, lang: string): Promise<UserEntity> {
        const user = await this.usersRepository.findOne({
            where: { id, isDeleted: false },
            select: ['id', 'password', 'role'],
        });
        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return user;
    }

    async adminLogin(email: string, lang: string): Promise<UserEntity> {
        const user = await this.usersRepository.findOne({
            where: { email, isActive: true },
            select: ['id', 'password', 'verificationCode', 'role', 'refreshToken'],
        });
        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_INACTIVE,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return user;
    }

    async findUserByPhone(phone: string, role: RoleType, lang: string, password?: string): Promise<UserEntity> {
        const user = await this.usersRepository.findOne({
            where: { phone, role, isDeleted: false },
            select: ['id', 'password', 'verificationCode', 'role', 'firebaseUserId'],
        });
        let isPasswordCorrect = false;

        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        if (user && password) {
            isPasswordCorrect = await bcrypt.compare(password, user.password);
        }

        if (password && !isPasswordCorrect) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.WRONG_PASSWORD,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.UNAUTHORIZED,
            );
        }

        return user;
    }

    async findUserByEmailAndRole(email: string, role: RoleType, lang: string): Promise<UserEntity> {
        const user = await this.usersRepository.findOne({
            where: { email, role, isDeleted: false },
            select: ['id', 'password', 'verificationCode', 'role', 'refreshToken', 'email'],
        });
        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.EMAIL_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        return user;
    }

    async updateRefreshToken(
        userId: number,
        refreshToken: string,
    ): Promise<void> {
        await this.usersRepository.update({ id: userId }, { refreshToken });
    }

    async updateVerificationCode(userId: number, code: string): Promise<void> {
        await this.usersRepository.update(
            { id: userId },
            { verificationCode: code },
        );
    }

    async updatePassword(userId: number, password: string): Promise<void> {
        password = bcrypt.hashSync(password, 10);
        await this.usersRepository.update({ id: userId }, { password });
    }

    async changePassword(
        userId: number,
        password: string,
        oldPassword: string,
        lang: string,
    ): Promise<BasicOperationsResponse> {
        const user = await this.usersRepository.findOne({
            where: { id: userId },
            select: ['id', 'password'],
        });
        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }
        const isPasswordCorrect = await bcrypt.compare(
            oldPassword,
            user.password,
        );

        if (!isPasswordCorrect) {
            throw new HttpException(
                await this.i18n.translate(AuthMessagesKeys.WRONG_PASSWORD, {
                    lang,
                }),
                HttpStatus.UNAUTHORIZED,
            );
        }

        await this.updatePassword(userId, password);
        return {
            isSuccessful: true,
            message: 'password updated successfully',
        };
    }

    async getUserByDoctorId(doctorId: number, lang: string): Promise<UserEntity> {

        return await this.usersRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.doctor', 'doctor')
            .leftJoinAndSelect('doctor.specialities', 'specialities')
            .leftJoinAndSelect(
                'specialities.translations',
                'specialityTranslations',
                'specialityTranslations.languageCode = :lang',
                { lang },
            )
            .leftJoinAndSelect('doctor.grade', 'grade')
            .leftJoinAndSelect(
                'grade.translations',
                'gradeTranslations',
                'gradeTranslations.languageCode = :lang',
                { lang },
            )
            .where('doctor.id = :id', { id: doctorId })
            .select([
                'user.id',
                'user.name',
                'user.avatar',
                'user.role',
                'user.averageRating',
                'doctor.id',
                'specialities',
                'grade',
                'specialityTranslations',
                'gradeTranslations',
            ])
            .getOne();
    }

    async updateAverageRating(userId: number, rating: number): Promise<void> {
        const user = await this.usersRepository.findOne({
            where: { id: userId },
            select: ['averageRating', 'totalConsultations'],
        });
        const newTotalNumberOfConsultations = user.totalConsultations + 1;
        const newRating = ((user.averageRating * user.totalConsultations) + rating) / newTotalNumberOfConsultations;
        await this.usersRepository.update({ id: userId }, {
            averageRating: newRating,
            totalConsultations: newTotalNumberOfConsultations,
        });
    }

    async updateUser(id: number, updateUser: UpdateUserDto, lang: string) {
        const user = await this.usersRepository.findOne({ where: { id } });

        if (!user) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.USER_NOT_FOUND,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.NOT_FOUND,
            );
        }

        // TODO: use typeorm relations to do this update this in a better way
        if (updateUser.translations) {
            await this.updateTranslation(
                id,
                updateUser.translations,
            );
            delete updateUser.translations;
        }
        await this.usersRepository.update({ id }, updateUser);
    }

    async getFcmTokenByUserId(userId: number): Promise<string[]> {
        const latestToken = await this.userFcmTokensRepository
            .createQueryBuilder('userFcmToken')
            .leftJoin('userFcmToken.user', 'user')
            .select(['userFcmToken.fcmToken'])
            .where('user.id = :userId', { userId })
            .orderBy('userFcmToken.createdAt', 'DESC')
            .getOne();

        if (!latestToken) {
            // Return empty array instead of throwing error - user might not have FCM token
            return [];
        }
        return [latestToken.fcmToken];
    }

    async updateFcmToken(userId: number, fcmTokenDto: UpdateUserFcmTokenDto, lang: string): Promise<BasicOperationsResponse> {
        const { deviceId, fcmToken } = fcmTokenDto;
        await this.userFcmTokensRepository.insert({ deviceId, fcmToken, user: { id: userId } });
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                AuthMessagesKeys.UPDATE_FCMTOKEN,
                {
                    lang,
                },
            ),
        };
    }

    async logOut(userId: number, deviceId: string, lang: string): Promise<BasicOperationsResponse> {
        await this.userFcmTokensRepository.delete({ user: { id: userId }, deviceId });
        return {
            isSuccessful: true,
            message: await this.i18n.translate(
                AuthMessagesKeys.UPDATE_FCMTOKEN,
                {
                    lang,
                },
            ),
        };

    }

    async confirmEmail(id: number, lang: string): Promise<BasicOperationsResponse> {
        const user = await this.usersRepository.findOne({ where: { id }, select: ['email'] });
        const token = this.jwtService.sign(
            { id },
            {
                expiresIn: this.configService.JWT_ACCESS_TOKEN_EXPIRES_IN,
                secret: this.configService.JWT_ACCESS_TOKEN_SECRET_KEY,
            },
        );

        this.sendEmailVerification(token, lang, user).catch(error => this.logger.error(error));
        return {
            isSuccessful: true,
            message: await this.i18n.translate(AuthMessagesKeys.EMAIL_VERIFICATION_SENT, { lang }),
        };
    }

    async sendRegistrationEmail(id: number, header: string, body: string, lang: string): Promise<void> {
        const user = await this.usersRepository.findOne({ where: { id }, select: ['email'] });

        const subject = await this.i18n.translate(AuthMessagesKeys.WELCOME_MESSAGE, { lang });

        const html = `${header}<br> ${body}<br>`;

        this.emailService.sendEmail(user.email, subject, html).catch(error => this.logger.error(error));
    }

    private async sendEmailVerification(token: string, lang: string, user: UserEntity) {
        const link = `${this.configService.SERVER_URL}auth/email-verification/${token}/${lang}`;

        const header = await this.i18n.translate(AuthMessagesKeys.EMAIL_VERIFICATION_HEADER, { lang });
        const body = await this.i18n.translate(AuthMessagesKeys.EMAIL_VERIFICATION_BODY, { lang });
        const button = await this.i18n.translate(AuthMessagesKeys.EMAIL_VERIFICATION_BUTTON, { lang });
        const subject = await this.i18n.translate(AuthMessagesKeys.EMAIL_VERIFICATION_SUBJECT, { lang });
        const html = `${header}<br> ${body}<br><a href='${link}'>${button}</a>`;

        this.emailService.sendEmail(user.email, subject, html).catch(error => this.logger.error(error));
    }

    async sendEmailForSynicateUpdate(lang: string, userEmail: string) {
        const subject = await this.i18n.translate(SyndicateUpdateMessageKeys.SYNDICATE_UPDATE_SUBJECT, { lang });
        const body = await this.i18n.translate(SyndicateUpdateMessageKeys.SYNDICATE_UPDATE_BODY, { lang });
        const superAdmins = await this.usersRepository.find({
            where:
                [
                    {
                        role: RoleType.SUPER_ADMIN,
                    },
                    {
                        role: RoleType.ADMIN,
                    },
                ],
            select: ['id', 'email'],
        });
        for (let admin of superAdmins) {
            this.emailService.sendEmail(admin.email, subject, body).catch(error => this.logger.error(error));
        }
    }

    async sendEmailToAdminOnPatientRegistration(lang: string) {
        const subject = await this.i18n.translate(AuthMessagesKeys.NEW_PATIENT_REGISTER_SUBJECT, { lang });
        const body = await this.i18n.translate(AuthMessagesKeys.NEW_PATIENT_REGISTER, { lang });

        await this.sendEmailToAdminOnRegisteration(subject, body);
    }
    async sendEmailToAdminOnServiceRegistration(lang: string) {
        const subject = await this.i18n.translate(AuthMessagesKeys.NEW_SERVICE_PROVIDER_REGISTER_SUBJECT, { lang });
        const body = await this.i18n.translate(AuthMessagesKeys.NEW_SERVICE_PROVIDER_REGISTER, { lang });

        await this.sendEmailToAdminOnRegisteration(subject, body);
    }

    async sendEmailToAdminOnDoctorRegistration(lang: string) {
        const subject = await this.i18n.translate(AuthMessagesKeys.NEW_DOCTOR_REGISTER_SUBJECT, { lang });
        const body = await this.i18n.translate(AuthMessagesKeys.NEW_DOCTOR_REGISTER, { lang });

        await this.sendEmailToAdminOnRegisteration(subject, body);
    }

    async sendEmailToAdminOnRegisteration(subject: string, body: string) {
        const superAdmins = await this.usersRepository.find({
            where:
                [
                    {
                        role: RoleType.SUPER_ADMIN,
                    },
                    {
                        role: RoleType.ADMIN,
                    },
                ],
            select: ['id', 'email'],
        });
        for (let admin of superAdmins) {
            this.emailService.sendEmail(admin.email, subject, body).catch(error => this.logger.error(error));
        }
    }

    async verifyEmail(token: string, lang: string): Promise<void> {

        const decode = this.jwtService.verify(
            token,
            { secret: this.configService.JWT_ACCESS_TOKEN_SECRET_KEY },
        );

        if (!decode) {
            throw new HttpException(
                {
                    message: await this.i18n.translate(
                        AuthMessagesKeys.INVALID_TOKEN,
                        {
                            lang,
                        },
                    ),
                },
                HttpStatus.UNAUTHORIZED,
            );
        }

        await this.usersRepository.update({ id: decode.id }, { isEmailVerified: true });
    }

    async updateEmailVerified(userId: number, isEmailVerified: boolean): Promise<void> {
        await this.usersRepository.update({ id: userId }, { isEmailVerified });
    }

    async updateTranslation(
        parentId: number,
        updatedTranslation: UserTranslation[],
    ): Promise<void> {
        await Promise.all(
            updatedTranslation.map(async (translation) => {
                this.helperService.removeEmptyKeys(translation);
                await this.userTranslationsRepository.update(
                    {
                        user: { id: parentId },
                        languageCode: translation.languageCode,
                    },
                    translation,
                );
            }),
        );
    }

    async softDelete(id: string): Promise<UpdateResult> {
        return this.usersRepository
            .createQueryBuilder()
            .update()
            .set({ isDeleted: true })
            .where('id = :id', { id })
            .execute();
    }

    async updateIsCompletedUser(): Promise<void> {
        await this.markUserAsCompleted();
        // await this.markUserAsIsNotCompleted();
    }

    async markUserAsCompleted(): Promise<void> {
        const isCompleted = true;
        await this.usersRepository.query(`
            update
                users
            set is_completed = '${isCompleted}'
            where (
                    now() > created_at + interval '24 hour'
                    OR now() > updated_at + interval '24 hour'
                )
              AND is_completed = false
              AND is_active = true
              AND is_deleted = false
              AND is_email_verified = true
        `);
    }

    async markUserAsIsNotCompleted(): Promise<void> {
        const isCompleted = false;
        await this.usersRepository.query(`
            update
                users
            set is_completed = '${isCompleted}'
            where (
                    now() > created_at + interval '24 hour'
                    OR now() > updated_at + interval '24 hour'
                )
              AND is_completed = true
              AND is_active = false
              AND is_deleted = true
              AND is_email_verified = false
        `);
    }
}
