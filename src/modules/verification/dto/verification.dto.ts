import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsPhoneNumber, IsString } from 'class-validator';

export class VerificationDto {
    @IsString()
    @Expose()
    @ApiProperty()
    @IsPhoneNumber(null, { message: 'Invalid phone number format' })
    phoneNumber: string;

    @IsString()
    @IsOptional()
    @Expose()
    @ApiProperty()
    code?: string;
}
