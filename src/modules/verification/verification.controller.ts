import { Body, Controller, Post } from '@nestjs/common';
import { ApiHeader, ApiResponse, ApiTags } from '@nestjs/swagger';

import { AvailableLanguageCodes } from '../../i18n/languageCodes';
import { VerificationCodeDto } from './dto/verification-code.dto';
import { VerificationDto } from './dto/verification.dto';
import { VerificationService } from './verification.service';

@Controller('verification')
@ApiTags('verification')
@ApiHeader({
    name: 'Accept-Language',
    enum: AvailableLanguageCodes,
})
export class VerificationController {
    constructor(private verificationService: VerificationService) {}

    @Post('/send')
    @ApiResponse({
        description: 'Send Verification Code',
    })
    async sendVerificationCode(@Body() verificationDto: VerificationDto) {
        return this.verificationService.sendVerification(
            verificationDto.phoneNumber,
        );
    }

    @Post('/verify/code')
    @ApiResponse({
        description: 'Send Verification Code',
    })
    async verifyCode(@Body() verificationDto: VerificationCodeDto) {
        return this.verificationService.verifyCode(verificationDto);
    }
}
