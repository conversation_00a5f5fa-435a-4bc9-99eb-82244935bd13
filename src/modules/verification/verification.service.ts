import { Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { validateSync } from 'class-validator';

import { VerificationMessages } from '../../common/constants/types';
import { BasicOperationsResponse } from '../../common/dto/basicOperationsResponse.dto';
import { DebugLogger } from '../../shared/services/logger.service';
import { TwilioService } from '../../shared/services/twilio.service';
import { VerificationCodeDto } from './dto/verification-code.dto';
import { VerificationDto } from './dto/verification.dto';
import { PhoneVerificationException } from './exceptions/phone-verification.exception';

@Injectable()
export class VerificationService {
    constructor(
        private readonly twilioService: TwilioService,
        private readonly logger: DebugLogger,
    ) {}

    // Validate phone number
    private validatePhoneNumber(phoneNumber: string): void {
        const dto = plainToClass(VerificationDto, { phoneNumber });
        const errors = validateSync(dto);

        if (errors.length > 0) {
            const errorMessages = errors
                .map((err) => Object.values(err.constraints || {}).join(', '))
                .join('; ');

            throw new PhoneVerificationException(errorMessages);
        }
    }

    // Validate verification code
    private validateVerificationCode(phoneNumber: string, code: string): void {
        this.validatePhoneNumber(phoneNumber);

        if (!code || code.trim().length === 0) {
            throw new PhoneVerificationException(
                'Verification code is required',
            );
        }

        if (code.length < 4 || code.length > 10) {
            throw new PhoneVerificationException(
                'Invalid verification code length',
            );
        }
    }

    async sendVerification(
        phoneNumber: string,
    ): Promise<BasicOperationsResponse> {
        // Validate input
        this.validatePhoneNumber(phoneNumber);

        const verificationResult =
            await this.twilioService.sendSmsVerification(phoneNumber);

        if (!verificationResult.isSuccess) {
            this.logger.error(
                `Unexpected error in sendVerification: ${verificationResult.error.message}`,
                verificationResult.error.stack,
            );

            throw new PhoneVerificationException(
                VerificationMessages.SEND_FAILED,
            );
        }

        return {
            isSuccessful: true,
            message: VerificationMessages.SEND_SUCCESS,
        };
    }

    async verifyCode(
        verificationCode: VerificationCodeDto,
    ): Promise<BasicOperationsResponse> {
        const { phoneNumber, code } = verificationCode;
        // Validate inputs
        this.validateVerificationCode(phoneNumber, code);

        const verificationResult = await this.twilioService.verifyCode(
            phoneNumber,
            code,
        );

        if (!verificationResult.isSuccess) {
            this.logger.error(
                `Verification check failed: ${verificationResult.error.message}`,
                verificationResult.error.stack,
            );
            throw new PhoneVerificationException(
                VerificationMessages.VERIFY_FAILED,
            );
        }

        return {
            isSuccessful: true,
            message: VerificationMessages.VERIFY_SUCCESS,
        };
    }
}
