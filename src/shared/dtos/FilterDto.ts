import { Type } from 'class-transformer';
import { <PERSON><PERSON>rray, IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';

import { FilterByType, FilterType } from '../../common/constants/types';
import { IFilter } from '../../interfaces/IHttpQuery';

export class FilterDto implements IFilter {
    @IsEnum(FilterType)
    type: FilterType;

    @IsEnum(FilterByType)
    by: FilterByType;

    @IsOptional()
    @IsString()
    value?: string;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    values?: string[];

    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    min?: number;

    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    max?: number;
}
