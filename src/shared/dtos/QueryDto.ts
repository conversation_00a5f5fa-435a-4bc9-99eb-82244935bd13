import { Type } from 'class-transformer';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';

import { IHttpQuery } from '../../interfaces/IHttpQuery';
import { FilterDto } from './FilterDto';
import { PaginationDto } from './PaginationDto';
import { SearchDto } from './SearchDto';
import { SortDto } from './SortDto';

export class QueryDto implements IHttpQuery {
    @IsOptional()
    @ValidateNested()
    @Type(() => SearchDto)
    search?: SearchDto;

    @IsOptional()
    @ValidateNested()
    @Type(() => SortDto)
    sort?: SortDto;

    @IsOptional()
    @ValidateNested()
    @Type(() => PaginationDto)
    pagination?: PaginationDto;

    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => FilterDto)
    filters?: FilterDto[];
}
