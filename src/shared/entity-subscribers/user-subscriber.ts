import * as bcrypt from 'bcrypt';
import {
    EntitySubscriberInterface,
    EventSubscriber,
    InsertEvent,
} from 'typeorm';

import { UserEntity } from '../../modules/users/entities/user.entity';

@EventSubscriber()
export class UserSubscriber implements EntitySubscriberInterface<UserEntity> {
    listenTo(): typeof UserEntity {
        return UserEntity;
    }
    beforeInsert(event: InsertEvent<UserEntity>): void {
        if (event.entity.password) {
            event.entity.password = bcrypt.hashSync(event.entity.password, 10);
        }
    }
}
