import { Request } from 'express';

import { SortByType, SortingType } from '../../common/constants/types';
import { IHttpQuery } from '../../interfaces/IHttpQuery';

export class QueryParser {
    static parseRequest(req: Request): IHttpQuery {
        const query: IHttpQuery = {};

        // Parse search
        if (req.query.searchBy && req.query.searchValue) {
            query.search = {
                by: req.query.searchBy as string,
                value: req.query.searchValue as string,
            };
        }

        // Parse sort
        if (req.query.sortBy && req.query.sortType) {
            query.sort = {
                by: req.query.sortBy as SortByType,
                type: req.query.sortType as SortingType,
            };
        }

        // Parse pagination
        const page = Number(req.query.page as string) || 1;
        const perPage = Number(req.query.perPage as string) || 10;
        query.pagination = { page, perPage };

        // Parse filters
        if (req.query.filters) {
            try {
                const filters = JSON.parse(req.query.filters as string);
                if (Array.isArray(filters)) {
                    query.filters = filters.map((filter) => ({
                        type: filter.type,
                        by: filter.by,
                        value: filter.value,
                        values: filter.values,
                        min: filter.min ? Number(filter.min) : undefined,
                        max: filter.max ? Number(filter.max) : undefined,
                    }));
                }
            } catch (error) {
                console.error('Error parsing filters:', error);
            }
        }

        return query;
    }
}
