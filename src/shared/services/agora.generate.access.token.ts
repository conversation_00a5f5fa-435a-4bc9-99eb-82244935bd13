import { Injectable } from '@nestjs/common';
import { RtcRole, RtcTokenBuilder } from 'agora-access-token';

import { ConfigService } from '../../config/config.service';
import { GeneratorService } from './generator.service';

@Injectable()
export class AgoraService {
    constructor(
        public configService: ConfigService,
        public generatorService: GeneratorService,
    ) {}

    getAccessToken(
        channelName: string,
        expirationTimeInSeconds: number,
    ): string {
        const agoraAppId = this.configService.AGORA_APP_ID;
        const agoraCertificate = this.configService.AGORA_CERTIFICATE;
        const currentTimestamp = Math.floor(Date.now() / 1000);
        const privilegeExpiredTs = currentTimestamp + expirationTimeInSeconds;
        const role = RtcRole.PUBLISHER;

        return RtcTokenBuilder.buildTokenWithUid(
            agoraAppId,
            agoraCertificate,
            channelName,
            0,
            role,
            privilegeExpiredTs,
        );
    }
}
