import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { I18nService } from 'nestjs-i18n';

import { ConfigService } from '../../config/config.service';

@Injectable()
export class AxiosService {
    public instance: AxiosInstance;

    public constructor(
        private readonly configService: ConfigService,
        private i18n: I18nService,
    ) {
        this.instance = axios.create({
            baseURL: this.configService.PAYMOB_BASE_URL,
            timeout: 100000,
        });
        this.initializeRequestInterceptor();
        this.initializeResponseInterceptor();
    }

    private initializeRequestInterceptor = () => {
        this.instance.interceptors.request.use((config) => config);
    };


    private initializeResponseInterceptor = () => {
        this.instance.interceptors.response.use(
            this.handleResponse,
            this.handleError,
        );
    };

    private handleResponse = (response: AxiosResponse) => response;

    protected handleError = (error: any): Promise<void> => {
        console.error(error);

        throw new HttpException(
            {
                message: error,
            },
            HttpStatus.BAD_REQUEST,
        );
    };
}
