import { ValueTransformer } from 'typeorm';

import { HelperService } from './helper';

export class ColumnNumericTransformer implements ValueTransformer {
    helperService = new HelperService();
    public to(data?: number | null): number | null {
        if (!this.helperService.isNullOrUndefined(data)) {
            return data;
        }
        return 0;
    }

    public from(data?: string | null): number | null {
        if (!this.helperService.isNullOrUndefined(data)) {
            const res = parseFloat(data);
            if (isNaN(res)) {
                return null;
            }
            return res;
        }
        return 0;
    }
}
