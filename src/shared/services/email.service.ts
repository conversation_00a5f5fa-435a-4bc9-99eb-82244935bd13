import { Injectable } from '@nestjs/common';
import { File } from 'multer';
import * as nodemailer from 'nodemailer';
import Mail from 'nodemailer/lib/mailer';

import { ConfigService } from '../../config/config.service';

@Injectable()
export class EmailService {
    constructor(public configService: ConfigService) {
    }

    async sendEmail(to: string, subject: string, html: string): Promise<void> {
        const transporter = nodemailer.createTransport({
            service: 'gmail',
            auth: {
                user: this.configService.EMAIL,
                pass: this.configService.EMAIL_PASSWORD,
            },
        });
        const mailOptions = {
            to,
            subject,
            html,
            from: this.configService.EMAIL,
        };
        await transporter.sendMail(mailOptions);
    }

    async sendEmailWithAttachment(
        to: string,
        subject: string,
        html: string,
        file: File,
    ): Promise<void> {
        const transporter = nodemailer.createTransport({
            service: 'gmail',
            auth: {
                user: this.configService.EMAIL,
                pass: this.configService.EMAIL_PASSWORD,
            },
        });
        const mailOptions: Mail.Options = {
            to,
            subject,
            html,
            from: this.configService.EMAIL,
            attachments: [
                {
                    filename: file.originalname,
                    path: file.path,
                },
            ],
        };
        await transporter.sendMail(mailOptions);
    }

    async sendEmailWithAttachmentAsBuffer(
        to: string,
        subject: string,
        html: string,
        attachments: Mail.Attachment[],
    ): Promise<void> {
        const transporter = nodemailer.createTransport({
            service: 'gmail',
            auth: {
                user: this.configService.EMAIL,
                pass: this.configService.EMAIL_PASSWORD,
            },
        });
        const mailOptions: Mail.Options = {
            to,
            subject,
            html,
            from: this.configService.EMAIL,
            attachments,
        };
        await transporter.sendMail(mailOptions);
    }
}
