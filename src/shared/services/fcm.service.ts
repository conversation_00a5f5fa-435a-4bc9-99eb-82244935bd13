import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { token } from 'morgan';

import { FirebaseTopics } from '../../common/constants/types';
import { INotificationData } from '../../interfaces/INotifcation';
import { DebugLogger } from './logger.service';

@Injectable()
export class FCMService {
    constructor(private readonly logger: DebugLogger) {}

    async sendNotificationInNormal(
        title: string,
        body: string,
        data: INotificationData,
        deviceToken: string,
    ): Promise<void> {
        const notification: admin.messaging.MessagingPayload = {
            data,
            notification: {
                title,
                body,
            },
        };
        if (deviceToken.length) {
            try {
                await admin.messaging().sendToDevice(deviceToken, notification);
                this.logger.log('Successfully sent message:');
            } catch (error) {
                this.logger.error('Error sending message:');
                this.logger.error(error.stack); // Log the whole error object
                // Specifically log the code and message if available
                if (error.code) {
                    this.logger.error('Error Code:', error.code);
                }
                if (error.message) {
                    this.logger.error('Error Message:', error.message);
                }
                throw error;
            }
        }
    }

    async sendNotificationToMultipleDevices(
        title: string,
        body: string,
        notificationData: INotificationData,
        deviceTokens: string[],
    ): Promise<void> {
        try {
            // Construct the message payload
            const message: admin.messaging.MulticastMessage = {
                notification: {
                    title,
                    body,
                },
                data: notificationData,
                tokens: deviceTokens, // Array of device tokens
            };

            // Send the message to multiple devices
            const response = await admin.messaging().sendMulticast(message);

            this.logger.log('Successfully sent message:');
            if (response.failureCount > 0) {
                const failedTokens = [];
                response.responses.forEach((resp, idx) => {
                    if (!resp.success) {
                        failedTokens.push({
                            token: deviceTokens[idx],
                            error: resp.error,
                        });
                    }
                });

                this.logger.error('List of tokens that caused failures:');
                this.logger.error(JSON.stringify(failedTokens));
            }
        } catch (error) {
            this.logger.error('Error sending message:');
            this.logger.error(error.stack); // Log the whole error object
            if (error.message) {
                this.logger.error('Error Message:', error.message);
            }
            throw error;
        }
    }

    async sendNotification(
        title: string,
        body: string,
        notificationData: INotificationData,
        deviceTokens: string[],
    ): Promise<void> {
        const results = {
            successCount: 0,
            failureCount: 0,
            responses: [],
        };

        // Process each token one by one
        for (const deviceToken of deviceTokens) {
            try {
                // Construct the message payload for a single device
                const message = {
                    notification: {
                        title,
                        body,
                    },
                    data: notificationData,
                    token: deviceToken,
                };

                // Send message to individual device
                const response = await admin.messaging().send(message);

                results.successCount++;
                results.responses.push({
                    success: true,
                    messageId: response,
                    token: deviceToken,
                });

                this.logger.log(
                    `Successfully sent message to token: ${deviceToken}`,
                );

                // Add a small delay to avoid potential rate limiting
                await new Promise((resolve) => setTimeout(resolve, 100));
            } catch (error) {
                results.failureCount++;
                results.responses.push({
                    success: false,
                    error,
                    token: deviceToken,
                });

                this.logger.error(
                    `Failed to send message to token: ${deviceToken}`,
                    error,
                );
                throw error;
            }
        }

        this.logger.log(
            `Summary: ${results.successCount} successful, ${results.failureCount} failed`,
        );
    }

    async sendNotificationToTopic(
        topics: FirebaseTopics | FirebaseTopics[],
        body: string,
        title: string,
        data?: INotificationData,
    ): Promise<void> {
        if (Array.isArray(topics)) {
            const notification: admin.messaging.Message = {
                data,
                notification: {
                    title,
                    body,
                },
                condition: '',
            };
            const condition = topics
                .map((topic) => `'${topic}' in topics`)
                .join(' || ');
            notification.condition = condition;
            await admin.messaging().send(notification);
        } else {
            const notification: admin.messaging.MessagingPayload = {
                data,
                notification: {
                    title,
                    body,
                },
            };
            await admin.messaging().sendToTopic(topics, notification);
        }
    }
}
