import { Injectable } from '@nestjs/common';
import { v1 as uuid } from 'uuid';

@Injectable()
export class GeneratorService {
    public uuid(): string {
        return uuid();
    }
    public fileName(ext: string): string {
        return this.uuid() + '.' + ext;
    }
    public getCustomLengthRandomNumber(length: number): number {
        if (length > 10) {
            length = 10;
        }
        let randomNumber = 0;
        for (let i = 0; i < length; i += 1) {
            let randomDigit = Math.floor(Math.random() * 10);
            // prevent first digit from being a zeroe except when length is one
            while (!i && !randomDigit && length) {
                randomDigit = Math.floor(Math.random() * 10);
            }
            randomNumber = randomNumber * 10 + randomDigit;
        }
        return randomNumber;
    }
}
