import { Injectable } from '@nestjs/common';

import { IHttpQuery } from '../../interfaces/IHttpQuery';

@Injectable()
export class HelperService {
    removeEmptyKeys(object: any): void {
        Object.keys(object).forEach((key) => {
            if (
                object[key] === undefined ||
                object[key] === '' ||
                Number.isNaN(object[key])
            ) {
                delete object[key];
            }
        });
    }

    extractFilterValue(httpQueryObject: IHttpQuery, field: string): string {
        for (const filter of httpQueryObject.filters) {
            if (filter.by === field) {
                return filter.value;
            }
        }
        return null;
    }
    isNullOrUndefined<T>(obj: T | null | undefined): obj is null | undefined {
        return typeof obj === 'undefined' || obj === null;
    }
}
