import { BadRequestException, Injectable } from '@nestjs/common';
import { Brackets } from 'typeorm';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';

import { AbstractEntity } from '../../common/abstract.entity';
import {
    FilterByType,
    FilterType,
    SortByType,
} from '../../common/constants/types';
import { IHttpQuery } from '../../interfaces/IHttpQuery';

/* eslint-disable complexity */
@Injectable()
export class HttpQueryService<T extends AbstractEntity> {
    buildQuery(
        query: SelectQueryBuilder<T>,
        httpQueryString: string,
    ): SelectQueryBuilder<T> {
        let httpQueryObject: IHttpQuery;
        try {
            httpQueryObject = JSON.parse(httpQueryString);
        } catch (error) {
            throw new BadRequestException('Invalid filter');
        }
        // TODO: fix filter once the doctor has en and ar names
        if (httpQueryObject.search) {
            query.andWhere('user.name ILIKE :name', {
                name: `%${httpQueryObject.search.value}%`,
            });
        }
        if (httpQueryObject.filters) {
            httpQueryObject.filters.forEach((filter) => {
                switch (filter.type) {
                    case FilterType.FIXED:
                        switch (filter.by) {
                            case FilterByType.SPECIALITY:
                                query.andWhere('specialities.id = :id', {
                                    id: parseInt(filter.value, 10),
                                });
                                break;
                            case FilterByType.GOVERNORATE:
                                query.andWhere('governorate.id = :id', {
                                    id: parseInt(filter.value, 10),
                                });
                                break;
                            case FilterByType.CITY:
                                query.andWhere('city.id = :id', {
                                    id: parseInt(filter.value, 10),
                                });
                                break;
                            case FilterByType.GRADE:
                                query.andWhere('grade.id = :id', {
                                    id: parseInt(filter.value, 10),
                                });
                                break;
                            case FilterByType.SERVICE_PROVIDER:
                                query.andWhere('serviceProviders.id = :id', {
                                    id: parseInt(filter.value, 10),
                                });
                                break;
                            case FilterByType.EMAIL:
                                query.andWhere('user.email = :email', {
                                    email: filter.value,
                                });
                                break;
                            case FilterByType.GENDER:
                                query.andWhere('user.gender = :gender', {
                                    gender: filter.value,
                                });
                        }
                        break;
                    case FilterType.RANGE:
                        switch (filter.by) {
                            case FilterByType.RATING:
                                query.andWhere(
                                    'user.averageRating between :min AND :max',
                                    {
                                        min: filter.min,
                                        max: filter.max,
                                    },
                                );
                                break;
                            case FilterByType.FEES:
                                query.andWhere(
                                    'clinics.fees between :min AND :max',
                                    {
                                        min: filter.min,
                                        max: filter.max,
                                    },
                                );
                        }
                        break;
                    case FilterType.MULTIPLE:
                        switch (filter.by) {
                            case FilterByType.LANGUAGES:
                                query.andWhere(
                                    new Brackets((nestedQuery) => {
                                        filter.values.forEach((op) => {
                                            nestedQuery.orWhere(
                                                'doctor.languages ILIKE :languages',
                                                {
                                                    languages: `%${op}%`,
                                                },
                                            );
                                        });
                                    }),
                                );
                        }
                }
            });
        }

        if (httpQueryObject.sort) {
            switch (httpQueryObject.sort.by) {
                case SortByType.RATING:
                    query.orderBy(
                        'user.averageRating',
                        httpQueryObject.sort.type,
                    );
                    break;
                case SortByType.POPULARITY:
                    query.orderBy(
                        'user.totalConsultations',
                        httpQueryObject.sort.type,
                    );
                    break;
                case SortByType.FEES:
                    query.orderBy({
                        'doctor.onlineConsultationFees':
                            httpQueryObject.sort.type,
                        'doctor.homeVisitFees': httpQueryObject.sort.type,
                        'clinics.fees': httpQueryObject.sort.type,
                    });
            }
        }

        return query;
    }
}
