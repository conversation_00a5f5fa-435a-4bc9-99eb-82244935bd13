import { Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as moment from 'moment';

const enum DebugLevel {
    LOG = 'LOG',
    DEBUG = 'DEBUG',
    VERBOSE = 'VERBOSE',
    ERROR = 'ERROR',
    WARN = 'WARN',
}

export class <PERSON>bug<PERSON>og<PERSON> extends Logger {
    private logStream: fs.WriteStream;
    private level: DebugLevel;

    constructor(context?: string, isTimestampEnabled?: boolean) {
        super(context, { timestamp: isTimestampEnabled });
        this.logStream = fs.createWriteStream('debug.log', {
            flags: 'a',
        });
    }

    warn(message: any, context?: string): void {
        super.warn(message, context);
        this.modifyLevel(DebugLevel.WARN);
        this.write(message, context);
    }

    debug(message: any, context?: string): void {
        super.debug(message, context);
        this.modifyLevel(DebugLevel.DEBUG);
        this.write(message, context);
    }

    verbose(message: any, context?: string): void {
        super.verbose(message, context);
        this.modifyLevel(DebugLevel.VERBOSE);
        this.write(message, context);
    }

    error(message: any, trace?: string, context?: string): void {
        super.error(message, trace, context);
        this.modifyLevel(DebugLevel.ERROR);
        this.write(message, context);
    }

    log(message: any, context?: string): void {
        super.log(message, context);
        this.modifyLevel(DebugLevel.LOG);
        this.write(message, context);
    }

    private modifyLevel(level: DebugLevel) {
        this.level = level;
    }

    private write(message: any, context?: string) {
        const formattedMessage = String(message);

        const header = `[${this.level}]:[${moment().format('YYYY-MM-DD')}]`;

        const msg = context
            ? `${header}[from:${context}]` + formattedMessage
            : header + formattedMessage;
        this.logStream.write(`${msg}\n`);

        // eslint-disable-next-line no-restricted-syntax
        console.log(message);
    }
}
