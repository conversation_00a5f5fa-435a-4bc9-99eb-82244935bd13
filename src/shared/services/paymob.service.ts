import { Injectable } from '@nestjs/common';
import * as moment from 'moment';
import * as crypto from 'crypto';

import { ConfigService } from '../../config/config.service';
import { AxiosService } from './axios.service';
import { GeneratorService } from './generator.service';
import { Result, tryCatch } from '../../utils/helpers';
import { AxiosError, AxiosResponse } from 'axios';
import { PaymentStatus } from '../../common/constants/status';
import {
    IPaymobTransactionCallBack,
    TransactionStatus,
} from '../../modules/payments/payment.enum';

export interface IPaymobCallbackResponse {
    id: number;
    amountInCents: number;
    integrationId: number;
    isPending: boolean;
    isRefunded: boolean;
    isSucceeded: boolean;
    paymobOrderId: string;
    transactionResponseCode: string;
    status: TransactionStatus;
}

@Injectable()
export class PaymobService {
    constructor(
        public configService: ConfigService,
        public generatorService: GeneratorService,
        public axiosService: AxiosService,
    ) {}

    accessToken: string | PromiseLike<string>;
    accessTokenExpirationTime: moment.MomentInput;

    async auth(): Promise<string> {
        const isAccessTokenExpired = moment().isAfter(
            this.accessTokenExpirationTime,
        );
        if (this.accessToken && !isAccessTokenExpired) {
            return this.accessToken;
        }

        const response: any = await this.axiosService.instance.post(
            'auth/tokens',
            {
                api_key: this.configService.PAYMOB_API_KEY,
            },
        );
        if (!response.data.token) {
            throw new Error('No token from response of zoho get accessToken');
        }

        this.accessToken = response.data.token;
        this.accessTokenExpirationTime = moment().add(55, 'minutes');

        return response.data.token;
    }

    async createOrder(amount: string, items: any[]): Promise<string> {
        const authToken = await this.auth();
        const response: any = await this.axiosService.instance.post(
            'ecommerce/orders',
            {
                items,
                auth_token: authToken,
                delivery_needed: 'false',
                amount_cents: amount,
                currency: 'EGP',
            },
        );
        return response.data.id;
    }

    async getPaymentKey(
        orderId: string,
        amount: string,
        email: string,
        name: string,
        phone: string,
    ): Promise<string> {
        const authToken = await this.auth();
        const response: any = await this.axiosService.instance.post(
            'acceptance/payment_keys',
            {
                auth_token: authToken,
                amount_cents: amount,
                expiration: 3600,
                order_id: orderId,
                billing_data: {
                    email,
                    apartment: 'NA',
                    floor: 'NA',
                    first_name: name,
                    street: 'NA',
                    building: 'NA',
                    phone_number: phone,
                    shipping_method: 'NA',
                    postal_code: 'NA',
                    city: 'NA',
                    country: 'NA',
                    last_name: name,
                    state: 'NA',
                },
                currency: 'EGP',
                integration_id: this.configService.PAYMOB_INTEGRATIONID,
                lock_order_when_paid: 'false',
            },
        );
        return response.data.token;
    }

    /**
     * Generates a payment URL using the provided payment token
     * @param paymentToken The payment token from getPaymentKey
     * @returns Payment URL to redirect the user to
     */
    generateUrl(paymentToken: string): string {
        const iFrame = this.configService.PAYMOB_IFRAME;
        return `https://accept.paymobsolutions.com/api/acceptance/iframes/${iFrame}?payment_token=${paymentToken}`;
    }

    /**
     * Refund a transaction
     * @param amount Amount to refund in cents
     * @param transactionId Transaction ID to refund
     * @returns Refund response data
     */
    async refund(
        amount: string,
        transactionId: string,
    ): Promise<Result<IPaymobTransactionCallBack>> {
        const authToken = await this.auth();

        const responseResult = await tryCatch<AxiosResponse, AxiosError>(
            this.axiosService.instance.post('acceptance/void_refund/refund', {
                auth_token: authToken,
                amount_cents: amount,
                transaction_id: transactionId,
            }),
        );

        if (!responseResult.isSuccess) {
            return {
                isSuccess: false,
                error: new Error(
                    `Refund failed: ${responseResult.error.message}`,
                ),
            };
        }

        return {
            isSuccess: responseResult.isSuccess,
            value: {
                id: responseResult.value.data.id,
                amountInCents: responseResult.value.data.amount_cents,
                integrationId: responseResult.value.data.integration_id,
                isPending: responseResult.value.data.pending,
                isRefunded: responseResult.value.data.is_refunded,
                isSucceeded: responseResult.value.data.success,
                paymobOrderId: responseResult.value.data.order.id,
                transactionResponseCode:
                    responseResult.value.data.data.txn_response_code,
                data: responseResult.value.data,
            },
        };
    }

    async getTransaction(transactionId: string): Promise<any> {
        const authToken = await this.auth();

        const responseResult = await tryCatch(
            this.axiosService.instance.get(
                `acceptance/transactions/${transactionId}`,
                {
                    headers: {
                        Authorization: authToken,
                    },
                },
            ),
        );

        if (!responseResult.isSuccess) {
            throw new Error(
                `Get transaction failed: ${responseResult.error.message}`,
            );
        }

        return responseResult.value.data;
    }

    async payWithSavedToken(
        paymentToken: string,
        cardToken: string,
    ): Promise<Result<IPaymobTransactionCallBack, Error>> {
        const authToken = await this.auth();

        const responseResult = await tryCatch<AxiosResponse, AxiosError>(
            this.axiosService.instance.post(
                'acceptance/payments/pay',
                {
                    source: {
                        identifier: cardToken,
                        subtype: 'TOKEN',
                    },
                    payment_token: paymentToken,
                },
                {
                    headers: {
                        Authorization: authToken,
                    },
                },
            ),
        );

        if (!responseResult.isSuccess) {
            return {
                isSuccess: false,
                error: new Error(
                    `Pay with saved token failed: ${responseResult.error.message}`,
                ),
            };
        }

        return {
            isSuccess: true,
            value: {
                id: responseResult.value.data.id,
                amountInCents: responseResult.value.data.amount_cents,
                integrationId: responseResult.value.data.integration_id,
                isPending: responseResult.value.data.pending,
                isRefunded: responseResult.value.data.is_refunded,
                isSucceeded: responseResult.value.data.success,
                paymobOrderId: responseResult.value.data.order.id,
                transactionResponseCode:
                    responseResult.value.data.data.txn_response_code,
                status: TransactionStatus.SUCCEEDED,
                data: responseResult.value.data,
            },
        };
    }
}
