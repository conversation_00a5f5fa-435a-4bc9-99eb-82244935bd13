import { Injectable } from '@nestjs/common';
import * as twilio from 'twilio';
import { VerificationInstance } from 'twilio/lib/rest/verify/v2/service/verification';
import { VerificationCheckInstance } from 'twilio/lib/rest/verify/v2/service/verificationCheck';

import { ConfigService } from '../../config/config.service';

import { DebugLogger } from './logger.service';
import { Result, tryCatch } from '../../utils/helpers';

@Injectable()
export class TwilioService {
    private readonly client: twilio.Twilio;

    constructor(
        private readonly configService: ConfigService,
        private readonly logger: DebugLogger,
    ) {
        try {
            this.client = this.initializeTwilioClient();
        } catch (error) {
            this.logger.error('Failed to initialize Twilio client', error);
        }
    }

    private initializeTwilioClient(): twilio.Twilio {
        const { TWILIO_SID, TWILIO_AUTH_TOKEN } = this.configService;

        if (!TWILIO_SID || !TWILIO_AUTH_TOKEN) {
            throw new Error('Twilio credentials are missing');
        }

        return twilio(TWILIO_SID, TWILIO_AUTH_TOKEN);
    }

    async sendSmsVerification(
        phoneNumber: string,
    ): Promise<Result<VerificationInstance>> {
        const verificationResult = await tryCatch(
            this.client.verify.v2
                .services(this.configService.TWILIO_VERIFY_SID)
                .verifications.create({
                    to: phoneNumber,
                    channel: 'sms',
                }),
        );

        if (!verificationResult.isSuccess) {
            this.logger.error(
                `SMS verification failed ${verificationResult.error.message}`,
            );
            return {
                isSuccess: false,
                error: verificationResult.error,
            };
        }

        return {
            isSuccess: true,
            value: verificationResult.value,
        };
    }

    async verifyCode(
        phoneNumber: string,
        verificationCode: string,
    ): Promise<Result<VerificationCheckInstance, Error>> {
        const verificationCheck = await tryCatch(
            this.client.verify.v2
                .services(this.configService.TWILIO_VERIFY_SID)
                .verificationChecks.create({
                    to: phoneNumber,
                    code: verificationCode,
                }),
        );

        if (!verificationCheck.isSuccess) {
            this.logger.error(
                `Verification check failed ${verificationCheck.error.message}`,
            );

            return {
                isSuccess: false,
                error: verificationCheck.error,
            };
        }

        return {
            isSuccess: verificationCheck.isSuccess,
            value: verificationCheck.value,
        };
    }
}
