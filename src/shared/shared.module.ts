import { HttpModule } from '@nestjs/axios';
import { Global, Module } from '@nestjs/common';

import { ConfigModule } from '../config/config.module';
import { S3Service } from '../modules/cloud-providers/aws/s3/s3.service';
import { AgoraService } from './services/agora.generate.access.token';
import { AxiosService } from './services/axios.service';
import { DataDownloadService } from './services/data-download.service';
import { EmailService } from './services/email.service';
import { FCMService } from './services/fcm.service';
import { GeneratorService } from './services/generator.service';
import { HelperService } from './services/helper';
import { DebugLogger } from './services/logger.service';
import { PaymobService } from './services/paymob.service';
import { TwilioService } from './services/twilio.service';
import { ValidatorService } from './services/validator.service';

const providers = [
    ValidatorService,
    AgoraService,
    GeneratorService,
    HelperService,
    FCMService,
    PaymobService,
    AxiosService,
    EmailService,
    DataDownloadService,
    DebugLogger,
    S3Service,
    TwilioService,
];

@Global()
@Module({
    providers,
    imports: [HttpModule, ConfigModule],
    exports: [...providers, HttpModule],
})
export class SharedModule { }
