import axios from 'axios';
import * as fs from 'fs';
import * as jspdf from 'jspdf';
import { jsPDF } from 'jspdf';

enum OutputType {
    SAVE = 'save', // save pdf as a file
    DATA_URI_STRING = 'datauristring', // returns the data uri string
    DATA_URI = 'datauri', // opens the data uri in current window
    DATA_URL_NEW_WINDOW = 'dataurlnewwindow', // opens the data uri in new window
    BLOB = 'blob', // return blob format of the doc,
    ARRAY_BUFFER = 'arraybuffer', // return ArrayBuffer format
}

interface ILogo {
    src: string;
    type: string;
    width: number;
    height: number;
    margin: {
        top: number;
        left: number;
    };
}

interface IStyleheader {
    title: string;
    style?: {
        width: number;
    };
}

interface IStamp {
    type: string;
    inAllPages: boolean;
    src: string;
    width: number;
    height: number;
    margin: {
        top: number;
        left: number;
    };
}

interface IBusiness {
    name: string;
    phone: string;
    email: string;
    website: string;
}

interface IContact {
    label: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    otherInfo: string;
}

interface IInvoice {
    label: string;
    num: number;
    invDate: string;
    invGenDate: string;
    headerBorder: boolean;
    tableBodyBorder: boolean;
    header: IStyleheader[];
    table: string[][];
    invDescLabel: string;
    invDesc: string;
    additionalRows: {
        col1: string;
        col2: string;
        col3: string;
        style: {
            fontSize: number;
        };
    }[];
}

interface IFooter {
    text: string;
    address: string;
    taxId: string;
}

interface IInvoiceMetaData extends jspdf.TextOptionsLight {
    xInvoiceDescription: number;
    margin: number;
}

interface IJsPDFInvoiceTemplateProps {
    outputType?: OutputType;
    returnJsPDFDocObject?: boolean;
    fileName?: string;
    direction: 'rtl' | 'ltr';
    orientationLandscape?: boolean;
    fontPath?: string;
    compress?: boolean;
    logo?: ILogo;
    stamp?: IStamp;
    business?: IBusiness;
    contact?: IContact;
    invoice?: IInvoice;
    footer?: IFooter;
    pageEnable?: boolean;
    pageLabel?: string;
}

interface IReturnType {
    pagesNumber: number;
    jsPDFDocObject?: jspdf.jsPDF;
    blob?: Blob;
    dataUriString?: any;
    arrayBuffer?: ArrayBuffer;
}

function convertFileToBase64(filePath: string) {
    const bytes = fs.readFileSync(filePath);

    return Buffer.from(bytes).toString('base64');
}

function loadFont(path: string) {
    const font = convertFileToBase64(path);

    return {
        name: 'Amiri-Regular.ttf',
        fontType: 'Amiri',
        mode: 'normal',
        formData: font,
    };
}

async function getImage(url: string) {
    const response = await axios.get(url, {
        responseType: 'arraybuffer', // Ensure response is treated as binary data
    });

    // Convert binary data to base64
    return Buffer.from(response.data, 'binary').toString('base64');
}

// eslint-disable-next-line complexity
async function jsPDFInvoiceTemplate(
    props: IJsPDFInvoiceTemplateProps,
): Promise<IReturnType> {
    const param: IJsPDFInvoiceTemplateProps = {
        outputType: props.outputType || OutputType.SAVE,
        returnJsPDFDocObject: props.returnJsPDFDocObject || false,
        fileName: props.fileName || '',
        fontPath: props.fontPath || '',
        orientationLandscape: props.orientationLandscape || false,
        compress: props.compress || false,
        direction: props.direction || 'ltr',
        logo: {
            src: props.logo?.src || '',
            type: props.logo?.type || '',
            width: props.logo?.width || 0,
            height: props.logo?.height || 0,
            margin: {
                top: props.logo?.margin?.top || 0,
                left: props.logo?.margin?.left || 0,
            },
        },
        stamp: {
            inAllPages: props.stamp?.inAllPages || false,
            type: props.stamp?.type || '',
            src: props.stamp?.src || '',
            width: props.stamp?.width || 0,
            height: props.stamp?.height || 0,
            margin: {
                top: props.stamp?.margin?.top || 0,
                left: props.stamp?.margin?.left || 0,
            },
        },
        business: {
            name: props.business?.name || '',
            phone: props.business?.phone || '',
            email: props.business?.email || '',
            website: props.business?.website || '',
        },
        contact: {
            label: props.contact?.label || '',
            name: props.contact?.name || '',
            address: props.contact?.address || '',
            phone: props.contact?.phone || '',
            email: props.contact?.email || '',
            otherInfo: props.contact?.otherInfo || '',
        },
        invoice: {
            label: props.invoice?.label || '',
            num: props.invoice?.num || 0,
            invDate: props.invoice?.invDate || '',
            invGenDate: props.invoice?.invGenDate || '',
            headerBorder: props.invoice?.headerBorder || false,
            tableBodyBorder: props.invoice?.tableBodyBorder || false,
            header: props.invoice?.header || [],
            table: props.invoice?.table || [],
            invDescLabel: props.invoice?.invDescLabel || '',
            invDesc: props.invoice?.invDesc || '',
            additionalRows:
                props.invoice?.additionalRows?.map((x) => ({
                    col1: x?.col1 || '',
                    col2: x?.col2 || '',
                    col3: x?.col3 || '',
                    style: {
                        fontSize: x?.style?.fontSize || 12,
                    },
                })) || [],
        },
        footer: {
            text: props.footer?.text || '',
            address: props.footer?.address || '',
            taxId: props.footer?.taxId || '',
        },
        pageEnable: props.pageEnable || false,
        pageLabel: props.pageLabel || 'Page',
    };

    if (param.invoice.table && param.invoice.table.length) {
        if (param.invoice.table[0].length !== param.invoice.header.length) {
            throw Error('Length of header and table column must be equal.');
        }
    }

    const doc = new jspdf.jsPDF({
        compress: !!param.compress,
        orientation: param.orientationLandscape ? 'landscape' : 'portrait',
    });

    if (param.fontPath) {
        const arabicFont = loadFont(param.fontPath);

        doc.addFileToVFS(arabicFont.name, arabicFont.formData);
        doc.addFont(arabicFont.name, arabicFont.fontType, arabicFont.mode);
        doc.setFont(arabicFont.fontType);
    }
    const splitTextAndGetHeight = (text: string, size: number) => {
        const lines = doc.splitTextToSize(text, size);
        return {
            text: lines,
            height: doc.getTextDimensions(lines).h,
        };
    };

    const docWidth = doc.internal.pageSize.width;
    const docHeight = doc.internal.pageSize.height;

    const colorBlack = '#000000';
    const colorGray = '#4d4e53';
    // starting at 15mm
    let currentHeight = 15;
    // var startPointRectPanel1 = currentHeight + 6;
    const pdfConfig = {
        headerTextSize: 20,
        labelTextSize: 12,
        fieldTextSize: 10,
        lineHeight: 6,
        subLineHeight: 4,
    };

    doc.setFontSize(pdfConfig.headerTextSize);
    doc.setTextColor(colorBlack);
    doc.text(param.business.name, docWidth - 10, currentHeight, {
        align: 'right',
    });

    doc.setFontSize(pdfConfig.fieldTextSize);

    if (param.logo.src) {
        if (param.logo.type) {
            const logoImage = await getImage(param.logo.src);
            doc.addImage(
                logoImage,
                param.logo.type,
                10 + param.logo.margin.left,
                currentHeight - 5 + param.logo.margin.top,
                Number(param.logo.width),
                Number(param.logo.height),
            );
        }
    }

    doc.setTextColor(colorGray);

    currentHeight += pdfConfig.subLineHeight;
    currentHeight += pdfConfig.subLineHeight;

    doc.text(param.business.phone, docWidth - 10, currentHeight, {
        align: 'right',
    });

    doc.setFontSize(pdfConfig.fieldTextSize);
    currentHeight += pdfConfig.subLineHeight;

    doc.text(param.business.email, docWidth - 10, currentHeight, {
        align: 'right',
    });

    currentHeight += pdfConfig.subLineHeight;

    doc.text(param.business.website, docWidth - 10, currentHeight, {
        align: 'right',
    });

    // line breaker after logo & business info
    if (param.invoice.header.length) {
        currentHeight += pdfConfig.subLineHeight;
        doc.line(10, currentHeight, docWidth - 10, currentHeight);
    }

    // Contact part
    doc.setTextColor(colorGray);
    doc.setFontSize(pdfConfig.fieldTextSize);
    currentHeight += pdfConfig.lineHeight;
    if (param.contact.label) {
        doc.text(param.contact.label, 10, currentHeight);
        currentHeight += pdfConfig.lineHeight;
    }

    doc.setTextColor(colorBlack);
    doc.setFontSize(pdfConfig.headerTextSize - 5);
    if (param.contact.name) {
        doc.text(param.contact.name, 10, currentHeight);
    }

    if (param.invoice.label && param.invoice.num) {
        doc.text(
            `${param.invoice.label} ${param.invoice.num}`,
            docWidth - 10,
            currentHeight,
            {
                align: 'right',
            },
        );
    }

    if (param.contact.name || (param.invoice.label && param.invoice.num)) {
        currentHeight += pdfConfig.subLineHeight;
    }

    doc.setTextColor(colorGray);
    doc.setFontSize(pdfConfig.fieldTextSize - 2);

    if (param.contact.address || param.invoice.invDate) {
        doc.text(param.contact.address, 10, currentHeight);
        doc.text(param.invoice.invDate, docWidth - 10, currentHeight, {
            align: 'right',
        });
        currentHeight += pdfConfig.subLineHeight;
    }

    if (param.contact.phone || param.invoice.invGenDate) {
        doc.text(param.contact.phone, 10, currentHeight);
        doc.text(param.invoice.invGenDate, docWidth - 10, currentHeight, {
            align: 'right',
        });
        currentHeight += pdfConfig.subLineHeight;
    }

    if (param.contact.email) {
        doc.text(param.contact.email, 10, currentHeight);
        currentHeight += pdfConfig.subLineHeight;
    }

    if (param.contact.otherInfo) {
        doc.text(param.contact.otherInfo, 10, currentHeight);
    } else {
        currentHeight -= pdfConfig.subLineHeight;
    }
    // end contact part
    // TABLE PART
    // var tdWidth = 31.66;
    // 10 margin left - 10 margin right
    let tdWidth =
        (doc.internal.pageSize.getWidth() - 20) / param.invoice.header.length;

    //#region TD WIDTH
    if (param.invoice.header.length > 2) {
        // add style for 2 or more columns
        const customColumnNo = param.invoice.header
            .map((x) => x?.style?.width || 0)
            .filter((x) => x > 0);
        const customWidthOfAllColumns = customColumnNo.reduce(
            (a, b) => a + b,
            0,
        );
        tdWidth =
            (doc.internal.pageSize.getWidth() - 20 - customWidthOfAllColumns) /
            (param.invoice.header.length - customColumnNo.length);
    }
    //#endregion
    //#region TABLE HEADER BORDER
    const addTableHeaderBorder = () => {
        currentHeight += 2;
        const lineHeight = 7;
        let startWidth = 0;
        for (let i = 0; i < param.invoice.header.length; i++) {
            const currentTdWidth =
                param.invoice.header[i]?.style?.width || tdWidth;
            if (i === 0) {
                doc.rect(10, currentHeight, currentTdWidth, lineHeight);
            } else {
                const previousTdWidth =
                    param.invoice.header[i - 1]?.style?.width || tdWidth;
                const widthToUse =
                    currentTdWidth === previousTdWidth
                        ? currentTdWidth
                        : previousTdWidth;
                startWidth += widthToUse;
                doc.rect(
                    startWidth + 10,
                    currentHeight,
                    currentTdWidth,
                    lineHeight,
                );
            }
        }
        currentHeight -= 2;
    };
    //#endregion
    //#region TABLE BODY BORDER
    const addTableBodyBorder = (lineHeight: number) => {
        let startWidth = 0;
        for (let i = 0; i < param.invoice.header.length; i++) {
            const currentTdWidth =
                param.invoice.header[i]?.style?.width || tdWidth;
            if (i === 0) {
                doc.rect(10, currentHeight, currentTdWidth, lineHeight);
            } else {
                const previousTdWidth =
                    param.invoice.header[i - 1]?.style?.width || tdWidth;
                const widthToUse =
                    currentTdWidth === previousTdWidth
                        ? currentTdWidth
                        : previousTdWidth;
                startWidth += widthToUse;
                doc.rect(
                    startWidth + 10,
                    currentHeight,
                    currentTdWidth,
                    lineHeight,
                );
            }
        }
    };
    //#endregion

    const [firstHeaderName, sequenceHeaderName] =
        param.direction === 'rtl' ? [80, 33] : [11, 11];

    //#region TABLE HEADER
    const addTableHeader = () => {
        if (param.invoice.headerBorder) {
            addTableHeaderBorder();
        }

        currentHeight += pdfConfig.subLineHeight;
        doc.setTextColor(colorBlack);
        doc.setFontSize(pdfConfig.fieldTextSize);
        // border color
        doc.setDrawColor(colorGray);
        currentHeight += 2;

        let startWidth = 0;
        param.invoice.header.forEach(function (row, index) {
            if (index === 0) {
                doc.text(row.title, firstHeaderName, currentHeight);
            } else {
                const currentTdWidth = row?.style?.width || tdWidth;
                const previousTdWidth =
                    param.invoice.header[index - 1]?.style?.width || tdWidth;
                const widthToUse =
                    currentTdWidth === previousTdWidth
                        ? currentTdWidth
                        : previousTdWidth;
                startWidth += widthToUse;
                doc.text(
                    row.title,
                    startWidth + sequenceHeaderName,
                    currentHeight,
                );
            }
        });

        currentHeight += pdfConfig.subLineHeight - 1;
        doc.setTextColor(colorGray);
    };
    //#endregion

    addTableHeader();
    //#region TABLE BODY
    const tableBodyLength = param.invoice.table.length;
    param.invoice.table.forEach(function (row, index) {
        doc.line(10, currentHeight, docWidth - 10, currentHeight);

        // get nax height for the current row
        const getRowsHeight = () => {
            const rowsHeight = [];
            row.forEach(function (rr, newIndex) {
                const widthToUse =
                    param.invoice.header[newIndex]?.style?.width || tdWidth;

                const item = splitTextAndGetHeight(
                    rr.toString(),
                    widthToUse - 1,
                ); // minus 1, to fix the padding issue between borders
                rowsHeight.push(item.height);
            });

            return rowsHeight;
        };

        const maxHeight = Math.max(...getRowsHeight());

        // body borders
        if (param.invoice.tableBodyBorder) {
            addTableBodyBorder(maxHeight + 1);
        }

        let startWidth = 0;
        row.forEach(function (rr, newIndex) {
            const widthToUse =
                param.invoice.header[newIndex]?.style?.width || tdWidth;
            const item = splitTextAndGetHeight(rr.toString(), widthToUse - 1); // minus 1, to fix the padding issue between borders

            if (newIndex === 0) {
                doc.text(item.text, 11, currentHeight + 4);
            } else {
                const currentTdWidth = tdWidth; // rr?.style?.width ||
                const previousTdWidth =
                    param.invoice.header[newIndex - 1]?.style?.width || tdWidth;
                const widthMustUse =
                    currentTdWidth === previousTdWidth
                        ? currentTdWidth
                        : previousTdWidth;
                startWidth += widthMustUse;
                doc.text(
                    item.text,
                    sequenceHeaderName + startWidth,
                    currentHeight + 4,
                );
            }
        });

        currentHeight += maxHeight - 4;

        // td border height
        currentHeight += 5;

        // pre-increase currentHeight to check the height based on next row
        if (index + 1 < tableBodyLength) {
            currentHeight += maxHeight;
        }

        if (
            param.orientationLandscape &&
            (currentHeight > 185 ||
                (currentHeight > 178 && doc.getNumberOfPages() > 1))
        ) {
            doc.addPage();
            currentHeight = 10;
            if (index + 1 < tableBodyLength) {
                addTableHeader();
            }
        }

        if (
            !param.orientationLandscape &&
            (currentHeight > 265 ||
                (currentHeight > 255 && doc.getNumberOfPages() > 1))
        ) {
            doc.addPage();
            currentHeight = 10;
            if (index + 1 < tableBodyLength) {
                addTableHeader();
            }
        }

        // reset the height that was increased to check the next row
        if (index + 1 < tableBodyLength && currentHeight > 30) {
            // check if new page
            currentHeight -= maxHeight;
        }
    });
    //#endregion

    const invDescSize = splitTextAndGetHeight(
        param.invoice.invDesc,
        docWidth / 2,
    ).height;
    //#region PAGE BREAKER
    const checkAndAddPageLandscape = () => {
        if (!param.orientationLandscape && currentHeight + invDescSize > 270) {
            doc.addPage();
            currentHeight = 10;
        }
    };
    const checkAndAddPageNotLandscape = (heightLimit = 173) => {
        if (
            param.orientationLandscape &&
            currentHeight + invDescSize > heightLimit
        ) {
            doc.addPage();
            currentHeight = 10;
        }
    };

    const checkAndAddPage = () => {
        checkAndAddPageNotLandscape();
        checkAndAddPageLandscape();
    };
    //#endregion

    //#region Stamp
    const addStamp = async () => {
        const addStampBase = async () => {
            // const stampImage = new Image();
            // stampImage.src = param.stamp.src;

            if (param.stamp.type) {
                const image = await getImage(param.stamp.src);
                doc.addImage(
                    image,
                    param.stamp.type,
                    10 + param.stamp.margin.left,
                    docHeight - 22 + param.stamp.margin.top,
                    Number(param.stamp.width),
                    Number(param.stamp.height),
                );
            }
        };

        if (param.stamp.src) {
            if (param.stamp.inAllPages) {
                await addStampBase();
            } else if (
                !param.stamp.inAllPages &&
                doc.getCurrentPageInfo().pageNumber === doc.getNumberOfPages()
            ) {
                await addStampBase();
            }
        }
    };
    //#endregion

    checkAndAddPage();

    doc.setTextColor(colorBlack);
    doc.setFontSize(pdfConfig.labelTextSize);
    currentHeight += pdfConfig.lineHeight;

    //#region additionalRows
    if (param.invoice.additionalRows?.length > 0) {
        //#region Line breaker before invoce total
        doc.line(docWidth / 2, currentHeight, docWidth - 10, currentHeight);
        currentHeight += pdfConfig.lineHeight;
        //#endregion

        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let i = 0; i < param.invoice.additionalRows.length; i++) {
            currentHeight += pdfConfig.lineHeight;
            doc.setFontSize(param.invoice.additionalRows[i].style.fontSize);

            doc.text(
                param.invoice.additionalRows[i].col1,
                docWidth / 1.5,
                currentHeight,
                { align: 'right' },
            );
            doc.text(
                param.invoice.additionalRows[i].col2,
                docWidth - 25,
                currentHeight,
                { align: 'right' },
            );
            doc.text(
                param.invoice.additionalRows[i].col3,
                docWidth - 10,
                currentHeight,
                { align: 'right' },
            );
            checkAndAddPage();
        }
    }
    //#endregion

    checkAndAddPage();

    doc.setTextColor(colorBlack);
    currentHeight += pdfConfig.subLineHeight;
    currentHeight += pdfConfig.subLineHeight;
    doc.setFontSize(pdfConfig.labelTextSize);

    //#region Add num of pages at the bottom
    if (doc.getNumberOfPages() > 1) {
        for (let i = 1; i <= doc.getNumberOfPages(); i++) {
            doc.setFontSize(pdfConfig.fieldTextSize - 2);
            doc.setTextColor(colorGray);

            if (param.pageEnable) {
                doc.text(param.footer.text, docWidth / 2, docHeight - 10, {
                    align: 'center',
                });
                doc.setPage(i);
                doc.text(
                    `${param.pageLabel} ${i} / ${doc.getNumberOfPages()}`,
                    docWidth - 20,
                    doc.internal.pageSize.height - 6,
                );
            }

            checkAndAddPageNotLandscape(183);
            checkAndAddPageLandscape();
            await addStamp();
        }
    }
    //#endregion
    //#region INVOICE DESCRIPTION
    const addInvoiceDesc = () => {
        const properties: IInvoiceMetaData = {
            xInvoiceDescription: param.direction === 'rtl' ? 100 : 10,
            margin: param.direction === 'rtl' ? 0 : 5,
            align: param.direction === 'rtl' ? 'right' : 'left',
        };
        doc.setFontSize(pdfConfig.labelTextSize);
        doc.setTextColor(colorBlack);

        doc.text(
            param.invoice.invDescLabel,
            properties.xInvoiceDescription,
            currentHeight,
            { align: properties.align },
        );
        currentHeight += pdfConfig.subLineHeight;
        doc.setTextColor(colorGray);
        doc.setFontSize(pdfConfig.fieldTextSize - 1);

        const lines = doc.splitTextToSize(param.invoice.invDesc, docWidth);
        // text in left half
        doc.text(
            lines,
            properties.xInvoiceDescription + properties.margin,
            currentHeight,
            { align: properties.align },
        );
        currentHeight +=
            doc.getTextDimensions(lines).h > 5
                ? doc.getTextDimensions(lines).h + 6
                : pdfConfig.lineHeight;

        return currentHeight;
    };
    addInvoiceDesc();
    //#endregion

    await addStamp();

    //#region Add num of first page at the bottom
    if (doc.getNumberOfPages() === 1 && param.pageEnable) {
        doc.setFontSize(pdfConfig.fieldTextSize - 2);
        doc.setTextColor(colorGray);
        // doc.text(param.footer.text, docWidth / 2, docHeight - 10, {
        //     align: 'center',
        // });
        doc.text(param.footer.address, docWidth - 20, docHeight - 10, {
            align: 'right',
        });
        doc.text(param.footer.taxId, docWidth - 200, docHeight - 10, {
            align: 'left',
        });
        doc.text(
            param.pageLabel + '1 / 1',
            docWidth - 20,
            doc.internal.pageSize.height - 6,
        );
    }
    //#endregion
    let returnObj: IReturnType = {
        pagesNumber: doc.getNumberOfPages(),
    };

    if (param.returnJsPDFDocObject) {
        returnObj = {
            ...returnObj,
            jsPDFDocObject: doc,
        };
    }

    switch (param.outputType) {
        case OutputType.SAVE:
            doc.save(param.fileName);
            break;

        case OutputType.BLOB:
            returnObj = {
                ...returnObj,
                blob: doc.output('blob'),
            };
            break;
        case OutputType.DATA_URI_STRING:
            returnObj = {
                ...returnObj,
                dataUriString: doc.output('datauristring', {
                    filename: param.fileName,
                }),
            };
            break;

        case OutputType.ARRAY_BUFFER:
            returnObj = {
                ...returnObj,
                arrayBuffer: doc.output('arraybuffer'),
            };
            break;
        default:
    }

    return returnObj;
}

export { OutputType, jsPDF, jsPDFInvoiceTemplate };
