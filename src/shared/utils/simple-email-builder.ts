export class SimpleEmailBuilder {
    private text = '';

    addText(text: string): SimpleEmailBuilder {
        this.text += text;
        return this;
    }

    addBreak(count = 1): SimpleEmailBuilder {
        this.text += Array(count).fill('<br/>').join('');
        return this;
    }

    addLine(line: string): SimpleEmailBuilder {
        this.addText(line);
        this.addBreak();
        return this;
    }

    get content(): string {
        return `<p>${this.text}</p>`;
    }
}
