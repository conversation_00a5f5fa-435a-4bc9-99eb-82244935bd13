/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable @typescript-eslint/tslint/config */

// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { SourceType } from '../common/constants/types';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { UserDetailsDto } from '../modules/users/dto/userDetails.dto';

declare module 'agora-access-token';

declare module 'http' {
    interface IncomingHttpHeaders {
        'source-type': SourceType;
        'device-id': string;
    }
}

declare global {
    namespace Express {
        interface User extends UserDetailsDto {
            id: number;
        }
        interface Request {
            rawBody?: Buffer;
        }
    }
}
