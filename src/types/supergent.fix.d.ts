import * as http from 'http';

declare module 'superagent' {
    interface RequestBase {
        // Add the missing overloads to match those in SARequest
        set(field: "<PERSON><PERSON>", val: string[]): this;
        set(field: http.IncomingHttpHeaders): this;
        set(field: Record<string, string>): this;
        set(field: string, val: string): this;

        set(field: any, val?: any): this;
    }
}
