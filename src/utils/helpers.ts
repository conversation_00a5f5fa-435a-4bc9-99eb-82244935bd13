/**
 * A standardized result object containing either the resolved value or the error.
 */
export type Result<T, E = Error> =
    | { isSuccess: true; value: T; error?: never }
    | { isSuccess: false; value?: never; error: E };

/**
 * Executes a promise and returns a standardized result object containing either the resolved value or the error.
 * This function never throws an error, instead returning the error as part of the result object.
 *
 * @template T The type of the resolved value
 * @template E The type of the error (defaults to Error)
 * @param {Promise<T>} promiseOrValue - The promise to execute or a function that returns a value/throws an error
 * @returns {Promise<Result<T, E>>} - A standardized result object
 */
export async function tryCatch<T, E = Error>(
    promiseOrValue: Promise<T> | (() => Promise<T> | T),
): Promise<Result<T, E>> {
    try {
        const value =
            typeof promiseOrValue === 'function'
                ? await (promiseOrValue as () => Promise<T> | T)()
                : await promiseOrValue;

        return { isSuccess: true, value: value as T };
    } catch (error) {
        return { isSuccess: false, error: error as E };
    }
}

/**
 * Synchronous version of tryCatch that works with regular functions or values.
 * This function never throws an error, instead returning the error as part of the result object.
 *
 * @template T The type of the resolved value
 * @template E The type of the error (defaults to Error)
 * @param {(() => T)} fn - The function to execute or a value
 * @returns {Result<T, E>} - A standardized result object
 */
export function tryCatchSync<T, E = Error>(fn: (() => T) | T): Result<T, E> {
    try {
        const value = typeof fn === 'function' ? (fn as () => T)() : fn;

        return { isSuccess: true, value };
    } catch (error) {
        return { isSuccess: false, error: error as E };
    }
}

// Utility functions to work with Result objects
export const Result = {
    /**
     * Creates a success result
     */
    success<T>(value: T): Result<T, never> {
        return { isSuccess: true, value };
    },

    /**
     * Creates a failure result
     */
    failure<E>(error: E): Result<never, E> {
        return { isSuccess: false, error };
    },

    /**
     * Safely unwraps a result or returns a default value
     */
    unwrapOr<T, E>(result: Result<T, E>, defaultValue: T): T {
        return result.isSuccess ? result.value : defaultValue;
    },

    /**
     * Maps a successful result to a new value
     */
    map<T, U, E>(result: Result<T, E>, fn: (value: T) => U): Result<U, E> {
        if (result.isSuccess) {
            try {
                return { isSuccess: true, value: fn(result.value) };
            } catch (error) {
                return { isSuccess: false, error: error as E };
            }
        }
        return result as unknown as Result<U, E>;
    },

    /**
     * Maps a failure result to a new error
     */
    mapError<T, E, F>(result: Result<T, E>, fn: (error: E) => F): Result<T, F> {
        if (!result.isSuccess) {
            try {
                return { isSuccess: false, error: fn(result.error) };
            } catch (error) {
                return { isSuccess: false, error: error as F };
            }
        }
        return result as unknown as Result<T, F>;
    },

    /**
     * Chains result operations
     */
    andThen<T, U, E>(
        result: Result<T, E>,
        fn: (value: T) => Result<U, E>,
    ): Result<U, E> {
        if (result.isSuccess) {
            try {
                return fn(result.value);
            } catch (error) {
                return { isSuccess: false, error: error as E };
            }
        }
        return result as unknown as Result<U, E>;
    },
};
