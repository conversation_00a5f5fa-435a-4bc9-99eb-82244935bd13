import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export function setupSwagger(app: INestApplication): void {
    const options = new DocumentBuilder()
        .setTitle('Kashf backend')
        .setDescription('Kashf API description')
        .setVersion('1.0')
        .addTag('kashf')
        .addBearerAuth({ in: 'header', type: 'http' })
        .build();

    const document = SwaggerModule.createDocument(app, options);
    SwaggerModule.setup('docs', app, document);
}
