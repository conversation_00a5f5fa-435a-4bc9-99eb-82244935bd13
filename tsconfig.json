{
    "compilerOptions": {
        "module": "commonjs",
        "declaration": false,
        "noImplicitAny": false,
        "removeComments": true,
        "noLib": false,
        "allowSyntheticDefaultImports": true,
        "emitDecoratorMetadata": true,
        "experimentalDecorators": true,
        "target": "es6",
        "sourceMap": true,
        "allowJs": true,
        "outDir": "./dist",
        "rootDir": "./src",  // Add this explicitly
        "baseUrl": "./src",
        "resolveJsonModule": true,
        "skipLibCheck": true,
        "typeRoots": [
            "./node_modules/@types",
            "./src/types"
        ],
        "paths": {
            "*": ["node_modules/*", "src/types/*"]
        }
    },
    "ts-node": {
        "transpileOnly": true,
        "require": ["tsconfig-paths/register"]
    },
    "include": [
        "src/**/*",
        "src/**/*.json"
    ],
    "exclude": [
        "node_modules",
        "dist"  // Add dist to exclude
    ]
}
