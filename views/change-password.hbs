<!DOCTYPE html>
<html>

<head>
    <title>Password Change</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f2f2f2;
        }

        .container {
            width: 300px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-top: 50px;
        }

        h2 {
            text-align: center;
            color: #333;
        }

        form {
            text-align: center;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="password"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 16px;
        }

        .error {
            color: red;
        }

        .success {
            color: green;
            font-weight: bold;
            margin-top: 10px;
        }

        .button-container {
            text-align: center;
        }

        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }
    </style>
    <script>
        function validateForm(event) {
            event.preventDefault(); // Prevent default form submission behavior
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
            const errorElement = document.getElementById('error');
            const successMessage = document.getElementById('successMessage');

            if (newPassword !== confirmNewPassword) {
                errorElement.textContent = 'Passwords do not match';
                return false;
            }

            const headers = new Headers();
            // headers.append('Authorization', `Bearer ${getParameter('token')}`);
            headers.append('Content-Type', 'application/json'); // Adjust the content type if needed

            // Prepare the request options
            const requestOptions = {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({ password: newPassword, token: getParameter('token') }), // Modify the data sent in the request
            };
            const url = `${location.protocol}//${location.host}{{action}}`;

            // Send the request with fetch
            fetch(url, requestOptions)
                .then(response => {
                    if (response.ok) {
                        setMessage(successMessage);

                        // Handle a successful response (e.g., redirect or show a success message)
                    } else {
                        // Handle errors (e.g., display an error message)
                        console.error('Error:', response.statusText);
                    }
                })
                .catch(error => {
                    console.error('Network error:', error);
                });
            event.submit();
        }

        function getParameter(p) {
            let url = window.location.search.substring(1);
            let varUrl = url.split('&');
            for (let i = 0; i < varUrl.length; i++) {
                let parameter = varUrl[i].split('=');
                if (parameter[0] === p) {
                    return parameter[1];
                }
            }
        }

        function setMessage(successMessage) {
            successMessage.textContent = 'Password successfully changed!';
            clearPassword();
        }

        function clearPassword() {
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmNewPassword').value = '';
        }
    </script>
</head>

<body>
<div class="container">
    <h2>Change Password</h2>
    <form onsubmit="validateForm(event)" action="javascript:void(0);" method="post">
        <label for="newPassword">New Password:</label>
        <input type="password" id="newPassword" name="newPassword" required>

        <label for="confirmNewPassword">Confirm New Password:</label>
        <input type="password" id="confirmNewPassword" name="confirmNewPassword" required>

        <p class="error" id="error"></p>
        <p class="success" id="successMessage"></p>

        <div class="button-container">
            <button type="submit">Change Password</button>
        </div>
    </form>
</div>
</body>

</html>
