<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Doctor Packages Upload Portal</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f6f9;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        .upload-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
        }
        .drag-drop-zone {
            border: 2px dashed #3498db;
            border-radius: 8px;
            padding: 50px;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        .drag-drop-zone.drag-over {
            background-color: #f1f8ff;
        }
        .error-message {
            color: #e74c3c;
            margin-top: 10px;
            text-align: center;
        }
        #fileInput {
            display: none;
        }
        .upload-btn {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            margin-top: 20px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .upload-btn:hover {
            background-color: #2980b9;
        }
        .upload-btn:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        #fileDetails {
            margin-top: 20px;
            text-align: center;
        }
        .progress-bar {
            width: 100%;
            height: 10px;
            background-color: #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 10px;
        }
        .progress {
            width: 0;
            height: 100%;
            background-color: #2ecc71;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
<div class="upload-container">
    <div id="dropZone" class="drag-drop-zone">
        <input type="file" id="fileInput" accept=".xlsx">
        <p>Drag and drop XLSX file here or click to select</p>
    </div>

    <button id="uploadBtn" class="upload-btn" disabled>Upload Excel File</button>

    <div id="fileDetails"></div>
    <div id="errorMessage" class="error-message"></div>
</div>

<script>
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const fileDetails = document.getElementById('fileDetails');
    const errorMessage = document.getElementById('errorMessage');
    let selectedFile = null;

    // Drag and drop event listeners
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        dropZone.classList.add('drag-over');
    }

    function unhighlight() {
        dropZone.classList.remove('drag-over');
    }

    // File selection
    dropZone.addEventListener('drop', handleDrop, false);
    dropZone.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFile, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        processFile(files[0]);
    }

    function handleFile(e) {
        processFile(e.target.files[0]);
    }

    function processFile(file) {
        // Reset previous states
        errorMessage.textContent = '';
        fileDetails.innerHTML = '';
        uploadBtn.disabled = true;

        // Validate file type
        if (!file) return;

        const fileExtension = file.name.split('.').pop().toLowerCase();
        if (fileExtension !== 'xlsx') {
            errorMessage.textContent = 'Please upload only .xlsx files';
            return;
        }

        selectedFile = file;
        uploadBtn.disabled = false;

        fileDetails.innerHTML = `
                <p>File: ${file.name}</p>
                <p>Size: ${(file.size / 1024).toFixed(2)} KB</p>
            `;
    }

    // Upload functionality
    uploadBtn.addEventListener('click', uploadFile);

    function uploadFile() {
        if (!selectedFile || selectedFile.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            errorMessage.textContent = 'Invalid file type. Please upload an .xlsx file.';
            return;
        }

        const progressBar = document.createElement('div');
        progressBar.classList.add('progress-bar');
        const progress = document.createElement('div');
        progress.classList.add('progress');
        progressBar.appendChild(progress);
        fileDetails.appendChild(progressBar);

        simulateUpload(selectedFile, progress);
    }

    function simulateUpload(file, progressElement) {
        const urlParams = new URLSearchParams(window.location.search);
        const authToken = urlParams.get('token');
        const apiEndPoint = urlParams.get('endPoint');
        upload(file,`${location.protocol}//${location.host}${apiEndPoint}`,authToken,progressElement);
    }

    function upload(file,link,authToken,progressElement){
        const xhr = new XMLHttpRequest();
        const formData = new FormData();
        formData.append('file',file);

        xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
                const percentComplete = (event.loaded / event.total) * 100;
                progressElement.style.width = `${percentComplete}%`;
            }
        });

        xhr.addEventListener('load', () => {
            if (xhr.status === 201) {
                alert(`${file.name} uploaded successfully!`);
                resetUpload();
            } else {
                errorMessage.textContent = `Error uploading file: ${xhr.statusText}`
            }
        });

        xhr.addEventListener('error', () => {
            errorMessage.textContent = `Upload error: ${xhr.statusText}`;
        });

        xhr.open('POST', link);
        xhr.setRequestHeader('Authorization', `Bearer ${authToken}`);
        xhr.withCredentials = true;
        xhr.send(formData);
    }

    function resetUpload() {
        selectedFile = null;
        uploadBtn.disabled = true;
        fileInput.value = '';
        fileDetails.innerHTML = '';
        errorMessage.textContent = '';
    }
</script>
</body>
</html>
